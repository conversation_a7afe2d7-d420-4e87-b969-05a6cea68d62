/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { IconButton, Typography } from '@mui/material';
import { MainTooltip, RemoveComponentIcon, AddComponentIcon, DownloadIcon, FilterIcon } from '@tripudiotech/styleguide';

const getIconHeader = (name) => {
    switch (name) {
        case 'Add Entity':
            return <AddComponentIcon />;
        case 'Remove Entity':
            return <RemoveComponentIcon />;
        case 'Column Filter':
            return <FilterIcon />;
        case 'Export Excel':
            return <DownloadIcon />;
    }
};

const ToolbarItem = ({
    title,
    style = {},
    onClick = () => {},
    errors = null as any,
    isLocked = false,
    active = false,
}) => {
    const tooltipErrors = errors ? (
        <>
            <Typography sx={{ fontSize: '13px' }}>{errors.message}</Typography>
            {errors.details && errors.details.length > 0 && (
                <ul>
                    {errors.details.map((error, idx) => (
                        <li key={idx}>
                            <Typography sx={{ fontSize: '12px', opacity: '0.8' }}>{error}</Typography>
                        </li>
                    ))}
                </ul>
            )}
        </>
    ) : (
        ''
    );
    const disabled = !!errors;

    return (
        <MainTooltip arrow title={tooltipErrors}>
            <span>
                <IconButton color="primary" size="small" onClick={onClick} disabled={disabled} className="actionBtn">
                    {getIconHeader(title)}
                </IconButton>
            </span>
        </MainTooltip>
    );
};

export default ToolbarItem;
