/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useMemo } from 'react';
import { LoadingOverlay, AnimatedPage, TreeList, MainTooltip } from '@tripudiotech/styleguide';
import { Typography, Box, Chip } from '@mui/material';
import Container from './Container';

import { useStore } from '../../store/store';
import { Link, Outlet, useParams } from 'react-router-dom';
import CatalogNoRowsOverlay from '../NoRowsOverlay/CatalogNoRowsOverlay';
import get from 'lodash/get';

const ClassificationRenderer = (props) => {
    return (
        <Box
            component={Link}
            to={props.title}
            style={{ display: 'flex', width: '100%', textDecoration: 'none', gap: '8px', alignItems: 'center' }}
        >
            <Typography
                sx={{
                    fontSize: '14px',
                    fontWeight: 400,
                    color: (theme) => theme.palette.glide.text.tertiary,
                }}
            >
                {props.title}
            </Typography>
            <MainTooltip
                title={`There are approximately ${get(
                    props,
                    'entities',
                    0
                )} entities classified by this classification. You might not be able to see all entities due to permission`}
                enterDelay={500}
            >
                <Chip
                    color="info"
                    size="small"
                    sx={{ height: '18px', borderRadius: '50px', padding: '0px', cursor: 'pointer' }}
                    label={get(props, 'entities', 0)}
                />
            </MainTooltip>
        </Box>
    );
};

const BrowseCatalog = () => {
    const { classification } = useParams();
    const {
        isLoading,
        isLoadingApplyingClassifications,
        isLoadingClassificationSchema,
        onFetchClassifications,
        flattenClassifications,
        classifications,
        showBrowser,
    } = useStore();

    const formatClassifications = (classifications) => {
        return classifications.map(({ name, entities, description, subTypes }) => {
            const children = formatClassifications(subTypes);
            return {
                title: name,
                entities,
                description,
                children,
                id: name,
            };
        });
    };

    const classificationTree = useMemo(() => {
        if (classifications) {
            return formatClassifications(classifications);
        }
        return [];
    }, [classifications]);

    useEffect(() => {
        onFetchClassifications();
    }, []);

    return (
        <AnimatedPage>
            {(isLoading || isLoadingApplyingClassifications || isLoadingClassificationSchema) && <LoadingOverlay />}
            <Container>
                <div className={showBrowser ? 'browsePanel' : 'browsePanel close'}>
                    <div className="pageTitle">
                        <Typography variant="h1" className="title">
                            Browse Catalog
                        </Typography>
                    </div>
                    {!isLoading && (
                        <AnimatedPage className="tree">
                            <TreeList
                                data={classificationTree}
                                defaultState={flattenClassifications}
                                itemRenderer={ClassificationRenderer}
                                selectedValue={classification}
                            />
                        </AnimatedPage>
                    )}
                </div>
                <div className="content" id="catalog-table">
                    {!classification && (
                        <Box sx={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                            <CatalogNoRowsOverlay />
                        </Box>
                    )}
                    <Outlet />
                </div>
            </Container>
        </AnimatedPage>
    );
};

export default BrowseCatalog;
