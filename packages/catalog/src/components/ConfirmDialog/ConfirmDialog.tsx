/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { useDialogStore } from '../../store/dialogStore';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { TransitionProps } from '@mui/material/transitions';
import { IconButton, Slide, Typography } from '@mui/material';
import { CloseIcon, sanitizeRichText } from '@tripudiotech/styleguide';

const Transition = React.forwardRef(function Transition(
    props: TransitionProps & {
        children: React.ReactElement;
    },
    ref: React.Ref<unknown>
) {
    return <Slide direction="up" ref={ref} {...props} />;
});

const DialogContainer = () => {
    const {
        open,
        title,
        content,
        confirmAction,
        onCloseDialog,
        onCancel,
        confirmLabel = 'Confirm',
        cancelLabel = 'Cancel',
        confirmColor = 'info',
    } = useDialogStore();

    const onSubmit = () => {
        confirmAction();
        onCloseDialog();
    };

    const onClose = () => {
        if (typeof onCancel === 'function') {
            onCancel();
            return;
        }
        onCloseDialog();
    };

    return (
        <Dialog
            open={open}
            TransitionComponent={Transition}
            keepMounted
            disableEnforceFocus
            onClose={onClose}
            aria-describedby="alert-dialog-slide-description"
            PaperProps={{
                sx: {
                    borderRadius: 0,
                },
            }}
        >
            <DialogTitle
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    p: '8px 8px 8px 24px',
                    backgroundColor: (theme) => theme.palette.glide.background.normal.tertiary,
                    color: (theme) => theme.palette.glide.text.normal.tertiary,
                }}
            >
                <Typography
                    sx={{
                        fontSize: '18px',
                        fontWeight: 600,
                    }}
                >
                    {title}
                </Typography>
                <IconButton
                    sx={{
                        alignSelf: 'flex-start',
                        color: 'inherit',
                    }}
                    onClick={onClose}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent
                sx={{
                    p: '0 24px',
                    my: '16px',
                }}
            >
                <DialogContentText
                    id="alert-dialog-slide-description"
                    dangerouslySetInnerHTML={{ __html: sanitizeRichText(content) }}
                    sx={{
                        textAlign: 'left',
                        fontSize: '14px',
                        color: (theme) => theme.palette.glide.primary,
                        maxWidth: '400px',
                    }}
                />
            </DialogContent>
            <DialogActions sx={{ alignItems: 'center', justifyContent: 'space-between', px: '24px', my: '8px' }}>
                <Button
                    sx={{ width: 160, justifyContent: 'flex-start' }}
                    onClick={onClose}
                    size="large"
                    variant="contained"
                    color="secondary"
                >
                    {cancelLabel}
                </Button>
                <Button
                    sx={{ width: 226, justifyContent: 'flex-start' }}
                    onClick={onSubmit}
                    size="large"
                    variant="contained"
                    color={confirmColor || ('info' as any)}
                >
                    {confirmLabel}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export { DialogContainer };
