const singleSpaDefaults = require('webpack-config-single-spa-react-ts');
const { rspack } = require('@rspack/core');
const { merge } = require('webpack-merge');

const path = require('path');
const dotenv = require('dotenv');

dotenv.config();

const BUILD_DIR = path.resolve(__dirname, 'dist');

module.exports = (webpackConfigEnv, argv) => {
    const orgName = 'tripudiotech';
    const defaultConfig = singleSpaDefaults({
        orgName,
        projectName: 'root-config',
        webpackConfigEnv,
        argv,
        disableHtmlGeneration: true,
    });

    const isLocal = process.env.IS_LOCAL || 'false';

    return merge(defaultConfig, {
        // modify the webpack config however you'd like to by adding to this object
        plugins: [
            new rspack.HtmlRspackPlugin({
                inject: false,
                template: process.env.ENTRY_LOCATION || 'src/index.ejs',
                templateParameters: {
                    isLocal,
                    orgName,
                },
            }),
            new rspack.DefinePlugin({
                'process.env': JSON.stringify(process.env),
            }),
            new rspack.CopyRspackPlugin({
                patterns: [
                    {
                        from: `public/assets`,
                        to: `${BUILD_DIR}/assets`,
                        noErrorOnMissing: true,
                    },
                ],
            }),
        ],
        devtool: false,
        devServer: {
            client: {
                overlay: {
                    errors: true,
                    warnings: false,
                    runtimeErrors: false,
                },
            },
        },
    });
};
