/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, IconButton } from '@mui/material';
import { MainTooltip, ListViewIcon, TileViewIcon } from '@tripudiotech/styleguide';

export enum ViewType {
    List = 'list',
    Tile = 'tile',
}

export default function ViewTypeSwitch({
    viewType,
    setViewType,
}: {
    viewType: ViewType;
    setViewType: (viewType: ViewType) => void;
}) {
    return (
        <Box
            sx={{
                display: 'flex',
                border: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                borderRadius: '4px',
            }}
        >
            <MainTooltip title="List view">
                <IconButton
                    size="small"
                    onClick={() => setViewType(ViewType.List)}
                    sx={{
                        borderRight: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                        color: (theme) =>
                            `${viewType === ViewType.List ? theme.palette.glide.text.white : 'black'} !important`,
                        backgroundColor: (theme) =>
                            `${
                                viewType === ViewType.List
                                    ? theme.palette.glide.background.normal.quarternary
                                    : 'transparent'
                            } !important`,
                        borderTopLeftRadius: '4px',
                        borderBottomLeftRadius: '4px',
                    }}
                >
                    <ListViewIcon />
                </IconButton>
            </MainTooltip>
            <MainTooltip title="Tile view">
                <IconButton
                    onClick={() => setViewType(ViewType.Tile)}
                    size="small"
                    sx={{
                        color: (theme) =>
                            `${viewType === ViewType.Tile ? theme.palette.glide.text.white : 'black'} !important`,
                        backgroundColor: (theme) =>
                            `${
                                viewType === ViewType.Tile
                                    ? theme.palette.glide.background.normal.quarternary
                                    : 'transparent'
                            } !important`,
                        borderTopRightRadius: '4px',
                        borderBottomRightRadius: '4px',
                    }}
                >
                    <TileViewIcon />
                </IconButton>
            </MainTooltip>
        </Box>
    );
}
