/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Link } from 'react-router-dom';
import { Box, Chip, Typography } from '@mui/material';
import Assignee<PERSON>ender<PERSON> from './AssigneeRenderer';
import type { EntityDetail } from '@tripudiotech/api';

export default function ProjectCard({ project }: { project: EntityDetail }) {
    return (
        <Link
            to={`/detail/${project.properties.type}/${project.id}/properties`}
            style={{ textDecoration: 'none', color: 'unset' }}
        >
            <Box
                sx={{
                    height: '148px',
                    background: '#FFFFFF',
                    borderRadius: '6px',
                    boxShadow: '0px 2px 14px 0px #33446629',
                    border: '1px solid #D5D4D4',
                    padding: '22px 20px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '8px',
                    cursor: 'pointer',
                }}
            >
                <Box sx={{ display: 'flex', gap: '4px', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Typography sx={{ fontSize: '16px', fontWeight: 500 }}>{project.properties.name}</Typography>
                    <Chip
                        size="small"
                        label={project.state?.name || ''}
                        variant={project.state?.name === 'Active' ? 'status-success' : 'status-error'}
                    />
                </Box>
                <Typography
                    sx={{
                        fontSize: '14px',
                        color: '#4B4B4B',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                    }}
                >
                    {project.properties.description}
                </Typography>

                <AssigneeRenderer value={project.relationInfo?.['relation.ACCESSOR:Agent[id,name,email,type]'] || []} />
            </Box>
        </Link>
    );
}
