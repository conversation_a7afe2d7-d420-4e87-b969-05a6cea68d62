/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { getAvatarUrl } from '@tripudiotech/api';
import { MainTooltip } from '@tripudiotech/styleguide';
import { Avatar } from '@mui/material';
function stringToColor(string: string) {
    let hash = 0;
    for (let i = 0; i < string.length; i += 1) {
        hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }
    const hue = Math.abs(hash) % 360;

    const saturation = 65;
    const lightness = 72;

    return `hsl(${hue}deg ${saturation}% ${lightness}%)`;
}
const AssigneeAvatar = ({ name, email }: { name: string; email?: string }) => {
    return (
        <MainTooltip title={email}>
            <Avatar
                sizes="small"
                sx={{ bgcolor: stringToColor(name), width: 26, height: 26, border: '1px solid white' }}
                children={`${name.split(' ')[0][0]}${name.split(' ')?.[1]?.[0] || ''}`}
                className="avatar"
                alt={name}
                src={email ? getAvatarUrl(email) : ''}
            />
        </MainTooltip>
    );
};

export default AssigneeAvatar;
