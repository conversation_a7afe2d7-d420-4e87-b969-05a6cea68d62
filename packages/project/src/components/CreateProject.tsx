/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    Autocomplete,
    Avatar,
    Box,
    Button,
    Chip,
    CircularProgress,
    ClickAwayListener,
    Grid,
    IconButton,
    InputAdornment,
    Popper,
    PopperProps,
    styled,
    Tab,
    TabProps,
    Tabs,
    TextField,
    Typography,
} from '@mui/material';
import {
    AGENT_TYPE,
    CONTENT_ROLE,
    EntityDetail,
    Method,
    SYSTEM_ENTITY_TYPE,
    SYSTEM_RELATION,
    fetch as apiFetch,
    batchRequestBody,
    buildContainsQuery,
    buildOrOperatorQuery,
    entityUrls,
    getAvatarUrl,
    workspaceUrls,
} from '@tripudiotech/api';
import { usePlant, useSchemaDetail, useSchemaTree } from '@tripudiotech/caching-store';
import {
    buildValidationSchema,
    CloseIcon,
    DRAWER_COMPONENT_NAME,
    Loading,
    LoadingOverlay,
    MainTooltip,
    notifyError,
    notifySuccess,
    ResizableDrawer,
    SearchIcon,
    sortAndGroupAttributes,
} from '@tripudiotech/styleguide';
import { Form, Formik } from 'formik';
import { Dispatch, ReactNode, SetStateAction, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import AttributeBuilder from './AttributeBuilder';
import debounce from 'lodash/debounce';
import { useProjectStore } from '../store';
import AssigneeAvatar from './AssigneeAvatar';

const StyledPopper = styled(Popper)(({ theme }) => ({
    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.12)',
    '& .MuiPaper-root': {
        borderRadius: 0,
        backgroundColor: theme.palette.glide.background.normal.white,
        boxShadow: 'none',
    },
    '& .MuiAutocomplete-listbox': {
        backgroundColor: theme.palette.glide.background.normal.white,
    },
}));
const TabItem = styled(Tab)<TabProps>(({ theme }) => ({
    fontSize: '14px',
    minHeight: '19px',
    marginRight: '2px',
    padding: '12px 40px 8px 12px',
    textTransform: 'none',
    color: theme.palette.glide.text.normal.inversePrimary,
    '&.Mui-selected': {
        color: theme.palette.glide.text.normal.inverseTertiary,
        fontWeight: 700,
    },
    '&.Mui-disabled': {
        color: '#C2C2C2',
        borderColor: '#C2C2C2',
    },
}));
const StyledTabs = styled(Tabs)(({ theme }) => ({
    marginTop: '8px',
    minHeight: '28px',
    marginBottom: '-2px',
    '& .MuiTabs-indicator': { backgroundColor: theme.palette.glide.stroke.normal.main },
}));

const AGENT_TYPE_ORDER = {
    [AGENT_TYPE.PERSON]: 1,
    [AGENT_TYPE.TEAM]: 2,
    [AGENT_TYPE.DEPARTMENT]: 3,
    [AGENT_TYPE.INTERNAL_COMPANY]: 4,
    [AGENT_TYPE.EXTERNAL_COMPANY]: 5,
};
const PopperComponent = ({
    setOptions,
    ...props
}: PopperProps & { setOptions: Dispatch<SetStateAction<EntityDetail[]>> }) => {
    const [selectedTab, setSelectedTab] = useState<string>(SYSTEM_ENTITY_TYPE.PERSON);
    const setAssignees = useProjectStore((state) => state.setAssignees);
    const [loading, setLoading] = useState(false);
    const [searchText, setSearchText] = useState('');
    const abortRef = useRef<AbortController | null>(null);
    const agentTypes = useSchemaTree((state) => {
        return Object.values(state.schemaTreeMap || {})
            .filter((schema) => schema.path.includes(SYSTEM_ENTITY_TYPE.AGENT) && !schema.abstract)
            .sort((a, b) => {
                const rankA =
                    AGENT_TYPE_ORDER[a.name] !== undefined ? AGENT_TYPE_ORDER[a.name] : Number.MAX_SAFE_INTEGER;
                const rankB =
                    AGENT_TYPE_ORDER[b.name] !== undefined ? AGENT_TYPE_ORDER[b.name] : Number.MAX_SAFE_INTEGER;

                if (rankA !== rankB) return rankA - rankB;

                const keyA = a.displayName || a.name || '';
                const keyB = b.displayName || b.name || '';
                return keyA.localeCompare(keyB);
            });
    });

    const onTabChanged = (e, value) => {
        setSearchText('');
        setSelectedTab(value);
    };

    const fetchData = useCallback(
        debounce(async (entityType, searchText) => {
            try {
                abortRef.current?.abort();
                abortRef.current = new AbortController();
                setLoading(true);
                let query;
                if (searchText) {
                    const searchQuery = [
                        buildContainsQuery('name', searchText),
                        buildContainsQuery('description', searchText),
                        buildContainsQuery('title', searchText),
                    ];
                    if (entityType === SYSTEM_ENTITY_TYPE.PERSON) {
                        searchQuery.push(buildContainsQuery('email', searchText));
                    }
                    query = JSON.stringify(buildOrOperatorQuery(searchQuery));
                }
                const {
                    data: { agents },
                } = await apiFetch({
                    ...workspaceUrls.getAgents,
                    params: {
                        workspaceName: usePlant.getState().selectedPlant?.name || '',
                        agentType: entityType,
                    },
                    qs: {
                        query,
                    },
                    signal: abortRef.current.signal,
                });
                setOptions(agents);
            } catch (err) {
                console.error(err);
            } finally {
                setLoading(false);
            }
        }, 300),
        []
    );

    useEffect(() => {
        fetchData(selectedTab, searchText);
    }, [selectedTab, searchText]);

    return (
        <StyledPopper {...props}>
            <Box sx={{ padding: '8px' }}>
                <TextField
                    autoFocus
                    size="small"
                    sx={{
                        '& input': {
                            paddingLeft: '4px !important',
                            fontSize: '14px',
                            height: '24px',
                        },
                    }}
                    fullWidth
                    placeholder="Type to search"
                    value={searchText}
                    onChange={(e) => {
                        setSearchText((e.target as any).value);
                    }}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        ),
                    }}
                />
                <StyledTabs
                    variant="scrollable"
                    value={selectedTab}
                    scrollButtons="auto"
                    onChange={onTabChanged}
                    allowScrollButtonsMobile
                >
                    {agentTypes.map((agentType) => (
                        <TabItem key={agentType.name} value={agentType.name} label={agentType.displayName} />
                    ))}
                </StyledTabs>
            </Box>
            {loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '24px' }}>
                    <CircularProgress size={48} />
                </Box>
            ) : (
                <Box sx={{ maxHeight: '200px', overflow: 'auto' }}>{props.children as ReactNode}</Box>
            )}
            <Box sx={{ display: 'flex', alignItems: 'center', padding: '12px', justifyContent: 'flex-end' }}>
                <Button variant="contained-blue" size="small" onClick={() => setAssignees([])}>
                    Clear All
                </Button>
            </Box>
        </StyledPopper>
    );
};

const AgentOption = (
    props: React.HTMLAttributes<HTMLLIElement> & { key: any },
    option: EntityDetail,
    state,
    ownerState
) => {
    const { name, email } = option.properties;
    return (
        <Box key={option.id} {...props}>
            <Typography
                sx={{
                    fontSize: '14px',
                    fontWeight: 400,
                    lineHeight: '16px',
                    display: 'flex',
                    gap: '8px',
                    alignItems: 'center',
                    padding: '4px 8px',
                }}
            >
                <AssigneeAvatar name={name} email={email} />
                {name}
            </Typography>
        </Box>
    );
};
const AgentDropdown = () => {
    const [open, setOpen] = useState(false);
    const [options, setOptions] = useState<EntityDetail[]>([]);
    const { assignees, setAssignees } = useProjectStore();
    const schemaTreeMap = useSchemaTree((state) => state.schemaTreeMap);
    const popperComponent = useMemo(
        () => (props: PopperProps) => <PopperComponent {...props} setOptions={setOptions} />,
        []
    );
    return (
        <ClickAwayListener
            onClickAway={() => {
                setOpen(false);
            }}
        >
            <Box>
                <Autocomplete
                    options={options}
                    fullWidth
                    open={open}
                    onFocus={(e) => {
                        setOpen(true);
                    }}
                    onChange={(_, newValue) => setAssignees(newValue)}
                    value={assignees}
                    multiple
                    getOptionLabel={(option) => option?.properties?.name ?? ''}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    disabled={!schemaTreeMap}
                    renderTags={(selected, getTagProps) =>
                        selected.map((option, index) => {
                            const name = option?.properties?.name ?? '';
                            const email = option?.properties?.email ?? '';
                            const type =
                                schemaTreeMap?.[option?.properties?.type]?.displayName ?? option?.properties?.type;
                            return (
                                <MainTooltip key={option.id} title={email || type}>
                                    <Chip
                                        {...getTagProps({ index })}
                                        size="small"
                                        avatar={<Avatar alt={name} src={email ? getAvatarUrl(email) : ''} />}
                                        label={name}
                                    />
                                </MainTooltip>
                            );
                        })
                    }
                    ListboxProps={{
                        sx: {
                            maxHeight: '200px',
                            '& .MuiAutocomplete-option, .MuiAutocomplete-option[aria-selected="true"]': {
                                '&:hover': {
                                    background: '#334466 !important',
                                    color: '#fff !important',
                                },
                            },
                        },
                    }}
                    renderOption={AgentOption}
                    disableCloseOnSelect
                    autoFocus={false}
                    PopperComponent={popperComponent}
                    inputValue=""
                    renderInput={(params) => (
                        <TextField
                            autoComplete="off"
                            label="Assignees"
                            placeholder={!schemaTreeMap ? 'Loading...' : ''}
                            {...params}
                            InputLabelProps={{ shrink: true }}
                            fullWidth
                            size="small"
                            InputProps={{ ...params.InputProps }}
                            inputProps={{
                                ...params.inputProps,
                                readOnly: true,
                                tabIndex: -1,
                            }}
                            sx={{
                                '& input': { caretColor: 'transparent' },
                            }}
                        />
                    )}
                />
            </Box>
        </ClickAwayListener>
    );
};

const CreateProject = ({ onRefresh }) => {
    const [assignees, setAssignees] = useProjectStore((state) => [state.assignees, state.setAssignees]);
    const [open, setOpen] = useState(false);
    const handleClose = () => {
        setOpen(false);
        setAssignees([]);
    };
    const handleOpen = () => setOpen(true);
    const formRef = useRef(null);
    const [error, setError] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [projectSchema, getSchema] = useSchemaDetail((state) => [
        state.schema[SYSTEM_ENTITY_TYPE.PROJECT],
        state.getSchema,
    ]);
    useEffect(() => {
        getSchema(SYSTEM_ENTITY_TYPE.PROJECT, true).then((schema) => {
            if (!schema) {
                setError(true);
            }
        });
    }, []);
    const [initialValues, validationSchema] = useMemo(() => {
        if (!projectSchema) {
            return [{}, null];
        }
        const {
            attributes,
            entityType: { attributeOrder },
        } = projectSchema;
        const allAttributes = Object.values(attributes)
            .filter((attr) => attr.visible && isEmpty(attr.identifier))
            .sort((a, b) => {
                if (!attributeOrder) return a.name.localeCompare(b.name);
                return attributeOrder.indexOf(a.name) - attributeOrder.indexOf(b.name);
            });
        const validationSchema = buildValidationSchema(allAttributes);
        const initialValues = Object.fromEntries(
            allAttributes.map((attribute) => {
                if (get(attribute, ['constraint', 'enumRange'], false)) {
                    return [attribute.name, attribute.defaultValue || null];
                }
                return [attribute.name, attribute.defaultValue || null];
            })
        );
        return [initialValues, validationSchema];
    }, [projectSchema]);

    const handleSubmit = async (values) => {
        try {
            setIsLoading(true);
            const createdResponse = await apiFetch({
                ...entityUrls.createEntity,
                params: {
                    entityType: SYSTEM_ENTITY_TYPE.PROJECT,
                },
                data: {
                    attributes: values,
                },
            });
            if (assignees.length > 0) {
                const params = assignees.map((assignee) => ({
                    subParams: {
                        fromEntityId: createdResponse.data.id,
                        relationType: SYSTEM_RELATION.ACCESSOR,
                        toEntityId: assignee.id,
                    },
                    body: {
                        // TODO: support selecting roles after mockup is ready
                        permissionRole: CONTENT_ROLE.VIEWER,
                    },
                }));
                const batchRequest = batchRequestBody(
                    Method.POST,
                    `/entity/:fromEntityId/:relationType/:toEntityId`,
                    params
                );
                const { data } = await apiFetch({
                    ...entityUrls.batchRequest,
                    data: batchRequest,
                });
                if (data.some((res) => !res.success || res.status != 200)) {
                    notifyError(`An error occurred while adding agents to the created project`);
                    return;
                }
            }
            onRefresh();
            notifySuccess(`Successfully created new project`);
            handleClose();
        } catch (err) {
        } finally {
            setIsLoading(false);
        }
    };
    return (
        <>
            {isLoading && <LoadingOverlay />}
            <Button
                onClick={handleOpen}
                variant="contained"
                size="small"
                className="createBtn"
                sx={{ padding: '0 24px' }}
            >
                Create Project
            </Button>
            <ResizableDrawer
                open={open}
                onClose={handleClose}
                componentName={DRAWER_COMPONENT_NAME.CREATE_PROJECT}
                defaultWidth={650}
                minWidth={475}
                disableCloseOnClickOutside
                disableEnforceFocus
            >
                <Box
                    sx={{
                        display: 'flex',
                        color: '#545454',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: '24px',
                        backgroundColor: (theme) => theme.palette.glide.background.normal.tertiary,
                    }}
                >
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            width: '100%',
                            gap: '16px',
                            position: 'relative',
                        }}
                    >
                        <Typography
                            sx={{
                                fontSize: '20px',
                                lineHeight: '150%',
                                fontWeight: 600,
                                cursor: 'default',
                                color: (theme) => theme.palette.glide.text.white,
                            }}
                        >
                            Create Project
                        </Typography>
                        <IconButton
                            onClick={handleClose}
                            sx={{
                                height: '24px',
                                width: '24px',
                                p: 0,
                                color: (theme) => theme.palette.glide.text.white,
                                marginLeft: 'auto',
                            }}
                        >
                            <CloseIcon />
                        </IconButton>
                    </Box>
                </Box>
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                        overflow: 'hidden',
                    }}
                >
                    {projectSchema ? (
                        <>
                            <Box sx={{ flexGrow: 1, p: '24px', overflowY: 'auto', pt: '8px' }}>
                                <Formik
                                    enableReinitialize
                                    initialValues={initialValues}
                                    validationSchema={validationSchema}
                                    innerRef={formRef}
                                    onSubmit={(values) => {
                                        handleSubmit(values);
                                    }}
                                >
                                    {({ values, errors, setFieldValue, setFieldTouched, ...rest }) => (
                                        <Form id="create-project" {...rest}>
                                            <Grid container spacing="12px">
                                                <Grid item xs={12}>
                                                    <AttributeBuilder
                                                        setFieldTouched={setFieldTouched}
                                                        schema={projectSchema}
                                                        setFieldValue={setFieldValue}
                                                        errors={errors}
                                                        values={values}
                                                        label="General Information"
                                                    />
                                                </Grid>
                                                <Grid item xs={12}>
                                                    <AgentDropdown />
                                                </Grid>
                                            </Grid>
                                        </Form>
                                    )}
                                </Formik>
                            </Box>
                            <Box
                                sx={{
                                    marginTop: 'auto',
                                    p: '24px',
                                    display: 'flex',
                                    width: '100%',
                                    justifyContent: 'flex-end',
                                    gap: '8px',
                                }}
                            >
                                <Button
                                    sx={{
                                        width: { xs: '120px', msFlexDirection: '160px' },
                                        justifyContent: 'flex-start',
                                    }}
                                    variant="contained"
                                    color="secondary"
                                    size="medium"
                                    onClick={handleClose}
                                >
                                    Cancel
                                </Button>
                                <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-start' }}>
                                    <Button
                                        onClick={() => formRef.current.submitForm()}
                                        className="actionBtn"
                                        variant="contained"
                                        color="primary"
                                        size="medium"
                                        sx={{ minWidth: '180px', justifyContent: 'flex-start' }}
                                    >
                                        Create
                                    </Button>
                                </Box>
                            </Box>
                        </>
                    ) : (
                        <Loading />
                    )}
                </Box>
            </ResizableDrawer>
        </>
    );
};

export default CreateProject;
