/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Accordion, AccordionDetails, AccordionSummary, Box, Grid, styled, Typography } from '@mui/material';
import { useMemo } from 'react';
import { EntitySelect, ExpandMoreIcon } from '@tripudiotech/styleguide';
import { buildFormItem, sortAndGroupAttributes } from '../utils/attributeFormBuilder';
import isEmpty from 'lodash/isEmpty';
import { AttributeType } from '@tripudiotech/api';
const StyledAccordion = styled(Accordion)(({ theme }) => ({
    boxShadow: 'none',
    border: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
    borderRadius: '0 !important',
    margin: '8px 0',
    '& .allowPointer': {
        pointerEvents: 'auto',
        color: theme.palette.glide.text.normal.inverseTertiary,
    },
    '& .summary': {
        height: '38px',
        minHeight: '38px',
        flexDirection: 'row-reverse',
        paddingLeft: '4px',
        '& .MuiAccordionSummary-content': {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
        },
        '&.Mui-expanded': {
            height: '38px',
            minHeight: '38px',
            backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
        },
    },
    '& .accordionDetails': {
        padding: '0 16px 12px 16px',
        marginTop: '8px',
    },
    '& .label': {
        marginLeft: '8px',
    },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
    width: '100%',
    fontWeight: 500,
    lineHeight: '18px',
    fontSize: '14px',
    color: theme.palette.info.main,
    margin: '16px 0 8px 0',
}));

const ErrorTypography = ({ children }) => {
    return (
        <Typography
            sx={{
                fontWeight: 400,
                fontSize: '0.75rem',
                lineHeight: 1.66,
                marginTop: '5px',
                marginLeft: '16px',
                color: '#d32f2f',
            }}
        >
            {children}
        </Typography>
    );
};
const AttributeBuilder = ({
    schema,
    label = 'Attributes',
    setFieldValue,
    setFieldTouched,
    errors,
    excludedAttributes = null,
    values = {},
}) => {
    const items = useMemo(() => {
        return sortAndGroupAttributes(schema, excludedAttributes);
    }, [schema]);
    return schema ? (
        <Box>
            <SectionTitle>{label}</SectionTitle>
            <Grid container spacing={2}>
                {items.map((item) => {
                    if (item.type === AttributeType.GROUP) {
                        return (
                            <Grid item xs={12} key={`${AttributeType.GROUP}-${item.name}`}>
                                <StyledAccordion defaultExpanded>
                                    <AccordionSummary
                                        className="summary"
                                        expandIcon={<ExpandMoreIcon className="expandIcon" />}
                                    >
                                        <Typography variant="label2-med" className="label">
                                            {item.name}
                                        </Typography>
                                    </AccordionSummary>
                                    <AccordionDetails className="accordionDetails">
                                        <Grid container spacing={2}>
                                            {item.attributes.map((attribute) =>
                                                buildFormItem({
                                                    attribute,
                                                    setFieldValue,
                                                    attributes: Object.values(schema?.attributes || {}),
                                                    values,
                                                })
                                            )}
                                        </Grid>
                                    </AccordionDetails>
                                </StyledAccordion>
                            </Grid>
                        );
                    }
                    return buildFormItem({
                        attribute: item,
                        setFieldValue,
                        attributes: Object.values(schema?.attributes || {}),
                        values,
                    });
                })}
            </Grid>
        </Box>
    ) : null;
};

export default AttributeBuilder;
