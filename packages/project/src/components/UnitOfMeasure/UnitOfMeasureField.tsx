/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useState } from 'react';
import Grid from '@mui/material/Grid';
import { TextField } from 'formik-mui';
import { Field } from 'formik';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
// @ts-ignore
import { useUnitOfMeasure } from '@tripudiotech/caching-store';
import QuantityUnit from './QuantityUnit';

const UnitOfMeasureField = (props) => {
    const { size, attribute } = props;
    const { name, displayName } = attribute;
    const { quantityKind, getQuantityKind } = useUnitOfMeasure(({ quantityKind, getQuantityKind }) => ({
        quantityKind,
        getQuantityKind,
    }));
    const groupId = get(attribute, 'unitOfMeasure.quantityKind', '');

    const getQuantityGroups = useCallback(async () => {
        getQuantityKind();
    }, []);

    useEffect(() => {
        if (isEmpty(quantityKind)) {
            getQuantityGroups();
            return;
        }
    }, [quantityKind]);

    if (!isEmpty(attribute.unitOfMeasure) && !groupId) {
        return null;
    }

    return (
        <Grid item xs={6}>
            <Field name={`${name}:unit`}>
                {({ field, form, meta }) => {
                    return (
                        <QuantityUnit
                            size={size}
                            name={name}
                            displayName={displayName}
                            groupId={groupId}
                            value={field.value}
                            onChange={(quantityUnit) => {
                                form.setFieldValue(`${name}:unit`, quantityUnit);
                            }}
                            onClose={() => {
                                form.setFieldTouched(`${name}:unit`, true);
                            }}
                            metaData={meta}
                        />
                    );
                }}
            </Field>
        </Grid>
    );
};

export default UnitOfMeasureField;
