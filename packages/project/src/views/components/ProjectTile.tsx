/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { Box, Grid } from '@mui/material';
import { LoadingOverlay } from '@tripudiotech/styleguide';
import { useVirtualizer } from '@tanstack/react-virtual';
import {
    fetch as apiFetch,
    buildContainsQuery,
    buildOrOperatorQuery,
    EntityDetail,
    entityUrls,
    SYSTEM_ENTITY_TYPE,
} from '@tripudiotech/api';
import ProjectCard from '../../components/ProjectCard';
import debounce from 'lodash/debounce';

const PAGE_SIZE = 24;

const ProjectTile = ({ searchQuery }: { searchQuery }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [data, setData] = useState<EntityDetail[]>([]);
    const [hasMore, setHasMore] = useState(true);
    const parentRef = useRef<HTMLDivElement>(null);

    const rowVirtualizer = useVirtualizer({
        count: hasMore ? data.length + 1 : data.length,
        getScrollElement: () => parentRef.current,
        estimateSize: () => 100,
        overscan: 5,
    });
    const virtualItems = rowVirtualizer.getVirtualItems();

    const loadMore = async (abortController?: AbortController, queryText?: string) => {
        if (!hasMore || isLoading) return;
        setIsLoading(true);

        try {
            const offset = data.length;
            const response = await apiFetch({
                ...entityUrls.getListEntity,
                params: { entityType: SYSTEM_ENTITY_TYPE.PROJECT },
                skipToast: true,
                qs: {
                    fields: 'relation.ACCESSOR:Agent[id,name,email,type]',
                    offset,
                    limit: PAGE_SIZE,
                    query:
                        queryText ?? searchQuery
                            ? JSON.stringify(
                                  buildOrOperatorQuery([
                                      buildContainsQuery('name', queryText ?? searchQuery),
                                      buildContainsQuery('description', queryText ?? searchQuery),
                                  ])
                              )
                            : undefined,
                },
                signal: abortController?.signal,
            });

            const newProjects = response.data.data || [];
            setData((prev) => [...prev, ...newProjects]);

            const total = response?.data?.pageInfo?.total;
            const nextCount = offset + newProjects.length;
            if (typeof total === 'number') {
                setHasMore(nextCount < total);
            } else {
                setHasMore(newProjects.length === PAGE_SIZE);
            }
        } catch (error) {
            console.error('Failed to load projects:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const refreshData = useCallback(
        debounce((controller: AbortController, queryText: string) => {
            setData([]);
            setHasMore(true);
            loadMore(controller, queryText);
        }, 300),
        []
    );

    useEffect(() => {
        const controller = new AbortController();
        refreshData(controller, searchQuery);
        return () => {
            controller.abort();
        };
    }, [searchQuery]);

    useEffect(() => {
        // Skip while list is empty so debounce controls the first load
        const last = virtualItems[virtualItems.length - 1];
        if (!last || data.length === 0) return;

        const lastVisibleIndex = last.index;
        const reachedEnd = lastVisibleIndex >= data.length - 1;
        if (hasMore && reachedEnd && !isLoading) {
            loadMore(undefined, searchQuery);
        }
    }, [virtualItems, hasMore, isLoading, data.length, searchQuery]);

    return (
        <Box id="project-tile" sx={{ height: '100%', background: '#F6F8FF' }}>
            {isLoading && <LoadingOverlay />}
            <Box
                ref={parentRef}
                sx={{
                    height: '100%',
                    overflow: 'auto',
                    flex: 1,
                    padding: '24px',
                }}
            >
                <Grid container spacing={2}>
                    {data.map((project) => (
                        <Grid item xs={12} sm={6} md={4} key={project.id}>
                            <ProjectCard project={project} />
                        </Grid>
                    ))}
                    {hasMore && (
                        // Optional loader sentinel row; keeps count consistent
                        <Grid item xs={12}>
                            <Box sx={{ height: 1 }} />
                        </Grid>
                    )}
                </Grid>
            </Box>
        </Box>
    );
};

export default ProjectTile;
