export declare enum DRAWER_COMPONENT_NAME {
    BROWSE_CATALOG = "BrowseCatalog",
    CLASSIFY = "Classify",
    RELATION_SUMMARY = "RelationSummary",
    CREATE_PALLETE = "CreateEntity",
    ADD_BOM = "AddBOM",
    ADD_CONTEXTUAL_ALTERNATE = "AddContextualAlternate",
    ADD_GLOBAL_ALTERNATE = "AddGlobalAlternate",
    GRANT_ACCESS = "GrantAccess",
    REMOVE_CONTEXTUAL_ALTERNATE = "RemoveContextualAlternate",
    REPLACE_BOM = "ReplaceBom",
    ADD_SPECIFICATION = "AddSpecification",
    CLASSIFY_ENTITY_CATALOG = "ClassifyEntityCatalog",
    ADD_EXISTING_SPECIFICATION = "ADD_EXISTING_SPECIFICATION",
    EDIT_PROPERTIES_PANEL = "EDIT_PROPERTIES_PANEL",
    ADD_RELATIONS = "ADD_RELATIONS",
    PROCESS_TASK_DETAIL = "PROCESS_TASK_DETAIL",
    ADD_NEW_DOCUMENT_REF = "ADD_NEW_DOCUMENT_REF",
    CREATE_ENTITY_RELATION = "CREATE_ENTITY_RELATION",
    CREATE_CHANGE = "CREATE_CHANGE",
    FOLDER_ACCESS = "FOLDER_ACCESS",
    GRANT_FOLDER_ACCESS = "GRANT_FOLDER_ACCESS",
    IMPORT_PROGRESS_DETAIL = "IMPORT_PROGRESS_DETAIL",
    DISCUSSION_DRAWER = "DISCUSSION_DRAWER",
    ADD_USER = "ADD_USER",
    ENTITY_CRITICAL_INFORMATION_VIEW = "ENTITY_CRITICAL_INFORMATION_VIEW",
    WORKFLOW_BUILDER = "WORKFLOW_BUILDER",
    CREATE_REPORT = "CREATE_REPORT",
    REPORT_SCHEDULE_DETAIL = "REPORT_SCHEDULE_DETAIL",
    ROLLUP_EXPLANATION = "ROLLUP_EXPLANATION",
    CREATE_PROJECT = "CREATE_PROJECT"
}
//# sourceMappingURL=DrawerConstants.d.ts.map