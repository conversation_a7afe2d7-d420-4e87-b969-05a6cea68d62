import { type SvgIconProps } from '@mui/material/SvgIcon';
export declare const MenuBackIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DigitalThreadIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ItemCheckedIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const AddTaskIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const NotFoundIcon: () => import("react/jsx-runtime").JSX.Element;
export declare const ApprovalTaskIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ApprovedTaskIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const TrashIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ReportIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const RecurrenceIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ScheduleIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const GoogleDriveIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const AddFolderIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const OpenNewTabIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DisconnectIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const GrantAccessIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CheckMarkCircleIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CloudUploadIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ShareLinkIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CloseIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DiscussionIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CopyIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const TitleIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const MoveFolderIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const FolderIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CheckedIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const UncheckedIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const AdvanceSearchIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const RefreshIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const FilterRemoveIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ExportIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const FilterEditIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ComparisonIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const SettingIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const StatusAckIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const InfoIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const InfoIconOutline: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const PlanIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ComponentIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ComponentIllustrator: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const LeafComponentIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const AssemblyIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const BurgerIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const GroupObjectIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const PlusIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const GroupIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CheckboxUncheckedIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CheckboxCheckedIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const TreeIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const AddComponentIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const NoResultIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const RemoveComponentIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const MinusIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const SendIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ExpandIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CollapseIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ReplaceIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const SwapIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const AddCAIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ExpandMoreIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DocumentIllustrator: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CenterIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DownloadIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const WarningIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ImageIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const TaskIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ListViewIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const TileViewIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ProjectIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const NotificationIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const LogOutIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const SearchIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DragIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const MailIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const UploadIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const LinkIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const GroupContracted: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const GroupExpanded: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const LockIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DeleteIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const MarkerIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const FilterIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const Increase: () => import("react/jsx-runtime").JSX.Element;
export declare const Decrease: () => import("react/jsx-runtime").JSX.Element;
export declare const ArrowRight: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ComponentLifecycle: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const RequestQuoteIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const EmptyDocument: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ArrowLeft: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ViewOffIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ViewOnIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ArrowLeftIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const EditIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DocumentIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DocumentImportIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const TransparentIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CameraIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const HandIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DataViewIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DecisionTreeIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const LayerIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const BookIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ConfigViewIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const PrivateIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const UserIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const TeamIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const DepartmentIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const InternalCompanyIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ExternalCompanyIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const NewTemplateIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ExistingTemplateIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const StarIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const BookmarkIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const BookmarkFilledIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const BookmarkOutlineIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const MoreIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const SortIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const StepCompletedIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const StepIncompleteIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const StepInProgressIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const OverflowMenuHorizontal: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const LocalFileIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const OtherSourcesIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ExcelIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const BackIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const CloseIconOutlineIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const IncreaseIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const TimerIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
export declare const ClearIcon: (props: SvgIconProps) => import("react/jsx-runtime").JSX.Element;
//# sourceMappingURL=Icons.d.ts.map