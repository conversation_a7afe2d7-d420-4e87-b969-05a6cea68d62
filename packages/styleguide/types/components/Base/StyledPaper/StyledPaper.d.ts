/// <reference types="react" />
declare const StyledPaper: import('@emotion/styled').StyledComponent<
    import('@mui/material').PaperOwnProps &
        import('@mui/material/OverridableComponent').CommonProps &
        Omit<
            Omit<
                import('react').DetailedHTMLProps<import('react').HTMLAttributes<HTMLDivElement>, HTMLDivElement>,
                'ref'
            > & {
                ref?: import('react').Ref<HTMLDivElement>;
            },
            'classes' | 'children' | 'className' | 'style' | 'sx' | 'variant' | 'elevation' | 'square'
        > &
        import('@mui/system').MUIStyledCommonProps<import('@mui/material').Theme>,
    {},
    {}
>;
export default StyledPaper;
//# sourceMappingURL=StyledPaper.d.ts.map
