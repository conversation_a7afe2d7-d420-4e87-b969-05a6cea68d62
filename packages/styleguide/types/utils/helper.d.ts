export declare const getRelationDisplayName: (relation: any) => any;
export declare const isArrayType: (type: any) => boolean;
export declare const getAgGridColumnFilterType: (type: string) => "agTextColumnFilter" | "agSetColumnFilter" | "agDateColumnFilter" | "agNumberColumnFilter";
export declare const isValidAttributeToRender: (attribute: any) => any;
export declare const sortAndGroupAttributes: (schema: any, excludedAttributes?: Record<string, any>) => any[];
//# sourceMappingURL=helper.d.ts.map