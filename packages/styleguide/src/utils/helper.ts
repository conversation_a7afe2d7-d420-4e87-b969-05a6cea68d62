/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { AttributeType, SYSTEM_ATTRIBUTE } from '@tripudiotech/api';
import get from 'lodash/get';
import startCase from 'lodash/startCase';
import toLower from 'lodash/toLower';
import isEmpty from 'lodash/isEmpty';

export const getRelationDisplayName = (relation) => {
    const relationName = get(relation, SYSTEM_ATTRIBUTE.NAME);
    const relationDisplayName = get(relation, SYSTEM_ATTRIBUTE.DISPLAY_NAME);
    return relationDisplayName || startCase(toLower(relationName));
};

export const isArrayType = (type) =>
    [
        AttributeType.DATE_ARRAY,
        AttributeType.DATE_TIME_ARRAY,
        AttributeType.FLOAT_ARRAY,
        AttributeType.INTEGER_ARRAY,
        AttributeType.STRING_ARRAY,
    ].includes(type);

export const getAgGridColumnFilterType = (
    type: string
): 'agTextColumnFilter' | 'agSetColumnFilter' | 'agDateColumnFilter' | 'agNumberColumnFilter' => {
    switch (type) {
        case AttributeType.DATE:
        case AttributeType.DATE_TIME:
            return 'agDateColumnFilter';
        case AttributeType.FLOAT:
        case AttributeType.INTEGER:
            return 'agNumberColumnFilter';
        case AttributeType.STRING:
            return 'agTextColumnFilter';
        default:
            return 'agSetColumnFilter';
    }
};
export const isValidAttributeToRender = (attribute) =>
    attribute && attribute.visible && isEmpty(attribute.identifier) && attribute.mutable;
export const sortAndGroupAttributes = (schema, excludedAttributes?: Record<string, any>) => {
    let sortedKeys = {};
    const attributeOrder = get(schema, 'entityType.attributeOrder') ?? [];
    const attributes = get(schema, 'attributes', {});
    let sortedAttributes = [];

    attributeOrder.forEach((attribute: any) => {
        const isGroup = attribute.includes(':');
        if (isGroup) {
            const [groupName, groupString] = attribute.split(':');
            const groupAttributes = groupString?.split(',') || [];
            let sortedGroupAttributes = [];
            groupAttributes.forEach((groupAttribute) => {
                const schemaAttribute = attributes[groupAttribute];
                sortedKeys[groupAttribute] = true;
                if (isValidAttributeToRender(schemaAttribute) && !excludedAttributes?.[groupAttribute]) {
                    sortedGroupAttributes.push(schemaAttribute);
                }
            });
            if (sortedGroupAttributes.length > 0) {
                sortedAttributes.push({
                    type: AttributeType.GROUP,
                    attributes: sortedGroupAttributes,
                    name: groupName,
                });
            }
        } else {
            const schemaAttribute = attributes[attribute];
            sortedKeys[attribute] = true;
            if (isValidAttributeToRender(schemaAttribute) && !excludedAttributes?.[schemaAttribute.name]) {
                sortedAttributes.push(attributes[attribute]);
            }
        }
    });
    Object.values(get(schema, 'attributes', {})).forEach((attribute: any) => {
        if (
            !sortedKeys[attribute.name] &&
            isValidAttributeToRender(attribute) &&
            !excludedAttributes?.[attribute.name]
        ) {
            sortedAttributes.push(attribute);
        }
    });
    return sortedAttributes;
};
