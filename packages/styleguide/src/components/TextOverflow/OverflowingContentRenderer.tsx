/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';

import { Dialog, DialogContent, DialogTitle, IconButton, Typography, StyledEngineProvider } from '@mui/material';
import { CloseIcon } from '../icons/Icons';
import { Themes } from '../../tripudiotech-styleguide';
import { styled, ThemeProvider } from '@mui/material/styles';
import { create } from 'zustand';

interface DialogState {
    open: boolean;
    title?: string;
    content?: React.ReactNode;
    openDialog: (title: string, content: React.ReactNode) => void;
    closeDialog: () => void;
}

export const useOverflowingCellsDialogStore = create<DialogState>((set) => ({
    open: false,
    title: undefined,
    content: undefined,
    openDialog: (title, content) => set({ open: true, title, content }),
    closeDialog: () => set({ open: false, title: undefined, content: undefined }),
}));

const StyledDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiPaper-root': {
        borderRadius: 0,
        width: '30%',
        height: 'auto',
        maxHeight: '50%',
        overflow: 'hidden',
        maxWidth: '100%',
    },
    '& .dialogHeader': {
        display: 'flex',
        justifyContent: 'space-between',
        padding: '8px 8px 8px 24px',
        height: '46px',
        alignItems: 'center',
        backgroundColor: theme.palette.glide.background.normal.tertiary,
        '& .title': {
            fontSize: '20px',
            lineHeight: '150%',
            fontWeight: 600,
            color: theme.palette.glide.text.white,
            margin: 0,
        },
        '& .subTitle': {
            color: theme.palette.glide.text.normal.tertiary,
            fontWeight: 400,
            fontSize: '12px',
        },
        '& .headerButton': {
            alignSelf: 'flex-start',
            color: theme.palette.glide.text.normal.tertiary,
        },
    },
    '& .cancelBtn': {
        width: '160px',
        justifyContent: 'flex-start',
    },
    '& .searchBtn': {
        width: '260px',
        justifyContent: 'flex-start',
    },
    '& .actions': {
        borderTop: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
    },
    '& .dialogContent': {
        margin: '24px 0',
        '& form': {
            '& .MuiPaper-root': {
                minWidth: '180px',
                width: 'max-content',
            },
        },
    },
    '& .MuiOutlinedInput-root': {
        height: 'min-content',
    },
}));

export const OverflowingContentRenderer = () => {
    const { open, title, content, closeDialog } = useOverflowingCellsDialogStore();

    return (
        <StyledEngineProvider injectFirst>
            <ThemeProvider theme={Themes.default}>
                <StyledDialog open={open} onClose={closeDialog}>
                    <DialogTitle className="dialogHeader">
                        <Typography className="title">{title}</Typography>
                        <IconButton className="headerButton" onClick={closeDialog}>
                            <CloseIcon />
                        </IconButton>
                    </DialogTitle>
                    <DialogContent className="dialogContent">{content}</DialogContent>
                </StyledDialog>
            </ThemeProvider>
        </StyledEngineProvider>
    );
};
