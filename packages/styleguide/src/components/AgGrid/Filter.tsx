/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { AttributeType, buildOrOperatorQuery, extractAndOrCriterias, getCriteriasQuery } from '@tripudiotech/api';
import isEmpty from 'lodash/isEmpty';

export const filterParams = {
    filterOptions: ['contains', 'equals', 'blank', 'notBlank'],
};

export const getFilterParamsByType = (type) => {
    switch (type) {
        case AttributeType.DATE:
        case AttributeType.DATE_TIME:
        case AttributeType.INTEGER:
        case AttributeType.FLOAT:
            return {
                filterOptions: ['equals', 'lessThan', 'lessThanOrEqual', 'greaterThan', 'greaterThanOrEqual'],
            };
        case AttributeType.LONG:
            return {
                filterOptions: ['equals'],
            };
        default:
            return {
                filterOptions: ['contains', 'equals', 'blank', 'notBlank'],
            };
    }
};

function createFilter(mapper, key, item) {
    if (item.operator) {
        const condition1 = mapper(key, item.condition1);
        const condition2 = mapper(key, item.condition2);

        return extractAndOrCriterias(item.operator, [condition1, condition2]);
    }

    return mapper(key, item);
}

export const buildSortParams = (sortModel) => {
    if (sortModel && sortModel.length > 0) {
        return {
            sort: sortModel.map((sortField) => {
                const { colId, sort } = sortField;
                // for lifecycle state and owner we pass the filter as `state.name` and `owner.name`
                // for the properties, we strip off properties from `properties.name`
                const field = colId.replace('properties.', '');
                return `${field},${sort === 'desc' ? 'DESCENDING' : 'ASCENDING'}`;
            }),
        };
    }
    return null;
};

/**
 *
 * @param key eg. relationInfo.relation.ACCESSOR:Agent[id,name,email,type]
 * @returns the relation name and all included attributes
 */
const extractRelationInfoFilter = (key: string) => {
    const prefix = 'relationInfo.relation.';
    if (!key.startsWith(prefix)) return null;

    const rest = key.slice(prefix.length).trim();
    const match = rest.match(/^([A-Za-z_]+:[A-Za-z_]+)\[([^\]]+)\]$/);
    if (!match) return null;

    const [, relationNameRaw, attributesRaw] = match;
    const relationName = relationNameRaw;
    const attributes = attributesRaw
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean);

    return { relationName, attributes };
};

export const buildQueryBasedOnFilter = (filterConditions, filterModel) => {
    if (isEmpty(filterConditions) && isEmpty(filterModel)) {
        return {};
    }

    Object.keys(filterModel).forEach(function (key) {
        const item = filterModel[key];

        if (key.includes('properties')) {
            key = key.substring(key.indexOf('.') + 1);
        }
        // Create relation filter with the format similar to this relationInfo.relation.ACCESSOR:Agent[id,name,email,type]
        if (key.startsWith('relationInfo.relation')) {
            const relationInfo = extractRelationInfoFilter(key);
            if (relationInfo) {
                const { relationName, attributes } = relationInfo;
                const subConditions = [];
                attributes.forEach((attribute) => {
                    const condition = createFilter(getCriteriasQuery, `relation.${relationName}.${attribute}`, item);
                    if (condition) {
                        subConditions.push(condition);
                    }
                });
                if (subConditions.length > 0) {
                    filterConditions.push(buildOrOperatorQuery(subConditions));
                }
            }
            return;
        }
        switch (item.filterType) {
            case 'set': {
                item.type = 'in';
                const condition = createFilter(getCriteriasQuery, key, item);
                if (condition) {
                    filterConditions.push(condition);
                }
                break;
            }
            case 'text':
            case 'number':
            case 'date': {
                const condition = createFilter(getCriteriasQuery, key, item);
                if (condition) {
                    filterConditions.push(condition);
                }
                break;
            }
            default:
                break;
        }
    });

    if (isEmpty(filterConditions)) return {};

    return extractAndOrCriterias('AND', filterConditions);
};
