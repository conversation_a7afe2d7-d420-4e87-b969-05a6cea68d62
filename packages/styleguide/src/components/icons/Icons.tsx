/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import SvgIcon, { type SvgIconProps } from '@mui/material/SvgIcon';

export const MenuBackIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon fontSize="inherit" style={{ width: 26 }} {...props}>
            <path
                d="M20 8V14C20 15.1 19.1 16 18 16H2C0.9 16 0 15.1 0 14V2C0 0.9 0.9 0 2 0H12V2H2V14H18V8H20ZM20 3C20 1.34 18.66 0 17 0C15.34 0 14 1.34 14 3C14 4.66 15.34 6 17 6C18.66 6 20 4.66 20 3ZM9.47 8.12L6.64 10.95L8.05 12.36L10.88 9.53L13 11.66V6H7.34L9.47 8.12Z"
                fill="#616872"
            />
        </SvgIcon>
    );
};

export const DigitalThreadIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M4 15H1V12H4V15ZM2 14H3V13H2V14Z" fill="inherit" />
        <path d="M9.5 15H6.5V12H9.5V15ZM7.5 14H8.5V13H7.5V14Z" fill="inherit" />
        <path d="M15 15H12V12H15V15ZM13 14H14V13H13V14Z" fill="inherit" />
        <path
            d="M8 5C7.60444 5 7.21776 4.8827 6.88886 4.66294C6.55996 4.44318 6.30362 4.13082 6.15224 3.76537C6.00087 3.39992 5.96126 2.99778 6.03843 2.60982C6.1156 2.22186 6.30608 1.86549 6.58579 1.58579C6.86549 1.30608 7.22186 1.1156 7.60982 1.03843C7.99778 0.96126 8.39991 1.00087 8.76537 1.15224C9.13082 1.30362 9.44318 1.55996 9.66294 1.88886C9.8827 2.21776 10 2.60444 10 3C9.9994 3.53025 9.7885 4.03861 9.41356 4.41356C9.03861 4.7885 8.53025 4.99941 8 5ZM8 2C7.80222 2 7.60888 2.05865 7.44443 2.16853C7.27998 2.27841 7.15181 2.43459 7.07612 2.61732C7.00043 2.80004 6.98063 3.00111 7.01921 3.19509C7.0578 3.38907 7.15304 3.56726 7.29289 3.70711C7.43275 3.84696 7.61093 3.9422 7.80491 3.98079C7.99889 4.01937 8.19996 3.99957 8.38268 3.92388C8.56541 3.84819 8.72159 3.72002 8.83147 3.55557C8.94135 3.39112 9 3.19778 9 3C8.99974 2.73487 8.89429 2.48067 8.70681 2.29319C8.51934 2.10571 8.26514 2.00027 8 2Z"
            fill="inherit"
        />
        <path
            d="M13 8H10.5C9.96975 7.99941 9.46139 7.7885 9.08644 7.41356C8.7115 7.03861 8.5006 6.53025 8.5 6H7.5C7.4994 6.53025 7.2885 7.03861 6.91356 7.41356C6.53861 7.7885 6.03025 7.99941 5.5 8H3C2.73488 8.00031 2.4807 8.10576 2.29323 8.29323C2.10576 8.4807 2.0003 8.73488 2 9V11H3V9H5.5C6.239 8.99978 6.95165 8.72541 7.5 8.23V11H8.5V8.23C9.04835 8.72541 9.761 8.99978 10.5 9H13V11H14V9C13.9997 8.73488 13.8942 8.4807 13.7068 8.29323C13.5193 8.10576 13.2651 8.00031 13 8Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ItemCheckedIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M6 8H3V9H6V8Z" fill="inherit" />
        <path d="M8 6H3V7H8V6Z" fill="inherit" />
        <path d="M8 4H3V5H8V4Z" fill="inherit" />
        <path
            d="M7 13H2V3H14V8H15V3C15 2.73478 14.8946 2.48043 14.7071 2.29289C14.5196 2.10536 14.2652 2 14 2H2C1.73478 2 1.48043 2.10536 1.29289 2.29289C1.10536 2.48043 1 2.73478 1 3V13C1 13.2652 1.10536 13.5196 1.29289 13.7071C1.48043 13.8946 1.73478 14 2 14H7V13Z"
            fill="inherit"
        />
        <path d="M11 12.795L9.705 11.5L9 12.205L11 14.205L15 10.205L14.295 9.5L11 12.795Z" fill="inherit" />
    </SvgIcon>
);
export const AddTaskIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M15.5 12H13.5V10H12.5V12H10.5V13H12.5V15H13.5V13H15.5V12Z" fill="inherit" />
        <path
            d="M12.5 2.5H11V2C10.9992 1.73502 10.8936 1.48111 10.7063 1.29374C10.5189 1.10637 10.265 1.00077 10 1H6C5.73502 1.00077 5.48111 1.10637 5.29374 1.29374C5.10637 1.48111 5.00077 1.73502 5 2V2.5H3.5C3.23502 2.50077 2.98111 2.60637 2.79374 2.79374C2.60637 2.98111 2.50077 3.23502 2.5 3.5V14C2.50077 14.265 2.60637 14.5189 2.79374 14.7063C2.98111 14.8936 3.23502 14.9992 3.5 15H8.5V14H3.5V3.5H5V5H11V3.5H12.5V8H13.5V3.5C13.4992 3.23502 13.3936 2.98111 13.2063 2.79374C13.0189 2.60637 12.765 2.50077 12.5 2.5ZM10 4H6V2H10V4Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const NotFoundIcon = () => (
    <svg width="296" height="137" viewBox="0 0 296 137" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M38.64 113V92.24H0V85.16L36.12 33.8H48.24V83.96H62.28V92.24H48.24V113H38.64ZM10.2 83.96H38.64V54.32C38.64 52.24 38.68 50.12 38.76 47.96C38.92 45.8 39.04 43.68 39.12 41.6H38.88C37.92 43.6 36.68 45.84 35.16 48.32C33.64 50.8 32.24 52.96 30.96 54.8L10.2 83.96Z"
            fill="white"
        />
        <path
            d="M271.999 113V92.24H233.359V85.16L269.479 33.8H281.599V83.96H295.639V92.24H281.599V113H271.999ZM243.559 83.96H271.999V54.32C271.999 52.24 272.039 50.12 272.119 47.96C272.279 45.8 272.399 43.68 272.479 41.6H272.239C271.279 43.6 270.039 45.84 268.519 48.32C266.999 50.8 265.599 52.96 264.319 54.8L243.559 83.96Z"
            fill="white"
        />
        <g clip-path="url(#clip0_1032_2972)">
            <path
                d="M219.579 68.4693C219.579 106.324 188.879 137 150.996 137C129.444 137 110.165 127.061 97.6394 111.477C88.1224 99.7591 82.4736 84.7891 82.4736 68.5307C82.4736 30.6762 113.174 0 150.996 0C167.267 0 182.187 5.64442 193.976 15.0927C209.571 27.6699 219.579 46.8733 219.579 68.4693Z"
                fill="#DFDFDF"
            />
            <path
                d="M209.141 58.1008C209.141 95.9552 178.441 126.57 140.558 126.57C124.287 126.57 109.367 120.926 97.5778 111.416C88.0609 99.6977 82.4121 84.7277 82.4121 68.4693C82.4735 30.6762 113.173 0 150.996 0C167.267 0 182.187 5.64442 193.975 15.0927C203.492 26.8724 209.141 41.8424 209.141 58.1008Z"
                fill="white"
            />
            <path
                d="M175.433 73.7456C175.31 75.0953 174.696 76.3838 173.775 77.304C172.854 78.2243 171.626 78.8379 170.214 78.8992L167.512 79.0833C141.479 80.6171 115.445 80.6784 89.4116 79.3287L86.7714 79.206C83.0874 79.206 79.7718 76.8746 78.5438 73.3775L77.9912 71.7823V71.721C78.0526 71.905 78.1754 72.1504 78.2368 72.3345C78.3596 72.5186 78.421 72.7026 78.6052 72.8867C78.6052 72.8867 78.6052 72.948 78.6666 72.948C78.7894 73.1321 78.9122 73.2548 79.035 73.3775C79.2192 73.5615 79.342 73.7456 79.5262 73.8683C79.649 73.991 79.8332 74.1137 79.956 74.2364C80.3244 74.4818 80.6928 74.6659 81.0612 74.8499H81.1226C81.3068 74.9113 81.5524 74.9726 81.7366 75.034C81.9208 75.0954 82.105 75.0954 82.2892 75.1567C82.4734 75.2181 82.719 75.2181 82.9032 75.2181L85.5434 75.3408C111.577 76.6905 137.61 76.5678 163.644 75.0954L166.345 74.9113C167.758 74.7886 168.986 74.2364 169.907 73.3161C170.828 72.3958 171.442 71.1688 171.564 69.7577C172.854 53.8061 172.854 37.8544 171.564 21.9028V21.5347C171.503 20.4917 171.073 19.51 170.521 18.6511C170.459 18.5898 170.398 18.4671 170.336 18.4057C170.214 18.283 170.091 18.0989 169.968 17.9762C169.293 17.3014 168.494 16.8105 167.573 16.5651L168.494 16.8105C172.424 17.8535 175.187 21.3506 175.555 25.3999C175.555 25.3999 175.555 25.3999 175.555 25.4612V25.8293C176.722 41.781 176.722 57.794 175.433 73.7456Z"
                fill="#323436"
            />
            <path
                d="M170.214 78.8992L167.512 79.0832C141.54 80.6171 115.446 80.7398 89.412 79.39L86.7718 79.2673C83.9474 79.0832 81.7371 76.8746 81.4915 74.1137V73.6229C80.2021 57.794 80.2021 41.8423 81.4915 26.0134V25.5839C81.7371 22.8231 83.9474 20.6144 86.7104 20.4303C114.525 18.7738 142.4 18.7738 170.214 20.4303C172.977 20.6144 175.187 22.8231 175.433 25.5839V25.8907C176.722 41.8423 176.722 57.7939 175.433 73.7456C175.187 76.5064 172.977 78.7151 170.214 78.8992Z"
                fill="#323436"
            />
            <path
                d="M166.284 74.9727L163.583 75.1567C137.611 76.6905 111.516 76.8132 85.4822 75.4635L82.842 75.3408C80.0176 75.1567 77.8072 72.948 77.5616 70.1872L77.5002 69.7577C76.2109 53.9288 76.2109 38.0385 77.5002 22.2096L77.5616 21.7187C77.8072 18.9579 80.0176 16.7492 82.7806 16.5651C110.595 14.9086 138.47 14.9086 166.284 16.5651C169.047 16.7492 171.258 18.9579 171.503 21.7187V22.0255C172.793 37.9771 172.793 53.9288 171.503 69.8804C171.258 72.5799 169.047 74.7886 166.284 74.9727Z"
                fill="#424446"
            />
            <path
                d="M114.279 107.367C114.156 102.888 118.086 98.0412 126.498 98.0412C134.909 98.0412 138.839 102.888 138.716 107.367H114.279Z"
                fill="#424446"
            />
            <path
                d="M112.007 53.8061C111.639 53.8061 111.332 53.6834 111.025 53.3766L97.517 39.8177C96.9644 39.2655 96.9644 38.4066 97.517 37.8544C98.0696 37.3023 98.9292 37.3023 99.4818 37.8544L113.051 51.4133C113.604 51.9655 113.604 52.8244 113.051 53.3766C112.683 53.622 112.376 53.8061 112.007 53.8061Z"
                fill="white"
            />
            <path
                d="M98.806 53.8061C98.4376 53.8061 98.1306 53.6834 97.8236 53.3766C97.271 52.8244 97.271 51.9655 97.8236 51.4133L111.393 37.8544C111.946 37.3023 112.805 37.3023 113.358 37.8544C113.91 38.4066 113.91 39.2655 113.358 39.8177L99.727 53.3766C99.4814 53.622 99.113 53.8061 98.806 53.8061Z"
                fill="white"
            />
            <path
                d="M149.953 53.8061C149.584 53.8061 149.277 53.6834 148.97 53.3766L135.462 39.8177C134.91 39.2655 134.91 38.4066 135.462 37.8544C136.015 37.3023 136.874 37.3023 137.427 37.8544L150.996 51.4133C151.549 51.9655 151.549 52.8244 150.996 53.3766C150.628 53.622 150.321 53.8061 149.953 53.8061Z"
                fill="white"
            />
            <path
                d="M136.751 53.8061C136.383 53.8061 136.076 53.6834 135.769 53.3766C135.216 52.8244 135.216 51.9655 135.769 51.4133L149.338 37.8544C149.891 37.3023 150.75 37.3023 151.303 37.8544C151.856 38.4066 151.856 39.2655 151.303 39.8177L137.734 53.3766C137.427 53.622 137.058 53.8061 136.751 53.8061Z"
                fill="white"
            />
        </g>
        <defs>
            <clipPath id="clip0_1032_2972">
                <rect width="143" height="137" fill="white" transform="translate(76.5791)" />
            </clipPath>
        </defs>
    </svg>
);

export const ApprovalTaskIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M11.25 13C10.9039 13 10.5655 12.8974 10.2778 12.7051C9.98996 12.5128 9.76566 12.2395 9.63321 11.9197C9.50076 11.5999 9.4661 11.2481 9.53362 10.9086C9.60115 10.5691 9.76782 10.2573 10.0126 10.0126C10.2573 9.76782 10.5691 9.60115 10.9086 9.53362C11.2481 9.4661 11.5999 9.50076 11.9197 9.63321C12.2395 9.76566 12.5128 9.98997 12.7051 10.2778C12.8974 10.5655 13 10.9039 13 11.25C12.9995 11.714 12.8149 12.1588 12.4868 12.4868C12.1588 12.8149 11.714 12.9995 11.25 13ZM11.25 10.5C11.1017 10.5 10.9567 10.544 10.8333 10.6264C10.71 10.7088 10.6139 10.8259 10.5571 10.963C10.5003 11.1 10.4855 11.2508 10.5144 11.3963C10.5433 11.5418 10.6148 11.6754 10.7197 11.7803C10.8246 11.8852 10.9582 11.9566 11.1037 11.9856C11.2492 12.0145 11.4 11.9997 11.537 11.9429C11.6741 11.8861 11.7912 11.79 11.8736 11.6667C11.956 11.5433 12 11.3983 12 11.25C11.9998 11.0512 11.9207 10.8605 11.7801 10.7199C11.6395 10.5793 11.4488 10.5002 11.25 10.5Z"
            fill="inherit"
        />
        <path
            d="M11.25 15.5C10.4094 15.5 9.58773 15.2507 8.88882 14.7837C8.18992 14.3167 7.64518 13.653 7.32351 12.8764C7.00184 12.0998 6.91767 11.2453 7.08166 10.4209C7.24565 9.59645 7.65042 8.83917 8.24479 8.2448C8.83917 7.65042 9.59645 7.24565 10.4209 7.08166C11.2453 6.91768 12.0998 7.00184 12.8764 7.32351C13.653 7.64518 14.3167 8.18992 14.7837 8.88883C15.2507 9.58774 15.5 10.4094 15.5 11.25C15.4987 12.3768 15.0505 13.457 14.2537 14.2537C13.457 15.0505 12.3768 15.4987 11.25 15.5ZM11.25 8C10.6072 8 9.97885 8.19061 9.4444 8.54772C8.90994 8.90484 8.49337 9.41242 8.24739 10.0063C8.0014 10.6001 7.93704 11.2536 8.06245 11.884C8.18785 12.5145 8.49738 13.0936 8.9519 13.5481C9.40642 14.0026 9.98552 14.3122 10.616 14.4376C11.2464 14.563 11.8999 14.4986 12.4937 14.2526C13.0876 14.0066 13.5952 13.5901 13.9523 13.0556C14.3094 12.5211 14.5 11.8928 14.5 11.25C14.499 10.3883 14.1563 9.56227 13.547 8.95298C12.9377 8.3437 12.1117 8.00098 11.25 8Z"
            fill="inherit"
        />
        <path
            d="M12.5 2.5H11V2C10.9992 1.73502 10.8936 1.48111 10.7063 1.29374C10.5189 1.10637 10.265 1.00077 10 1H6C5.73502 1.00077 5.48111 1.10637 5.29374 1.29374C5.10637 1.48111 5.00077 1.73502 5 2V2.5H3.5C3.23502 2.50077 2.98111 2.60637 2.79374 2.79374C2.60637 2.98111 2.50077 3.23502 2.5 3.5V14C2.50077 14.265 2.60637 14.5189 2.79374 14.7063C2.98111 14.8936 3.23502 14.9992 3.5 15H6V14H3.5V3.5H5V5H11V3.5H12.5V6H13.5V3.5C13.4992 3.23502 13.3936 2.98111 13.2063 2.79374C13.0189 2.60637 12.765 2.50077 12.5 2.5ZM10 4H6V2H10V4Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ApprovedTaskIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M11 13.59L9.705 12.295L9 13L11 15L15 11L14.295 10.295L11 13.59Z" fill="inherit" />
        <path
            d="M12.5 2.5H11V2C10.9992 1.73502 10.8936 1.48111 10.7063 1.29374C10.5189 1.10637 10.265 1.00077 10 1H6C5.73502 1.00077 5.48111 1.10637 5.29374 1.29374C5.10637 1.48111 5.00077 1.73502 5 2V2.5H3.5C3.23502 2.50077 2.98111 2.60637 2.79374 2.79374C2.60637 2.98111 2.50077 3.23502 2.5 3.5V14C2.50077 14.265 2.60637 14.5189 2.79374 14.7063C2.98111 14.8936 3.23502 14.9992 3.5 15H8V14H3.5V3.5H5V5H11V3.5H12.5V9H13.5V3.5C13.4992 3.23502 13.3936 2.98111 13.2063 2.79374C13.0189 2.60637 12.765 2.50077 12.5 2.5ZM10 4H6V2H10V4Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const TrashIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M7 6H6V12H7V6Z" fill="currentColor" />
        <path d="M10 6H9V12H10V6Z" fill="currentColor" />
        <path
            d="M2 3V4H3V14C3 14.2652 3.10536 14.5196 3.29289 14.7071C3.48043 14.8946 3.73478 15 4 15H12C12.2652 15 12.5196 14.8946 12.7071 14.7071C12.8946 14.5196 13 14.2652 13 14V4H14V3H2ZM4 14V4H12V14H4Z"
            fill="currentColor"
        />
        <path d="M10 1H6V2H10V1Z" fill="currentColor" />
    </SvgIcon>
);

export const ReportIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M9 9.00098H5V10.001H9V9.00098Z" fill="inherit" />
        <path d="M11 6.50098H5V7.50098H11V6.50098Z" fill="inherit" />
        <path d="M7.5 11.501H5V12.501H7.5V11.501Z" fill="inherit" />
        <path
            d="M12.5 2.50098H11V2.00098C11 1.73576 10.8946 1.48141 10.7071 1.29387C10.5196 1.10633 10.2652 1.00098 10 1.00098H6C5.73478 1.00098 5.48043 1.10633 5.29289 1.29387C5.10536 1.48141 5 1.73576 5 2.00098V2.50098H3.5C3.23478 2.50098 2.98043 2.60633 2.79289 2.79387C2.60536 2.98141 2.5 3.23576 2.5 3.50098V14.001C2.5 14.2662 2.60536 14.5205 2.79289 14.7081C2.98043 14.8956 3.23478 15.001 3.5 15.001H12.5C12.7652 15.001 13.0196 14.8956 13.2071 14.7081C13.3946 14.5205 13.5 14.2662 13.5 14.001V3.50098C13.5 3.23576 13.3946 2.98141 13.2071 2.79387C13.0196 2.60633 12.7652 2.50098 12.5 2.50098ZM6 2.00098H10V4.00098H6V2.00098ZM12.5 14.001H3.5V3.50098H5V5.00098H11V3.50098H12.5V14.001Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const RecurrenceIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M3 3H13.0859L11.2929 1.20705L12 0.5L15 3.5L12 6.5L11.2929 5.79295L13.0859 4H3V7.5H2V4C2.00032 3.73488 2.10578 3.48071 2.29324 3.29324C2.48071 3.10578 2.73488 3.00032 3 3Z"
            fill="inherit"
        />
        <path
            d="M4.70705 10.2071L2.91405 12H13V8.5H14V12C13.9997 12.2651 13.8942 12.5193 13.7068 12.7068C13.5193 12.8942 13.2651 12.9997 13 13H2.91405L4.707 14.7929L4 15.5L1 12.5L4 9.5L4.70705 10.2071Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ScheduleIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M8 15C6.61553 15 5.26216 14.5895 4.11101 13.8203C2.95987 13.0511 2.06266 11.9579 1.53285 10.6788C1.00303 9.3997 0.86441 7.99224 1.13451 6.63437C1.4046 5.2765 2.07129 4.02922 3.05026 3.05026C4.02922 2.07129 5.2765 1.4046 6.63437 1.13451C7.99224 0.86441 9.3997 1.00303 10.6788 1.53285C11.9579 2.06266 13.0511 2.95987 13.8203 4.11101C14.5895 5.26216 15 6.61553 15 8C15 9.85652 14.2625 11.637 12.9497 12.9497C11.637 14.2625 9.85652 15 8 15ZM8 2C6.81332 2 5.65328 2.3519 4.66658 3.01119C3.67989 3.67047 2.91085 4.60755 2.45673 5.7039C2.0026 6.80026 1.88378 8.00666 2.11529 9.17054C2.3468 10.3344 2.91825 11.4035 3.75736 12.2426C4.59648 13.0818 5.66558 13.6532 6.82946 13.8847C7.99335 14.1162 9.19975 13.9974 10.2961 13.5433C11.3925 13.0892 12.3295 12.3201 12.9888 11.3334C13.6481 10.3467 14 9.18669 14 8C14 6.4087 13.3679 4.88258 12.2426 3.75736C11.1174 2.63214 9.5913 2 8 2Z"
            fill="inherit"
        />
        <path d="M10.295 11L7.5 8.205V3.5H8.5V7.79L11 10.295L10.295 11Z" fill="inherit" />
    </SvgIcon>
);

export const GoogleDriveIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '32px', height: '32px' }} {...props} viewBox="0 0 87 78" fill="none">
        <path d="m6.6 66.85 3.85 6.65c.8 1.4 1.95 2.5 3.3 3.3l13.75-23.8h-27.5c0 1.55.4 3.1 1.2 4.5z" fill="#0066da" />
        <path d="m43.65 25-13.75-23.8c-1.35.8-2.5 1.9-3.3 3.3l-25.4 44a9.06 9.06 0 0 0 -1.2 4.5h27.5z" fill="#00ac47" />
        <path
            d="m73.55 76.8c1.35-.8 2.5-1.9 3.3-3.3l1.6-2.75 7.65-13.25c.8-1.4 1.2-2.95 1.2-4.5h-27.502l5.852 11.5z"
            fill="#ea4335"
        />
        <path d="m43.65 25 13.75-23.8c-1.35-.8-2.9-1.2-4.5-1.2h-18.5c-1.6 0-3.15.45-4.5 1.2z" fill="#00832d" />
        <path d="m59.8 53h-32.3l-13.75 23.8c1.35.8 2.9 1.2 4.5 1.2h50.8c1.6 0 3.15-.45 4.5-1.2z" fill="#2684fc" />
        <path
            d="m73.4 26.5-12.7-22c-.8-1.4-1.95-2.5-3.3-3.3l-13.75 23.8 16.15 28h27.45c0-1.55-.4-3.1-1.2-4.5z"
            fill="#ffba00"
        />
    </SvgIcon>
);

export const AddFolderIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M13 10.001H12V12.001H10V13.001H12V15.001H13V13.001H15V12.001H13V10.001Z" fill="inherit" />
        <path
            d="M14 4.00098H8L6.3 2.30098C6.1 2.10098 5.85 2.00098 5.6 2.00098H2C1.45 2.00098 1 2.45098 1 3.00098V13.001C1 13.551 1.45 14.001 2 14.001H9V13.001H2V3.00098H5.6L7.6 5.00098H14V9.00098H15V5.00098C15 4.45098 14.55 4.00098 14 4.00098Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const OpenNewTabIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M13 14H3C2.73489 13.9996 2.48075 13.8942 2.29329 13.7067C2.10583 13.5193 2.00036 13.2651 2 13V3C2.00036 2.73489 2.10583 2.48075 2.29329 2.29329C2.48075 2.10583 2.73489 2.00036 3 2H8V3H3V13H13V8H14V13C13.9996 13.2651 13.8942 13.5193 13.7067 13.7067C13.5193 13.8942 13.2651 13.9996 13 14Z"
            fill="inherit"
        />
        <path d="M10 1V2H13.293L9 6.293L9.707 7L14 2.707V6H15V1H10Z" fill="inherit" />
    </SvgIcon>
);
export const DisconnectIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M2.49892 1.79373L1.79194 2.50096L3.4999 4.20832L4.20688 3.50109L2.49892 1.79373Z" fill="inherit" />
        <path d="M12.4979 11.789L11.7907 12.496L13.4981 14.2039L14.2053 13.4969L12.4979 11.789Z" fill="inherit" />
        <path d="M6.5 1H5.5V3H6.5V1Z" fill="inherit" />
        <path d="M3 5.5H1V6.5H3V5.5Z" fill="inherit" />
        <path d="M15 9.5H13V10.5H15V9.5Z" fill="inherit" />
        <path d="M10.5 13H9.5V15H10.5V13Z" fill="inherit" />
        <path
            d="M8.29 10.535L6.435 12.395C6.24918 12.5808 6.02858 12.7282 5.78579 12.8288C5.54301 12.9294 5.28279 12.9811 5.02 12.9811C4.75721 12.9811 4.49699 12.9294 4.25421 12.8288C4.01142 12.7282 3.79082 12.5808 3.605 12.395C3.22972 12.0197 3.01889 11.5107 3.01889 10.98C3.01889 10.4493 3.22972 9.94028 3.605 9.565L5.465 7.705L4.755 7L2.9 8.86C2.61533 9.13707 2.38853 9.46792 2.23275 9.83334C2.07697 10.1988 1.99531 10.5915 1.99252 10.9887C1.98973 11.386 2.06586 11.7798 2.21649 12.1474C2.36712 12.515 2.58925 12.849 2.87 13.13C3.15032 13.408 3.48277 13.628 3.84828 13.7773C4.21379 13.9266 4.60518 14.0023 5 14C5.40168 14.0004 5.79944 13.921 6.17023 13.7665C6.54101 13.612 6.87743 13.3855 7.16 13.1L9 11.245L8.29 10.535Z"
            fill="inherit"
        />
        <path
            d="M7.705 5.465L9.565 3.605C9.75082 3.41918 9.97142 3.27178 10.2142 3.17121C10.457 3.07065 10.7172 3.01889 10.98 3.01889C11.2428 3.01889 11.503 3.07065 11.7458 3.17121C11.9886 3.27178 12.2092 3.41918 12.395 3.605C12.5808 3.79082 12.7282 4.01142 12.8288 4.25421C12.9294 4.49699 12.9811 4.75721 12.9811 5.02C12.9811 5.28279 12.9294 5.54301 12.8288 5.78579C12.7282 6.02858 12.5808 6.24918 12.395 6.435L10.535 8.295L11.245 9L13.1 7.14C13.3847 6.86293 13.6115 6.53208 13.7673 6.16666C13.923 5.80123 14.0047 5.40851 14.0075 5.01127C14.0103 4.61404 13.9341 4.2202 13.7835 3.85262C13.6329 3.48505 13.4107 3.15104 13.13 2.87C12.8497 2.59196 12.5172 2.37198 12.1517 2.22269C11.7862 2.07339 11.3948 1.99772 11 2C10.5983 1.99961 10.2006 2.07897 9.82977 2.23346C9.45899 2.38795 9.12257 2.61451 8.84 2.9L7 4.755L7.705 5.465Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const GrantAccessIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M16 7.00098H14V5.00098H13V7.00098H11V8.00098H13V10.001H14V8.00098H16V7.00098Z" fill="inherit" />
        <path
            d="M6 2.00098C6.49445 2.00098 6.9778 2.1476 7.38893 2.4223C7.80005 2.69701 8.12048 3.08745 8.3097 3.54427C8.49892 4.00108 8.54843 4.50375 8.45196 4.9887C8.3555 5.47365 8.1174 5.91911 7.76777 6.26874C7.41814 6.61837 6.97268 6.85648 6.48773 6.95294C6.00277 7.0494 5.50011 6.99989 5.04329 6.81068C4.58648 6.62146 4.19603 6.30102 3.92133 5.8899C3.64662 5.47878 3.5 4.99543 3.5 4.50098C3.5 3.83794 3.76339 3.20205 4.23223 2.73321C4.70107 2.26437 5.33696 2.00098 6 2.00098ZM6 1.00098C5.30777 1.00098 4.63108 1.20625 4.0555 1.59083C3.47993 1.97542 3.03133 2.52204 2.76642 3.16158C2.50151 3.80113 2.4322 4.50486 2.56725 5.18379C2.7023 5.86273 3.03564 6.48637 3.52513 6.97585C4.01461 7.46533 4.63825 7.79868 5.31718 7.93372C5.99612 8.06877 6.69985 7.99946 7.33939 7.73455C7.97893 7.46965 8.52556 7.02104 8.91014 6.44547C9.29473 5.8699 9.5 5.19321 9.5 4.50098C9.5 3.57272 9.13125 2.68248 8.47487 2.0261C7.8185 1.36973 6.92826 1.00098 6 1.00098Z"
            fill="inherit"
        />
        <path
            d="M11 15.001H10V12.501C10 11.8379 9.73661 11.2021 9.26777 10.7332C8.79893 10.2644 8.16304 10.001 7.5 10.001H4.5C3.83696 10.001 3.20107 10.2644 2.73223 10.7332C2.26339 11.2021 2 11.8379 2 12.501V15.001H1V12.501C1 11.5727 1.36875 10.6825 2.02513 10.0261C2.6815 9.36973 3.57174 9.00098 4.5 9.00098H7.5C8.42826 9.00098 9.3185 9.36973 9.97487 10.0261C10.6313 10.6825 11 11.5727 11 12.501V15.001Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const CheckMarkCircleIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M7 10.707L4.5 8.2065L5.2065 7.5L7 9.293L10.7925 5.5L11.5 6.2075L7 10.707Z" fill="inherit" />
        <path
            d="M8 1C6.61553 1 5.26216 1.41054 4.11101 2.17971C2.95987 2.94888 2.06266 4.04213 1.53285 5.32122C1.00303 6.6003 0.86441 8.00776 1.13451 9.36563C1.4046 10.7235 2.07129 11.9708 3.05026 12.9497C4.02922 13.9287 5.2765 14.5954 6.63437 14.8655C7.99224 15.1356 9.3997 14.997 10.6788 14.4672C11.9579 13.9373 13.0511 13.0401 13.8203 11.889C14.5895 10.7378 15 9.38447 15 8C15 6.14348 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85652 1 8 1ZM8 14C6.81332 14 5.65328 13.6481 4.66658 12.9888C3.67989 12.3295 2.91085 11.3925 2.45673 10.2961C2.0026 9.19974 1.88378 7.99334 2.11529 6.82946C2.3468 5.66557 2.91825 4.59647 3.75736 3.75736C4.59648 2.91824 5.66558 2.3468 6.82946 2.11529C7.99335 1.88378 9.19975 2.0026 10.2961 2.45672C11.3925 2.91085 12.3295 3.67988 12.9888 4.66658C13.6481 5.65327 14 6.81331 14 8C14 9.5913 13.3679 11.1174 12.2426 12.2426C11.1174 13.3679 9.5913 14 8 14Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const CloudUploadIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '61px', height: '60px' }} {...props} viewBox="0 0 61 60" fill="none">
        <path
            d="M21.125 33.7496L23.7687 36.3933L28.625 31.5558V54.3746H32.375V31.5558L37.2312 36.3933L39.875 33.7496L30.5 24.3746L21.125 33.7496Z"
            fill="inherit"
        />
        <path
            d="M44.5625 41.2496H43.625V37.4996H44.5625C46.8002 37.5891 48.9819 36.786 50.6275 35.2669C52.2732 33.7479 53.248 31.6373 53.3375 29.3996C53.427 27.1618 52.6239 24.9801 51.1048 23.3345C49.5858 21.6889 47.4752 20.7141 45.2375 20.6246H43.625L43.4375 19.0871C43.0215 15.9304 41.4722 13.0326 39.0783 10.9334C36.6843 8.83423 33.6089 7.67684 30.425 7.67684C27.241 7.67684 24.1656 8.83423 21.7717 10.9334C19.3777 13.0326 17.8285 15.9304 17.4125 19.0871L17.375 20.6246H15.7625C13.5247 20.7141 11.4142 21.6889 9.89512 23.3345C8.37607 24.9801 7.57297 27.1618 7.66248 29.3996C7.75199 31.6373 8.72678 33.7479 10.3724 35.2669C12.018 36.786 14.1997 37.5891 16.4375 37.4996H17.375V41.2496H16.4375C13.4307 41.2305 10.5372 40.1007 8.31308 38.0772C6.08899 36.0538 4.69139 33.2796 4.38897 30.2881C4.08656 27.2965 4.90069 24.2988 6.67483 21.8712C8.44897 19.4436 11.0579 17.7575 14 17.1371C14.8094 13.3618 16.889 9.97826 19.8918 7.55107C22.8946 5.12387 26.6389 3.7998 30.5 3.7998C34.3611 3.7998 38.1054 5.12387 41.1081 7.55107C44.1109 9.97826 46.1905 13.3618 47 17.1371C49.9421 17.7575 52.551 19.4436 54.3251 21.8712C56.0993 24.2988 56.9134 27.2965 56.611 30.2881C56.3086 33.2796 54.911 36.0538 52.6869 38.0772C50.4628 40.1007 47.5692 41.2305 44.5625 41.2496Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ShareLinkIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <g clipPath="url(#clip0_3877_334262)">
            <path
                d="M11.5001 10.0011C11.1258 10.003 10.7568 10.0888 10.4201 10.2524C10.0835 10.416 9.78788 10.653 9.55509 10.9461L5.90009 8.66108C6.0333 8.23116 6.0333 7.771 5.90009 7.34108L9.55509 5.05608C9.92606 5.51544 10.4477 5.82867 11.0275 5.94028C11.6073 6.0519 12.2079 5.95469 12.7229 5.66589C13.2379 5.37709 13.634 4.91533 13.8412 4.3624C14.0483 3.80948 14.053 3.20109 13.8546 2.645C13.6561 2.0889 13.2672 1.62101 12.7568 1.32419C12.2464 1.02738 11.6473 0.920804 11.0659 1.02335C10.4844 1.12589 9.95797 1.43093 9.57987 1.88444C9.20177 2.33794 8.99638 2.91064 9.00009 3.50108C9.00245 3.72467 9.03611 3.94683 9.10009 4.16108L5.44509 6.44608C5.12241 6.03987 4.68136 5.7441 4.18307 5.59978C3.68477 5.45545 3.15392 5.46971 2.6641 5.64059C2.17427 5.81146 1.74974 6.13048 1.44934 6.55343C1.14893 6.97638 0.987549 7.4823 0.987549 8.00108C0.987549 8.51985 1.14893 9.02578 1.44934 9.44873C1.74974 9.87168 2.17427 10.1907 2.6641 10.3616C3.15392 10.5324 3.68477 10.5467 4.18307 10.4024C4.68136 10.2581 5.12241 9.96229 5.44509 9.55608L9.10009 11.8411C9.03611 12.0553 9.00245 12.2775 9.00009 12.5011C9.00009 12.9955 9.14671 13.4789 9.42142 13.89C9.69612 14.3011 10.0866 14.6216 10.5434 14.8108C11.0002 15 11.5029 15.0495 11.9878 14.953C12.4728 14.8566 12.9182 14.6185 13.2679 14.2688C13.6175 13.9192 13.8556 13.4738 13.9521 12.9888C14.0485 12.5039 13.999 12.0012 13.8098 11.5444C13.6206 11.0876 13.3001 10.6971 12.889 10.4224C12.4779 10.1477 11.9945 10.0011 11.5001 10.0011ZM11.5001 2.00108C11.7968 2.00108 12.0868 2.08905 12.3334 2.25387C12.5801 2.4187 12.7724 2.65296 12.8859 2.92705C12.9994 3.20114 13.0291 3.50274 12.9713 3.79371C12.9134 4.08469 12.7705 4.35196 12.5608 4.56174C12.351 4.77152 12.0837 4.91438 11.7927 4.97226C11.5018 5.03013 11.2002 5.00043 10.9261 4.8869C10.652 4.77337 10.4177 4.58111 10.2529 4.33443C10.0881 4.08776 10.0001 3.79775 10.0001 3.50108C10.0001 3.10325 10.1581 2.72172 10.4394 2.44042C10.7207 2.15911 11.1023 2.00108 11.5001 2.00108ZM3.50009 9.50108C3.20342 9.50108 2.91341 9.41311 2.66674 9.24828C2.42006 9.08346 2.2278 8.84919 2.11427 8.5751C2.00074 8.30101 1.97104 7.99941 2.02891 7.70844C2.08679 7.41747 2.22965 7.1502 2.43943 6.94042C2.64921 6.73064 2.91649 6.58778 3.20746 6.5299C3.49843 6.47202 3.80003 6.50173 4.07412 6.61526C4.34821 6.72879 4.58247 6.92105 4.7473 7.16772C4.91212 7.4144 5.00009 7.70441 5.00009 8.00108C5.00009 8.3989 4.84206 8.78043 4.56075 9.06174C4.27945 9.34304 3.89792 9.50108 3.50009 9.50108ZM11.5001 14.0011C11.2034 14.0011 10.9134 13.9131 10.6667 13.7483C10.4201 13.5835 10.2278 13.3492 10.1143 13.0751C10.0007 12.801 9.97104 12.4994 10.0289 12.2084C10.0868 11.9175 10.2297 11.6502 10.4394 11.4404C10.6492 11.2306 10.9165 11.0878 11.2075 11.0299C11.4984 10.972 11.8 11.0017 12.0741 11.1153C12.3482 11.2288 12.5825 11.4211 12.7473 11.6677C12.9121 11.9144 13.0001 12.2044 13.0001 12.5011C13.0001 12.8989 12.8421 13.2804 12.5608 13.5617C12.2794 13.843 11.8979 14.0011 11.5001 14.0011Z"
                fill="inherit"
            />
        </g>
        <defs>
            <clipPath id="clip0_3877_334262">
                <rect width="16" height="16" fill="white" transform="translate(0 0.000976562)" />
            </clipPath>
        </defs>
    </SvgIcon>
);

export const CloseIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} viewBox="0 0 24 24" fill="none">
        <path
            d="M18 7.05L16.95 6L12 10.95L7.05 6L6 7.05L10.95 12L6 16.95L7.05 18L12 13.05L16.95 18L18 16.95L13.05 12L18 7.05Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const DiscussionIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} viewBox="0 0 24 24" fill="none">
        <path
            d="M13.305 22.5L12 21.75L15 16.5H19.5C19.8978 16.5 20.2794 16.342 20.5607 16.0607C20.842 15.7794 21 15.3978 21 15V6C21 5.60218 20.842 5.22064 20.5607 4.93934C20.2794 4.65804 19.8978 4.5 19.5 4.5H4.5C4.10218 4.5 3.72064 4.65804 3.43934 4.93934C3.15804 5.22064 3 5.60218 3 6V15C3 15.3978 3.15804 15.7794 3.43934 16.0607C3.72064 16.342 4.10218 16.5 4.5 16.5H11.25V18H4.5C3.70435 18 2.94129 17.6839 2.37868 17.1213C1.81607 16.5587 1.5 15.7956 1.5 15V6C1.5 5.20435 1.81607 4.44129 2.37868 3.87868C2.94129 3.31607 3.70435 3 4.5 3H19.5C20.2956 3 21.0587 3.31607 21.6213 3.87868C22.1839 4.44129 22.5 5.20435 22.5 6V15C22.5 15.7956 22.1839 16.5587 21.6213 17.1213C21.0587 17.6839 20.2956 18 19.5 18H15.87L13.305 22.5Z"
            fill="#334466"
        />
        <path d="M18 7.5H6V9H18V7.5Z" fill="#334466" />
        <path d="M13.5 12H6V13.5H13.5V12Z" fill="#334466" />
    </SvgIcon>
);

export const CopyIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M13.7 7.35098L10.65 4.30098C10.5 4.10098 10.25 4.00098 10 4.00098H6C5.45 4.00098 5 4.45098 5 5.00098V14.001C5 14.551 5.45 15.001 6 15.001H13C13.55 15.001 14 14.551 14 14.001V8.05098C14 7.80098 13.9 7.55098 13.7 7.35098ZM10 5.00098L12.95 8.00098H10V5.00098ZM6 14.001V5.00098H9V8.00098C9 8.55098 9.45 9.00098 10 9.00098H13V14.001H6Z"
            fill="inherit"
        />
        <path d="M3 9.00098H2V2.00098C2 1.45098 2.45 1.00098 3 1.00098H10V2.00098H3V9.00098Z" fill="inherit" />
    </SvgIcon>
);

export const TitleIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path d="M7.5 14.001V4.0486H3V3.00098H13V4.0486H8.5V14.001H7.5Z" fill="inherit" />
    </SvgIcon>
);

export const MoveFolderIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M9 6.50098L8.295 7.20598L9.585 8.50098H5V9.50098H9.585L8.295 10.796L9 11.501L11.5 9.00098L9 6.50098Z"
            fill="inherit"
        />
        <path
            d="M5.5858 3.00098L7.5858 5.00098H14V13.001H2V3.00098H5.5858ZM5.5858 2.00098H2C1.73478 2.00098 1.48043 2.10633 1.29289 2.29387C1.10536 2.48141 1 2.73576 1 3.00098V13.001C1 13.2662 1.10536 13.5205 1.29289 13.7081C1.48043 13.8956 1.73478 14.001 2 14.001H14C14.2652 14.001 14.5196 13.8956 14.7071 13.7081C14.8946 13.5205 15 13.2662 15 13.001V5.00098C15 4.73576 14.8946 4.48141 14.7071 4.29387C14.5196 4.10633 14.2652 4.00098 14 4.00098H8L6.2929 2.29388C6.10537 2.10634 5.85102 2.00098 5.5858 2.00098Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const FolderIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M5.585 3L7.295 4.705L7.585 5H14V13H2V3H5.585ZM5.585 2H2C1.73478 2 1.48043 2.10536 1.29289 2.29289C1.10536 2.48043 1 2.73478 1 3V13C1 13.2652 1.10536 13.5196 1.29289 13.7071C1.48043 13.8946 1.73478 14 2 14H14C14.2652 14 14.5196 13.8946 14.7071 13.7071C14.8946 13.5196 15 13.2652 15 13V5C15 4.73478 14.8946 4.48043 14.7071 4.29289C14.5196 4.10536 14.2652 4 14 4H8L6.295 2.295C6.20197 2.20142 6.09134 2.12717 5.96948 2.07654C5.84763 2.02591 5.71696 1.9999 5.585 2Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const CheckedIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 12 12" fill="none">
        <path
            d="M9.75 1.5H2.25C2.05109 1.5 1.86032 1.57902 1.71967 1.71967C1.57902 1.86032 1.5 2.05109 1.5 2.25V9.75C1.5 9.94891 1.57902 10.1397 1.71967 10.2803C1.86032 10.421 2.05109 10.5 2.25 10.5H9.75C9.94891 10.5 10.1397 10.421 10.2803 10.2803C10.421 10.1397 10.5 9.94891 10.5 9.75V2.25C10.5 2.05109 10.421 1.86032 10.2803 1.71967C10.1397 1.57902 9.94891 1.5 9.75 1.5ZM5.25 8.0625L3.375 6.20351L3.97155 5.625L5.25 6.8796L8.02826 4.125L8.62519 4.71645L5.25 8.0625Z"
            fill="#2F54EB"
        />
    </SvgIcon>
);

export const UncheckedIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 12 12" fill="none">
        <path
            d="M9.75 1.5H2.25C2.05109 1.5 1.86032 1.57902 1.71967 1.71967C1.57902 1.86032 1.5 2.05109 1.5 2.25V9.75C1.5 9.94891 1.57902 10.1397 1.71967 10.2803C1.86032 10.421 2.05109 10.5 2.25 10.5H9.75C9.94891 10.5 10.1397 10.421 10.2803 10.2803C10.421 10.1397 10.5 9.94891 10.5 9.75V2.25C10.5 2.05109 10.421 1.86032 10.2803 1.71967C10.1397 1.57902 9.94891 1.5 9.75 1.5ZM2.25 9.75V2.25H9.75V9.75H2.25Z"
            fill="#161616"
        />
    </SvgIcon>
);

export const AdvanceSearchIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} viewBox="0 0 16 16" fill="none">
        <path d="M14.9999 3H12.9999V1H11.9999V3H9.99994V4H11.9999V6H12.9999V4H14.9999V3Z" fill="inherit" />
        <path
            d="M11.9999 14.2929L9.01239 11.3054C9.73247 10.4046 10.0803 9.26225 9.98433 8.11301C9.8884 6.96377 9.35602 5.8949 8.49657 5.12595C7.63711 4.35699 6.51582 3.94635 5.36303 3.97836C4.21024 4.01037 3.11347 4.48261 2.29801 5.29807C1.48255 6.11353 1.01031 7.2103 0.978303 8.36309C0.946292 9.51588 1.35694 10.6372 2.12589 11.4966C2.89484 12.3561 3.96372 12.8885 5.11296 12.9844C6.2622 13.0803 7.40454 12.7325 8.30534 12.0125L11.2929 15L11.9999 14.2929ZM1.99994 8.5C1.99994 7.80777 2.20522 7.13108 2.5898 6.5555C2.97439 5.97993 3.52101 5.53133 4.16055 5.26642C4.80009 5.00151 5.50383 4.9322 6.18276 5.06725C6.86169 5.2023 7.48533 5.53564 7.97482 6.02513C8.4643 6.51461 8.79765 7.13825 8.93269 7.81718C9.06774 8.49612 8.99843 9.19985 8.73352 9.83939C8.46862 10.4789 8.02001 11.0256 7.44444 11.4101C6.86887 11.7947 6.19218 12 5.49994 12C4.572 11.999 3.68236 11.6299 3.02621 10.9737C2.37006 10.3176 2.00098 9.42794 1.99994 8.5Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const RefreshIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M9 14C10.1867 14 11.3467 13.6481 12.3334 12.9888C13.3201 12.3295 14.0892 11.3925 14.5433 10.2961C14.9974 9.19975 15.1162 7.99335 14.8847 6.82946C14.6532 5.66558 14.0818 4.59648 13.2426 3.75736C12.4035 2.91825 11.3344 2.3468 10.1705 2.11529C9.00666 1.88378 7.80026 2.0026 6.7039 2.45673C5.60754 2.91085 4.67047 3.67989 4.01118 4.66658C3.35189 5.65328 3 6.81331 3 8V11.1L1.2 9.3L0.5 10L3.5 13L6.5 10L5.8 9.3L4 11.1V8C4 7.0111 4.29324 6.0444 4.84265 5.22215C5.39206 4.39991 6.17295 3.75904 7.08658 3.38061C8.00021 3.00217 9.00555 2.90315 9.97545 3.09608C10.9454 3.289 11.8363 3.76521 12.5355 4.46447C13.2348 5.16373 13.711 6.05465 13.9039 7.02455C14.0969 7.99446 13.9978 8.99979 13.6194 9.91342C13.241 10.8271 12.6001 11.6079 11.7779 12.1574C10.9556 12.7068 9.98891 13 9 13V14Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const FilterRemoveIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M15 5.707L14.293 5L12 7.293L9.707 5L9 5.707L11.293 8L9 10.2925L9.7075 11L12 8.707L14.2935 11L15 10.2935L12.707 8L15 5.707Z"
            fill="inherit"
        />
        <path
            d="M2 2C1.73478 2 1.48043 2.10536 1.29289 2.29289C1.10536 2.48043 1 2.73478 1 3V4.58545C0.999989 4.7168 1.02585 4.84687 1.07612 4.96822C1.12638 5.08957 1.20006 5.19983 1.29295 5.2927L5 9V13C5 13.2652 5.10536 13.5196 5.29289 13.7071C5.48043 13.8946 5.73478 14 6 14H8C8.26522 14 8.51957 13.8946 8.70711 13.7071C8.89464 13.5196 9 13.2652 9 13V12H8V13H6V8.58545L2 4.58545V3H12V4H13V3C13 2.73478 12.8946 2.48043 12.7071 2.29289C12.5196 2.10536 12.2652 2 12 2H2Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ExportIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M3 4.00098V2.00098L13 2.00098V4.00098H14V2.00098L13.9962 2.00343C13.9967 1.87218 13.9713 1.74212 13.9215 1.62069C13.8716 1.49926 13.7984 1.38884 13.7059 1.29574C13.6133 1.20264 13.5034 1.12869 13.3823 1.07811C13.2611 1.02754 13.1313 1.00132 13 1.00098L3 1.00098C2.73478 1.00098 2.48043 1.10633 2.29289 1.29387C2.10536 1.48141 2 1.73576 2 2.00098V4.00098H3Z"
            fill="inherit"
        />
        <path
            d="M13 10.001L12.2945 9.29848L8.5 13.0885L8.5 4.00098H7.5L7.5 13.0885L3.7045 9.29848L3 10.001L8 15.001L13 10.001Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const FilterEditIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M13 3H2V4.585L5.705 8.295L6 8.585V13H8V12H9V13C9 13.2652 8.89464 13.5196 8.70711 13.7071C8.51957 13.8946 8.26522 14 8 14H6C5.73478 14 5.48043 13.8946 5.29289 13.7071C5.10536 13.5196 5 13.2652 5 13V9L1.295 5.295C1.20142 5.20197 1.12717 5.09134 1.07654 4.96948C1.02591 4.84763 0.999896 4.71696 1 4.585V3C1 2.73478 1.10536 2.48043 1.29289 2.29289C1.48043 2.10536 1.73478 2 2 2H13V3Z"
            fill="inherit"
        />
        <path
            d="M14.855 5.645L13.355 4.145C13.3085 4.09814 13.2532 4.06094 13.1923 4.03555C13.1314 4.01017 13.066 3.9971 13 3.9971C12.934 3.9971 12.8686 4.01017 12.8077 4.03555C12.7468 4.06094 12.6915 4.09814 12.645 4.145L8 8.795V11H10.205L14.855 6.355C14.9019 6.30852 14.9391 6.25322 14.9644 6.19229C14.9898 6.13136 15.0029 6.06601 15.0029 6C15.0029 5.93399 14.9898 5.86864 14.9644 5.80771C14.9391 5.74678 14.9019 5.69148 14.855 5.645ZM9.795 10H9V9.205L11.5 6.705L12.295 7.5L9.795 10ZM13 6.795L12.205 6L13 5.205L13.795 6L13 6.795Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ComparisonIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon {...props} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M21 4.5H13.5V3C13.5 2.60218 13.342 2.22064 13.0607 1.93934C12.7794 1.65804 12.3978 1.5 12 1.5H3C2.60218 1.5 2.22064 1.65804 1.93934 1.93934C1.65804 2.22064 1.5 2.60218 1.5 3V18C1.5 18.3978 1.65804 18.7794 1.93934 19.0607C2.22064 19.342 2.60218 19.5 3 19.5H10.5V21C10.5 21.3978 10.658 21.7794 10.9393 22.0607C11.2206 22.342 11.6022 22.5 12 22.5H21C21.3978 22.5 21.7794 22.342 22.0607 22.0607C22.342 21.7794 22.5 21.3978 22.5 21V6C22.5 5.60218 22.342 5.22064 22.0607 4.93934C21.7794 4.65804 21.3978 4.5 21 4.5ZM3 11.25H7.6275L5.6925 13.1925L6.75 14.25L10.5 10.5L6.75 6.75L5.6925 7.8075L7.6275 9.75H3V3H12V18H3V11.25ZM12 21V19.5C12.3978 19.5 12.7794 19.342 13.0607 19.0607C13.342 18.7794 13.5 18.3978 13.5 18V6H21V12.75H16.3725L18.3075 10.8075L17.25 9.75L13.5 13.5L17.25 17.25L18.3075 16.1925L16.3725 14.25H21V21H12Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const SettingIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M14.5 12.5H13.5V11.5H14V9.5H12V10H11V9C11 8.86739 11.0527 8.74021 11.1464 8.64645C11.2402 8.55268 11.3674 8.5 11.5 8.5H14.5C14.6326 8.5 14.7598 8.55268 14.8535 8.64645C14.9473 8.74021 15 8.86739 15 9V12C15 12.1326 14.9473 12.2598 14.8535 12.3536C14.7598 12.4473 14.6326 12.5 14.5 12.5Z"
            fill="inherit"
        />
        <path
            d="M12 15H9C8.86739 15 8.74021 14.9473 8.64644 14.8536C8.55267 14.7598 8.5 14.6326 8.5 14.5V11.5C8.5 11.3674 8.55267 11.2402 8.64644 11.1464C8.74021 11.0527 8.86739 11 9 11H12C12.1326 11 12.2598 11.0527 12.3535 11.1464C12.4473 11.2402 12.5 11.3674 12.5 11.5V14.5C12.5 14.6326 12.4473 14.7598 12.3535 14.8536C12.2598 14.9473 12.1326 15 12 15ZM9.5 14H11.5V12H9.5V14Z"
            fill="inherit"
        />
        <path
            d="M7.5 9.92895C7.13865 9.83482 6.81093 9.64126 6.55407 9.37023C6.2972 9.09921 6.12149 8.76158 6.04687 8.39571C5.97225 8.02983 6.00173 7.65036 6.13195 7.30039C6.26217 6.95043 6.48792 6.64399 6.78356 6.41588C7.0792 6.18778 7.43287 6.04714 7.80442 6.00995C8.17597 5.97276 8.55051 6.0405 8.88549 6.20548C9.22048 6.37046 9.50249 6.62607 9.6995 6.94328C9.8965 7.26049 10.0006 7.62659 10 8H11C11.0006 7.4279 10.8376 6.86756 10.5302 6.38506C10.2228 5.90256 9.78384 5.51801 9.2651 5.27675C8.74636 5.0355 8.16945 4.9476 7.6024 5.02342C7.03535 5.09924 6.5018 5.33562 6.06467 5.70469C5.62753 6.07375 5.30504 6.56012 5.13522 7.10643C4.9654 7.65274 4.95532 8.23622 5.10619 8.78806C5.25705 9.33991 5.56256 9.83712 5.9867 10.2211C6.41083 10.605 6.9359 10.8596 7.5 10.955V9.92895Z"
            fill="inherit"
        />
        <path
            d="M14.445 6.775L13.29 7.79L12.58 7.08L13.785 6.02L12.605 3.98L10.885 4.56C10.4821 4.22464 10.0257 3.95928 9.53499 3.775L9.17999 2H6.81999L6.46499 3.775C5.9703 3.95414 5.51146 4.21995 5.10999 4.56L3.39499 3.98L2.21499 6.02L3.57499 7.215C3.48248 7.73257 3.48248 8.26243 3.57499 8.78L2.21499 9.98L3.39499 12.02L5.11499 11.44C5.51789 11.7754 5.97424 12.0407 6.46499 12.225L6.81999 14H7.49999V15H6.81999C6.58878 14.9997 6.36479 14.9194 6.18612 14.7726C6.00745 14.6259 5.88514 14.4218 5.83999 14.195L5.58499 12.935C5.35862 12.8246 5.13981 12.6993 4.92999 12.56L3.71499 12.97C3.61167 13.0036 3.50364 13.0205 3.39499 13.02C3.21935 13.0212 3.04658 12.9755 2.89451 12.8876C2.74245 12.7997 2.61661 12.6728 2.52999 12.52L1.34999 10.48C1.23328 10.2795 1.18992 10.0447 1.22732 9.81572C1.26471 9.58677 1.38055 9.37794 1.55499 9.225L2.51499 8.385C2.50499 8.255 2.49999 8.13 2.49999 8C2.49999 7.87 2.50999 7.745 2.51999 7.62L1.55499 6.775C1.38055 6.62206 1.26471 6.41324 1.22732 6.18428C1.18992 5.95533 1.23328 5.72049 1.34999 5.52L2.52999 3.48C2.61661 3.3272 2.74245 3.20029 2.89451 3.1124C3.04658 3.0245 3.21935 2.97879 3.39499 2.98C3.50364 2.9795 3.61167 2.99638 3.71499 3.03L4.92499 3.44C5.13652 3.30065 5.357 3.17538 5.58499 3.065L5.83999 1.805C5.88514 1.57824 6.00745 1.37411 6.18612 1.22735C6.36479 1.08059 6.58878 1.00025 6.81999 1H9.17999C9.41121 1.00025 9.6352 1.08059 9.81386 1.22735C9.99253 1.37411 10.1148 1.57824 10.16 1.805L10.415 3.065C10.6414 3.17541 10.8602 3.30068 11.07 3.44L12.285 3.03C12.3883 2.99638 12.4963 2.9795 12.605 2.98C12.7806 2.97879 12.9534 3.0245 13.1055 3.1124C13.2575 3.20029 13.3834 3.3272 13.47 3.48L14.65 5.52C14.7667 5.72049 14.8101 5.95533 14.7727 6.18428C14.7353 6.41324 14.6194 6.62206 14.445 6.775Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const StatusAckIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M12 15C11.4067 15 10.8266 14.8241 10.3333 14.4944C9.83994 14.1648 9.45543 13.6962 9.22836 13.1481C9.0013 12.5999 8.94189 11.9967 9.05765 11.4147C9.1734 10.8328 9.45912 10.2982 9.87868 9.87868C10.2982 9.45912 10.8328 9.1734 11.4147 9.05765C11.9967 8.94189 12.5999 9.0013 13.1481 9.22836C13.6962 9.45543 14.1648 9.83994 14.4944 10.3333C14.8241 10.8266 15 11.4067 15 12C14.9991 12.7954 14.6828 13.5579 14.1203 14.1203C13.5579 14.6828 12.7954 14.9991 12 15ZM12 10C11.6044 10 11.2178 10.1173 10.8889 10.3371C10.56 10.5568 10.3036 10.8692 10.1522 11.2346C10.0009 11.6001 9.96126 12.0022 10.0384 12.3902C10.1156 12.7781 10.3061 13.1345 10.5858 13.4142C10.8655 13.6939 11.2219 13.8844 11.6098 13.9616C11.9978 14.0387 12.3999 13.9991 12.7654 13.8478C13.1308 13.6964 13.4432 13.44 13.6629 13.1111C13.8827 12.7822 14 12.3956 14 12C13.9994 11.4698 13.7885 10.9614 13.4136 10.5864C13.0386 10.2115 12.5303 10.0006 12 10Z"
            fill="inherit"
        />
        <path d="M6 7.795L4.705 6.5L4 7.205L6 9.205L9.5 5.705L8.795 5L6 7.795Z" fill="inherit" />
        <path
            d="M7 12C6.0111 12 5.0444 11.7068 4.22215 11.1574C3.39991 10.6079 2.75904 9.82705 2.3806 8.91342C2.00217 7.99979 1.90315 6.99446 2.09608 6.02455C2.289 5.05465 2.76521 4.16373 3.46447 3.46447C4.16373 2.76521 5.05465 2.289 6.02455 2.09608C6.99446 1.90315 7.99979 2.00217 8.91342 2.3806C9.82705 2.75904 10.6079 3.39991 11.1574 4.22215C11.7068 5.0444 12 6.0111 12 7H13C13 5.81331 12.6481 4.65328 11.9888 3.66658C11.3295 2.67989 10.3925 1.91085 9.2961 1.45673C8.19975 1.0026 6.99335 0.88378 5.82946 1.11529C4.66558 1.3468 3.59648 1.91825 2.75736 2.75736C1.91825 3.59648 1.3468 4.66558 1.11529 5.82946C0.88378 6.99335 1.0026 8.19975 1.45673 9.2961C1.91085 10.3925 2.67989 11.3295 3.66658 11.9888C4.65328 12.6481 5.81331 13 7 13V12Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const InfoIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M8 1C6.61553 1 5.26216 1.41054 4.11101 2.17971C2.95987 2.94888 2.06266 4.04213 1.53285 5.32122C1.00303 6.6003 0.86441 8.00776 1.13451 9.36563C1.4046 10.7235 2.07129 11.9708 3.05026 12.9497C4.02922 13.9287 5.2765 14.5954 6.63437 14.8655C7.99224 15.1356 9.3997 14.997 10.6788 14.4672C11.9579 13.9373 13.0511 13.0401 13.8203 11.889C14.5895 10.7378 15 9.38447 15 8C15 6.14348 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85652 1 8 1ZM8 4C8.14834 4 8.29334 4.04399 8.41668 4.1264C8.54002 4.20881 8.63615 4.32594 8.69291 4.46299C8.74968 4.60003 8.76453 4.75083 8.73559 4.89632C8.70665 5.0418 8.63522 5.17544 8.53033 5.28033C8.42544 5.38522 8.29181 5.45665 8.14632 5.48559C8.00083 5.51453 7.85004 5.49968 7.71299 5.44291C7.57595 5.38614 7.45881 5.29001 7.3764 5.16668C7.29399 5.04334 7.25 4.89834 7.25 4.75C7.25 4.55109 7.32902 4.36032 7.46967 4.21967C7.61033 4.07902 7.80109 4 8 4ZM10 12.0625H6V10.9375H7.4375V8.0625H6.5V6.9375H8.5625V10.9375H10V12.0625Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const InfoIconOutline = (props: SvgIconProps) => {
    return (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.5 11V7H6.5V8H7.5V11H6V12H10V11H8.5Z" fill="#161616" />
            <path
                d="M8 4C7.85167 4 7.70666 4.04399 7.58333 4.1264C7.45999 4.20881 7.36386 4.32595 7.30709 4.46299C7.25033 4.60004 7.23548 4.75084 7.26441 4.89632C7.29335 5.04181 7.36478 5.17544 7.46967 5.28033C7.57456 5.38522 7.7082 5.45665 7.85369 5.48559C7.99917 5.51453 8.14997 5.49968 8.28701 5.44291C8.42406 5.38615 8.54119 5.29002 8.6236 5.16668C8.70602 5.04334 8.75 4.89834 8.75 4.75C8.75 4.55109 8.67098 4.36033 8.53033 4.21967C8.38968 4.07902 8.19892 4 8 4Z"
                fill="#161616"
            />
            <path
                d="M8 15C6.61553 15 5.26216 14.5895 4.11101 13.8203C2.95987 13.0511 2.06266 11.9579 1.53285 10.6788C1.00303 9.3997 0.86441 7.99224 1.13451 6.63437C1.4046 5.2765 2.07129 4.02922 3.05026 3.05026C4.02922 2.07129 5.2765 1.4046 6.63437 1.13451C7.99224 0.86441 9.3997 1.00303 10.6788 1.53285C11.9579 2.06266 13.0511 2.95987 13.8203 4.11101C14.5895 5.26216 15 6.61553 15 8C15 9.85652 14.2625 11.637 12.9497 12.9497C11.637 14.2625 9.85652 15 8 15ZM8 2C6.81332 2 5.65328 2.3519 4.66658 3.01119C3.67989 3.67047 2.91085 4.60755 2.45673 5.7039C2.0026 6.80026 1.88378 8.00666 2.11529 9.17054C2.3468 10.3344 2.91825 11.4035 3.75736 12.2426C4.59648 13.0818 5.66558 13.6532 6.82946 13.8847C7.99335 14.1162 9.19975 13.9974 10.2961 13.5433C11.3925 13.0892 12.3295 12.3201 12.9888 11.3334C13.6481 10.3467 14 9.18669 14 8C14 6.4087 13.3679 4.88258 12.2426 3.75736C11.1174 2.63214 9.5913 2 8 2Z"
                fill="#161616"
            />
        </svg>
    );
};

export const PlanIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            d="M12 12V13H13.2296C12.9501 13.3153 12.6068 13.5675 12.2224 13.7399C11.838 13.9123 11.4213 14.001 11 14C10.2046 13.9991 9.44206 13.6828 8.87964 13.1204C8.31722 12.5579 8.00087 11.7954 8 11H7C6.99839 11.8133 7.24525 12.6078 7.70755 13.277C8.16984 13.9462 8.82552 14.4582 9.58681 14.7445C10.3481 15.0308 11.1787 15.0777 11.9674 14.879C12.7561 14.6803 13.4653 14.2454 14 13.6326V15H15V12H12Z"
            fill="inherit"
        />
        <path
            d="M11 7C10.4317 7.00293 9.87048 7.12632 9.35336 7.36203C8.83625 7.59774 8.37498 7.94041 8 8.36745V7H7V10H10V9H8.7704C9.04985 8.68471 9.39318 8.43249 9.77759 8.26008C10.162 8.08766 10.5787 7.99901 11 8C11.7954 8.00087 12.5579 8.31722 13.1204 8.87964C13.6828 9.44206 13.9991 10.2046 14 11H15C14.9988 9.93951 14.577 8.9228 13.8271 8.17292C13.0772 7.42304 12.0605 7.00122 11 7Z"
            fill="inherit"
        />
        <path
            d="M6 14H3V12H4V11H3V8.5H4V7.5H3V5H4V4H3V2H12V6H13V2C13 1.73478 12.8946 1.48043 12.7071 1.29289C12.5196 1.10536 12.2652 1 12 1H3C2.73478 1 2.48043 1.10536 2.29289 1.29289C2.10536 1.48043 2 1.73478 2 2V4H1V5H2V7.5H1V8.5H2V11H1V12H2V14C2 14.2652 2.10536 14.5196 2.29289 14.7071C2.48043 14.8946 2.73478 15 3 15H6V14Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ComponentIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '12px', height: '14px' }} {...props} viewBox="0 0 12 14" fill="none">
        <path
            d="M11.694 3.265L6.29404 0.0852024C6.20283 0.0293853 6.09937 0 5.99405 0C5.88873 0 5.78526 0.0293853 5.69405 0.0852024L0.294113 3.265C0.202776 3.32237 0.127436 3.4044 0.0757858 3.50272C0.0241353 3.60105 -0.00197608 3.71213 0.000116589 3.82465V10.1842C0.000895269 10.2976 0.0302289 10.4086 0.0850823 10.5059C0.139936 10.6032 0.218316 10.6831 0.312113 10.7375L5.71205 13.9173C5.79995 13.9701 5.89901 13.9986 6.00005 14C6.10295 13.9983 6.20389 13.9699 6.29404 13.9173L11.694 10.7375C11.7867 10.6823 11.8638 10.602 11.9176 10.5048C11.9714 10.4075 11.9998 10.2969 12 10.1842V3.82465C12.0009 3.71088 11.9729 3.59894 11.9191 3.50051C11.8653 3.40207 11.7876 3.32074 11.694 3.265ZM5.40005 12.2829L1.2001 9.80903V4.89942L5.40005 7.3733V12.2829ZM6.00005 6.27309L1.8361 3.82465L6.00005 1.36984L10.164 3.82465L6.00005 6.27309ZM10.8 9.80903L6.60004 12.2829V7.3733L10.8 4.89942V9.80903Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ComponentIllustrator = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '251px', height: '200px' }} {...props} viewBox="0 0 251 200" fill="none">
        <rect x="0.5" width="250" height="200" fill="white" />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M63.5 134H154.5C155.015 134 155.517 133.944 156 133.839C156.483 133.944 156.985 134 157.5 134H209.5C213.366 134 216.5 130.866 216.5 127C216.5 123.134 213.366 120 209.5 120H203.5C199.634 120 196.5 116.866 196.5 113C196.5 109.134 199.634 106 203.5 106H222.5C226.366 106 229.5 102.866 229.5 99C229.5 95.134 226.366 92 222.5 92H200.5C204.366 92 207.5 88.866 207.5 85C207.5 81.134 204.366 78 200.5 78H136.5C140.366 78 143.5 74.866 143.5 71C143.5 67.134 140.366 64 136.5 64H79.5C75.634 64 72.5 67.134 72.5 71C72.5 74.866 75.634 78 79.5 78H39.5C35.634 78 32.5 81.134 32.5 85C32.5 88.866 35.634 92 39.5 92H64.5C68.366 92 71.5 95.134 71.5 99C71.5 102.866 68.366 106 64.5 106H24.5C20.634 106 17.5 109.134 17.5 113C17.5 116.866 20.634 120 24.5 120H63.5C59.634 120 56.5 123.134 56.5 127C56.5 130.866 59.634 134 63.5 134ZM226.5 134C230.366 134 233.5 130.866 233.5 127C233.5 123.134 230.366 120 226.5 120C222.634 120 219.5 123.134 219.5 127C219.5 130.866 222.634 134 226.5 134Z"
            fill="#F3F7FF"
        />
        <path d="M125.5 62L171.5 89L125.5 115.5L79.5 88.5L125.5 62Z" fill="white" />
        <path
            d="M125.5 115.5L79.5 88.5L125.5 62L171.5 89"
            stroke="#1F64E7"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path d="M171.5 143.5L125.5 170V115.5L134.5 110.315L148.5 102.25L171.5 89V143.5Z" fill="white" />
        <path
            d="M134.5 110.315L125.5 115.5L125.5 170L171.5 143.5V89L148.5 102.25"
            stroke="#1F64E7"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M138.5 108L145 104.5"
            stroke="#1F64E7"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M125.5 170L79.5 143.5V88.5L125.5 115.5V170Z"
            fill="white"
            stroke="#1F64E7"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path d="M120.5 162L84.5 140.865V97L120.5 118.534V162Z" fill="#E8F0FE" />
        <path
            d="M98.5 34.3027L109.6 46.7559M150.6 34.3027L139.5 46.7559L150.6 34.3027ZM124.5 30V46.7559V30Z"
            stroke="#75A4FE"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </SvgIcon>
);
export const LeafComponentIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path fillRule="evenodd" clipRule="evenodd" d="M2.5 7.5H8.5V13.5H2.5V7.5Z" fill="#161616" />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.5 7.5H13.5V13.5H7.5V7.5ZM8.5 8.5V12.5H12.5V8.5H8.5Z"
            fill="#161616"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M2.5 2.5H8.5V8.5H2.5V2.5ZM3.5 3.5V7.5H7.5V3.5H3.5Z"
            fill="#161616"
        />
    </SvgIcon>
);

export const AssemblyIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.5 7.5H13.5V13.5H7.5V7.5ZM8.5 8.5V12.5H12.5V8.5H8.5Z"
            fill="#161616"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M2.5 2.5H8.5V8.5H2.5V2.5ZM3.5 3.5V7.5H7.5V3.5H3.5Z"
            fill="#161616"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M2.5 7.5H8.5V13.5H2.5V7.5ZM3.5 8.5V12.5H7.5V8.5H3.5Z"
            fill="#161616"
        />
    </SvgIcon>
);
export const BurgerIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon fontSize="inherit" viewBox="0 0 16 14" style={{ width: 16, height: 14 }} {...props}>
            <path d="M0 1C0 0.734784 0.105357 0.48043 0.292893 0.292893C0.48043 0.105357 0.734784 0 1 0H15C15.2652 0 15.5196 0.105357 15.7071 0.292893C15.8946 0.48043 16 0.734784 16 1C16 1.26522 15.8946 1.51957 15.7071 1.70711C15.5196 1.89464 15.2652 2 15 2H1C0.734784 2 0.48043 1.89464 0.292893 1.70711C0.105357 1.51957 0 1.26522 0 1ZM0 13C0 12.7348 0.105357 12.4804 0.292893 12.2929C0.48043 12.1054 0.734784 12 1 12H15C15.2652 12 15.5196 12.1054 15.7071 12.2929C15.8946 12.4804 16 12.7348 16 13C16 13.2652 15.8946 13.5196 15.7071 13.7071C15.5196 13.8946 15.2652 14 15 14H1C0.734784 14 0.48043 13.8946 0.292893 13.7071C0.105357 13.5196 0 13.2652 0 13ZM1 6C0.734784 6 0.48043 6.10536 0.292893 6.29289C0.105357 6.48043 0 6.73478 0 7C0 7.26522 0.105357 7.51957 0.292893 7.70711C0.48043 7.89464 0.734784 8 1 8H9C9.26522 8 9.51957 7.89464 9.70711 7.70711C9.89464 7.51957 10 7.26522 10 7C10 6.73478 9.89464 6.48043 9.70711 6.29289C9.51957 6.10536 9.26522 6 9 6H1Z" />
        </SvgIcon>
    );
};
export const GroupObjectIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon fontSize="inherit" viewBox="0 0 16 16" style={{ width: 16, height: 16 }} {...props}>
            <path
                d="M11 12H9V11H11V9H12V11C11.9997 11.2651 11.8943 11.5193 11.7068 11.7068C11.5193 11.8943 11.2651 11.9997 11 12Z"
                fill="inherit"
            />
            <path
                d="M5 7H4V5C4.00029 4.73487 4.10574 4.48069 4.29321 4.29321C4.48069 4.10574 4.73487 4.00029 5 4H7V5H5V7Z"
                fill="inherit"
            />
            <path
                d="M14 4H12V2C11.9997 1.73488 11.8942 1.4807 11.7068 1.29323C11.5193 1.10576 11.2651 1.0003 11 1H2C1.73488 1.0003 1.4807 1.10576 1.29323 1.29323C1.10576 1.4807 1.0003 1.73488 1 2V11C1.0003 11.2651 1.10576 11.5193 1.29323 11.7068C1.4807 11.8942 1.73488 11.9997 2 12H4V14C4.0003 14.2651 4.10576 14.5193 4.29323 14.7068C4.4807 14.8942 4.73488 14.9997 5 15H14C14.2651 14.9997 14.5193 14.8942 14.7068 14.7068C14.8942 14.5193 14.9997 14.2651 15 14V5C14.9997 4.73488 14.8942 4.4807 14.7068 4.29323C14.5193 4.10576 14.2651 4.0003 14 4ZM14 14H5V12H7V11H5V9H4V11H2V2H11V4H9V5H11V7H12V5H14V14Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const PlusIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path d="M8.5 7.5V4H7.5V7.5H4V8.5H7.5V12H8.5V8.5H12V7.5H8.5Z" fill="inherit" />
    </SvgIcon>
);

export const GroupIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path
            d="M14 5H2C1.73487 4.99971 1.48069 4.89426 1.29321 4.70679C1.10574 4.51931 1.00029 4.26513 1 4V2C1.00028 1.73487 1.10572 1.48068 1.2932 1.2932C1.48068 1.10572 1.73487 1.00028 2 1H14C14.2651 1.00028 14.5193 1.10572 14.7068 1.2932C14.8943 1.48068 14.9997 1.73487 15 2V4C14.9997 4.26513 14.8943 4.51931 14.7068 4.70679C14.5193 4.89426 14.2651 4.99971 14 5ZM2 2V4H14V2H2Z"
            fill="inherit"
        />
        <path
            d="M14 15H2C1.73487 14.9997 1.48069 14.8943 1.29321 14.7068C1.10574 14.5193 1.00029 14.2651 1 14V12C1.00028 11.7349 1.10572 11.4807 1.2932 11.2932C1.48068 11.1057 1.73487 11.0003 2 11H14C14.2651 11.0003 14.5193 11.1057 14.7068 11.2932C14.8943 11.4807 14.9997 11.7349 15 12V14C14.9997 14.2651 14.8943 14.5193 14.7068 14.7068C14.5193 14.8943 14.2651 14.9997 14 15ZM2 12V14H14V12H2Z"
            fill="inherit"
        />
        <path
            d="M14 10H2C1.73487 9.99971 1.48069 9.89426 1.29321 9.70679C1.10574 9.51931 1.00029 9.26513 1 9V7C1.00028 6.73487 1.10572 6.48068 1.2932 6.2932C1.48068 6.10572 1.73487 6.00028 2 6H14C14.2651 6.00028 14.5193 6.10572 14.7068 6.2932C14.8943 6.48068 14.9997 6.73487 15 7V9C14.9997 9.26513 14.8943 9.51931 14.7068 9.70679C14.5193 9.89426 14.2651 9.99971 14 10ZM2 7V9H14V7H2Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const CheckboxUncheckedIcon = (props: SvgIconProps) => (
    <SvgIcon width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g clipPath="url(#clip0_612_2934)">
            <g filter="url(#filter0_dd_612_2934)">
                <rect width="24" height="24" rx="6" fill="white" />
            </g>
            <g opacity="0.2">
                <path
                    d="M16.5716 8.57141L10.2859 14.8571L7.42871 12"
                    stroke="#191C1F"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
            </g>
        </g>
        <defs>
            <filter
                id="filter0_dd_612_2934"
                x="-8"
                y="-4"
                width="40"
                height="40"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
            >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                />
                <feOffset dy="4" />
                <feGaussianBlur stdDeviation="4" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.196487 0 0 0 0 0.196487 0 0 0 0 0.279476 0 0 0 0.06 0" />
                <feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_612_2934" />
                <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                />
                <feOffset dy="4" />
                <feGaussianBlur stdDeviation="2" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.196487 0 0 0 0 0.196487 0 0 0 0 0.279476 0 0 0 0.08 0" />
                <feBlend mode="multiply" in2="effect1_dropShadow_612_2934" result="effect2_dropShadow_612_2934" />
                <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_612_2934" result="shape" />
            </filter>
            <clipPath id="clip0_612_2934">
                <rect width="24" height="24" fill="white" />
            </clipPath>
        </defs>
    </SvgIcon>
);

export const CheckboxCheckedIcon = (props: SvgIconProps) => (
    <SvgIcon width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g clipPath="url(#clip0_803_2656)">
            <g filter="url(#filter0_dd_803_2656)">
                <rect width="24" height="24" rx="6" fill="#4285F4" />
            </g>
            <path
                d="M16.5716 8.57141L10.2859 14.8571L7.42871 12"
                stroke="white"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </g>
        <defs>
            <filter
                id="filter0_dd_803_2656"
                x="-8"
                y="-4"
                width="40"
                height="40"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
            >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                />
                <feOffset dy="4" />
                <feGaussianBlur stdDeviation="4" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.196487 0 0 0 0 0.196487 0 0 0 0 0.279476 0 0 0 0.06 0" />
                <feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_803_2656" />
                <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                />
                <feOffset dy="4" />
                <feGaussianBlur stdDeviation="2" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.196487 0 0 0 0 0.196487 0 0 0 0 0.279476 0 0 0 0.08 0" />
                <feBlend mode="multiply" in2="effect1_dropShadow_803_2656" result="effect2_dropShadow_803_2656" />
                <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_803_2656" result="shape" />
            </filter>
            <clipPath id="clip0_803_2656">
                <rect width="24" height="24" fill="white" />
            </clipPath>
        </defs>
    </SvgIcon>
);

export const TreeIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path
            d="M15 10V6H11V7.5H8.5V3.5C8.5 3.23478 8.39464 2.98043 8.20711 2.79289C8.01957 2.60536 7.76522 2.5 7.5 2.5H5V1H1V5H5V3.5H7.5V12.5C7.5 12.7652 7.60536 13.0196 7.79289 13.2071C7.98043 13.3946 8.23478 13.5 8.5 13.5H11V15H15V11H11V12.5H8.5V8.5H11V10H15ZM4 4H2V2H4V4ZM12 12H14V14H12V12ZM12 7H14V9H12V7Z"
            fill="inherit"
        />
    </SvgIcon>
);
export const AddComponentIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.29404 1.0852L12.694 4.265C12.7876 4.32074 12.8653 4.40207 12.9191 4.50051C12.9729 4.59894 13.0009 4.71088 13 4.82465V8H11.8V5.89942L7.60004 8.3733V13.2829L9 12.4583V13.9128L7.29404 14.9173C7.20389 14.9699 7.10295 14.9983 7.00005 15C6.89901 14.9986 6.79995 14.9701 6.71205 14.9173L1.31211 11.7375C1.21832 11.6831 1.13994 11.6032 1.08508 11.5059C1.03023 11.4086 1.0009 11.2976 1.00012 11.1842V4.82465C0.998024 4.71213 1.02414 4.60105 1.07579 4.50272C1.12744 4.4044 1.20278 4.32237 1.29411 4.265L6.69405 1.0852C6.78526 1.02939 6.88873 1 6.99405 1C7.09937 1 7.20283 1.02939 7.29404 1.0852ZM2.2001 10.809L6.40005 13.2829V8.3733L2.2001 5.89942V10.809ZM2.8361 4.82465L7.00005 7.27309L11.164 4.82465L7.00005 2.36984L2.8361 4.82465Z"
            fill="inherit"
        />
        <rect width="5" height="5" transform="translate(10 9)" fill="white" />
        <path d="M15 11H13V9H12V11H10V12H12V14H13V12H15V11Z" fill="inherit" />
    </SvgIcon>
);

export const NoResultIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '250px', height: '200px', color: '#FFFFFF' }} {...props} viewBox="0 0 251 200" fill="none">
        <rect x="0.5" width="250" height="200" fill="none" />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M207.5 65C211.366 65 214.5 68.134 214.5 72C214.5 75.866 211.366 79 207.5 79H167.5C171.366 79 174.5 82.134 174.5 86C174.5 89.866 171.366 93 167.5 93H189.5C193.366 93 196.5 96.134 196.5 100C196.5 103.866 193.366 107 189.5 107H179.326C174.452 107 170.5 110.134 170.5 114C170.5 116.577 172.5 118.911 176.5 121C180.366 121 183.5 124.134 183.5 128C183.5 131.866 180.366 135 176.5 135H93.5C89.634 135 86.5 131.866 86.5 128C86.5 124.134 89.634 121 93.5 121H54.5C50.634 121 47.5 117.866 47.5 114C47.5 110.134 50.634 107 54.5 107H94.5C98.366 107 101.5 103.866 101.5 100C101.5 96.134 98.366 93 94.5 93H69.5C65.634 93 62.5 89.866 62.5 86C62.5 82.134 65.634 79 69.5 79H109.5C105.634 79 102.5 75.866 102.5 72C102.5 68.134 105.634 65 109.5 65H207.5ZM207.5 93C211.366 93 214.5 96.134 214.5 100C214.5 103.866 211.366 107 207.5 107C203.634 107 200.5 103.866 200.5 100C200.5 96.134 203.634 93 207.5 93Z"
            fill="#EBEEF7"
        />
        <path
            d="M121 133C139.502 133 154.5 118.002 154.5 99.5C154.5 80.9985 139.502 66 121 66C102.498 66 87.5 80.9985 87.5 99.5C87.5 118.002 102.498 133 121 133Z"
            fill="#EBEEF7"
            stroke="#334466"
            strokeWidth="2.5"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M115.632 125.494C117.391 125.819 119.18 125.987 121 126C135.636 126 147.5 114.136 147.5 99.5C147.5 84.8645 135.636 73 121 73C117.24 73 113.664 73.7829 110.424 75.1946C104.794 77.6479 100.182 81.9999 97.396 87.4419C95.5445 91.0589 94.5 95.1575 94.5 99.5C94.5 103.44 95.3599 107.179 96.9021 110.54C98.0032 112.94 99.4521 115.146 101.184 117.096"
            fill="white"
        />
        <path
            d="M115.632 125.494C117.391 125.819 119.18 125.987 121 126C135.636 126 147.5 114.136 147.5 99.5C147.5 84.8645 135.636 73 121 73C117.24 73 113.664 73.7829 110.424 75.1946C104.794 77.6479 100.182 81.9999 97.396 87.4419C95.5445 91.0589 94.5 95.1575 94.5 99.5C94.5 103.44 95.3599 107.179 96.9021 110.54C98.0032 112.94 99.4521 115.146 101.184 117.096"
            stroke="#334466"
            strokeWidth="2.5"
            strokeLinecap="round"
        />
        <path
            d="M104.297 120.075C106.445 121.821 108.872 123.237 111.5 124.247"
            stroke="#334466"
            strokeWidth="2.5"
            strokeLinecap="round"
        />
        <path d="M148.5 126L154.5 132" stroke="#334466" strokeWidth="2.5" />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M153.529 131.03C151.636 132.923 151.636 135.992 153.529 137.885L164.614 148.97C166.507 150.863 169.576 150.863 171.468 148.97C173.361 147.077 173.361 144.008 171.468 142.116L160.383 131.03C158.49 129.138 155.422 129.138 153.529 131.03Z"
            fill="#B8C6E5"
            stroke="#334466"
            strokeWidth="2.5"
        />
        <path d="M158.5 133L169.5 144" stroke="white" strokeWidth="2.5" strokeLinecap="round" />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M114.501 88C114.501 99.598 123.903 109 135.501 109C137.779 109 139.972 108.637 142.027 107.966C138.674 116.287 130.523 122.161 121.001 122.161C108.485 122.161 98.3398 112.015 98.3398 99.5C98.3398 88.1596 106.67 78.7648 117.546 77.1011C115.613 80.2793 114.501 84.0097 114.501 88Z"
            fill="#B8C6E5"
        />
        <path
            d="M121.5 81C120.227 81 118.982 81.1253 117.779 81.3642M114.145 82.4761C107.304 85.3508 102.5 92.1144 102.5 100"
            stroke="#B8C6E5"
            strokeWidth="2.5"
            strokeLinecap="round"
        />
        <path
            d="M174.676 99.7773H166.5M181 92H163.824H181ZM188 92H185.779H188Z"
            stroke="#B8C6E5"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M84.6743 121.777H76.4985M79.9985 113H62.8228H79.9985ZM56.9985 113H52.7773H56.9985Z"
            stroke="#B8C6E5"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </SvgIcon>
);
export const RemoveComponentIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.29404 1.0852L12.694 4.265C12.7876 4.32074 12.8653 4.40207 12.9191 4.50051C12.9729 4.59894 13.0009 4.71088 13 4.82465V8H11.8V5.89942L7.60004 8.3733V13.2829L9 12.4583V13.9128L7.29404 14.9173C7.20389 14.9699 7.10295 14.9983 7.00005 15C6.89901 14.9986 6.79995 14.9701 6.71205 14.9173L1.31211 11.7375C1.21832 11.6831 1.13994 11.6032 1.08508 11.5059C1.03023 11.4086 1.0009 11.2976 1.00012 11.1842V4.82465C0.998024 4.71213 1.02414 4.60105 1.07579 4.50272C1.12744 4.4044 1.20278 4.32237 1.29411 4.265L6.69405 1.0852C6.78526 1.02939 6.88873 1 6.99405 1C7.09937 1 7.20283 1.02939 7.29404 1.0852ZM2.2001 10.809L6.40005 13.2829V8.3733L2.2001 5.89942V10.809ZM2.8361 4.82465L7.00005 7.27309L11.164 4.82465L7.00005 2.36984L2.8361 4.82465Z"
            fill="inherit"
        />
        <rect width="5" height="5" transform="translate(10 9)" fill="white" />
        <path
            d="M15 9.705L14.295 9L12.5 10.795L10.705 9L10 9.705L11.795 11.5L10 13.295L10.705 14L12.5 12.205L14.295 14L15 13.295L13.205 11.5L15 9.705Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const MinusIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path d="M12 7.5H4V8.5H12V7.5Z" fill="inherit" />
    </SvgIcon>
);

export const SendIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path
            d="M13.8555 2.14464C13.788 2.07748 13.7028 2.03098 13.6098 2.0106C13.5168 1.99023 13.4199 1.99684 13.3305 2.02964L2.33049 6.02964C2.23562 6.06562 2.15395 6.12962 2.09631 6.21312C2.03868 6.29662 2.00781 6.39568 2.00781 6.49714C2.00781 6.5986 2.03868 6.69766 2.09631 6.78116C2.15395 6.86466 2.23562 6.92866 2.33049 6.96464L7.13049 8.88464L9.05049 13.6846C9.08656 13.7753 9.14839 13.8535 9.22836 13.9095C9.30832 13.9654 9.40292 13.9968 9.50049 13.9996C9.60153 13.9976 9.69957 13.9649 9.78169 13.906C9.86381 13.8471 9.92615 13.7647 9.96049 13.6696L13.9605 2.66964C13.9946 2.58118 14.0028 2.48486 13.9842 2.3919C13.9656 2.29895 13.921 2.2132 13.8555 2.14464ZM9.50049 12.0996L8.10549 8.59964L10.5005 6.20464L9.79549 5.49964L7.38049 7.91464L3.90049 6.49964L12.6655 3.33464L9.50049 12.0996Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ExpandIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="inherit">
        <path d="M10 1V2H13.293L9 6.291L9.707 7L14 2.707V6H15V1H10Z" fill="inherit" />
        <path d="M7 9.708L6.296 9L2 13.293V10H1V15H6V14H2.707L7 9.708Z" fill="inherit" />
    </SvgIcon>
);

export const CollapseIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path d="M2 9V10H5.293L1 14.291L1.707 15L6 10.707V14H7V9H2Z" fill="inherit" />
        <path d="M15 1.708L14.296 1L10 5.293V2H9V7H14V6H10.707L15 1.708Z" fill="inherit" />
    </SvgIcon>
);

export const ReplaceIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.29404 1.0852L12.694 4.265C12.7876 4.32074 12.8653 4.40207 12.9191 4.50051C12.9729 4.59894 13.0009 4.71088 13 4.82465V8H11.8V5.89942L7.60004 8.3733V13.2829L9 12.4583V13.9128L7.29404 14.9173C7.20389 14.9699 7.10295 14.9983 7.00005 15C6.89901 14.9986 6.79995 14.9701 6.71205 14.9173L1.31211 11.7375C1.21832 11.6831 1.13994 11.6032 1.08508 11.5059C1.03023 11.4086 1.0009 11.2976 1.00012 11.1842V4.82465C0.998024 4.71213 1.02414 4.60105 1.07579 4.50272C1.12744 4.4044 1.20278 4.32237 1.29411 4.265L6.69405 1.0852C6.78526 1.02939 6.88873 1 6.99405 1C7.09937 1 7.20283 1.02939 7.29404 1.0852ZM2.2001 10.809L6.40005 13.2829V8.3733L2.2001 5.89942V10.809ZM2.8361 4.82465L7.00005 7.27309L11.164 4.82465L7.00005 2.36984L2.8361 4.82465Z"
            fill="inherit"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.9824 10.0681C12.4076 9.9542 12.8586 9.98376 13.2654 10.1522C13.4705 10.2372 13.6586 10.3551 13.8229 10.5H13V11.5H15.5V9H14.5V9.76393C14.2499 9.5402 13.9623 9.35852 13.648 9.22836C13.0379 8.97564 12.3614 8.9313 11.7235 9.10222C11.0856 9.27315 10.522 9.64978 10.1199 10.1737C9.71791 10.6976 9.5 11.3396 9.5 12C9.5 12.6604 9.71791 13.3024 10.1199 13.8263C10.522 14.3502 11.0856 14.7269 11.7235 14.8978C12.3614 15.0687 13.0379 15.0244 13.6481 14.7716C14.2582 14.5189 14.7679 14.0719 15.0981 13.5L14.2321 13C14.0119 13.3813 13.6721 13.6793 13.2654 13.8478C12.8586 14.0162 12.4076 14.0458 11.9824 13.9319C11.5571 13.8179 11.1813 13.5668 10.9133 13.2175C10.6453 12.8682 10.5 12.4403 10.5 12C10.5 11.5597 10.6453 11.1318 10.9133 10.7825C11.1813 10.4332 11.5571 10.1821 11.9824 10.0681Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const SwapIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.29404 1.0852L12.694 4.265C12.7876 4.32074 12.8653 4.40207 12.9191 4.50051C12.9729 4.59894 13.0009 4.71088 13 4.82465V8H11.8V5.89942L7.60004 8.3733V13.2829L9 12.4583V13.9128L7.29404 14.9173C7.20389 14.9699 7.10295 14.9983 7.00005 15C6.89901 14.9986 6.79995 14.9701 6.71205 14.9173L1.31211 11.7375C1.21832 11.6831 1.13994 11.6032 1.08508 11.5059C1.03023 11.4086 1.0009 11.2976 1.00012 11.1842V4.82465C0.998024 4.71213 1.02414 4.60105 1.07579 4.50272C1.12744 4.4044 1.20278 4.32237 1.29411 4.265L6.69405 1.0852C6.78526 1.02939 6.88873 1 6.99405 1C7.09937 1 7.20283 1.02939 7.29404 1.0852ZM2.2001 10.809L6.40005 13.2829V8.3733L2.2001 5.89942V10.809ZM2.8361 4.82465L7.00005 7.27309L11.164 4.82465L7.00005 2.36984L2.8361 4.82465Z"
            fill="inherit"
        />
        <g clipPath="url(#clip0_1203_170147)">
            <path
                d="M10.5 9.00002H14.0859L13.2929 8.20705L14 7.5L16 9.50002L14 11.5L13.2929 10.793L14.0859 10H10.5V11.5H9.5V10C9.50032 9.7349 9.60578 9.48073 9.79324 9.29326C9.98071 9.10579 10.2349 9.00034 10.5 9.00002Z"
                fill="inherit"
            />
            <path
                d="M12.2071 12.7071L11.4141 13.5H15V12H16V13.5C15.9997 13.7651 15.8942 14.0193 15.7068 14.2068C15.5193 14.3942 15.2651 14.4997 15 14.5H11.4141L12.2071 15.293L11.5001 16.0001L9.5 14L11.5 12L12.2071 12.7071Z"
                fill="inherit"
            />
        </g>
        <defs>
            <clipPath id="clip0_1203_170147">
                <rect width="7" height="9" fill="white" transform="translate(9 7)" />
            </clipPath>
        </defs>
    </SvgIcon>
);

export const AddCAIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="inherit">
        <path d="M15.5 12H13.5V10H12.5V12H10.5V13H12.5V15H13.5V13H15.5V12Z" fill="inherit" />
        <path
            d="M12.5 2.5H11V2C10.9992 1.73502 10.8936 1.48111 10.7063 1.29374C10.5189 1.10637 10.265 1.00077 10 1H6C5.73502 1.00077 5.48111 1.10637 5.29374 1.29374C5.10637 1.48111 5.00077 1.73502 5 2V2.5H3.5C3.23502 2.50077 2.98111 2.60637 2.79374 2.79374C2.60637 2.98111 2.50077 3.23502 2.5 3.5V14C2.50077 14.265 2.60637 14.5189 2.79374 14.7063C2.98111 14.8936 3.23502 14.9992 3.5 15H8.5V14H3.5V3.5H5V5H11V3.5H12.5V8H13.5V3.5C13.4992 3.23502 13.3936 2.98111 13.2063 2.79374C13.0189 2.60637 12.765 2.50077 12.5 2.5ZM10 4H6V2H10V4Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ExpandMoreIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px' }} viewBox="0 0 16 16" fill="none" {...props}>
        <path d="M8 11L3 6.00002L3.7 5.30002L8 9.60002L12.3 5.30002L13 6.00002L8 11Z" fill="inherit" />
    </SvgIcon>
);

export const DocumentIllustrator = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '251px', height: '200px' }} {...props} viewBox="0 0 251 200" fill="none">
        <rect x="0.5" width="250" height="200" fill="white" />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M207.5 65C211.366 65 214.5 68.134 214.5 72C214.5 75.866 211.366 79 207.5 79H167.5C171.366 79 174.5 82.134 174.5 86C174.5 89.866 171.366 93 167.5 93H189.5C193.366 93 196.5 96.134 196.5 100C196.5 103.866 193.366 107 189.5 107H179.326C174.452 107 170.5 110.134 170.5 114C170.5 116.577 172.5 118.911 176.5 121C180.366 121 183.5 124.134 183.5 128C183.5 131.866 180.366 135 176.5 135H93.5C89.634 135 86.5 131.866 86.5 128C86.5 124.134 89.634 121 93.5 121H54.5C50.634 121 47.5 117.866 47.5 114C47.5 110.134 50.634 107 54.5 107H94.5C98.366 107 101.5 103.866 101.5 100C101.5 96.134 98.366 93 94.5 93H69.5C65.634 93 62.5 89.866 62.5 86C62.5 82.134 65.634 79 69.5 79H109.5C105.634 79 102.5 75.866 102.5 72C102.5 68.134 105.634 65 109.5 65H207.5ZM207.5 93C211.366 93 214.5 96.134 214.5 100C214.5 103.866 211.366 107 207.5 107C203.634 107 200.5 103.866 200.5 100C200.5 96.134 203.634 93 207.5 93Z"
            fill="#EBEEF7"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M154.172 64.0007L163.474 131.843L164.31 138.65C164.579 140.842 163.02 142.838 160.827 143.107L102.267 150.298C100.074 150.567 98.0785 149.008 97.8093 146.815L88.7935 73.3874C88.6589 72.2911 89.4386 71.2932 90.5349 71.1586C90.5418 71.1578 90.5488 71.1569 90.5557 71.1562L95.4141 70.6112M99.3426 70.1705L103.93 69.656L99.3426 70.1705Z"
            fill="white"
        />
        <path
            d="M155.411 63.8309C155.317 63.1469 154.686 62.6685 154.003 62.7623C153.319 62.856 152.84 63.4865 152.934 64.1705L155.411 63.8309ZM163.474 131.843L164.715 131.691C164.714 131.685 164.713 131.679 164.713 131.674L163.474 131.843ZM164.31 138.65L165.551 138.497L164.31 138.65ZM160.827 143.107L160.98 144.348L160.827 143.107ZM102.267 150.298L102.419 151.538L102.267 150.298ZM97.8093 146.815L99.05 146.663L97.8093 146.815ZM88.7935 73.3874L87.5529 73.5398L88.7935 73.3874ZM90.5557 71.1562L90.695 72.3984L90.5557 71.1562ZM95.5534 71.8534C96.2394 71.7764 96.7332 71.1579 96.6563 70.4718C96.5793 69.7858 95.9608 69.292 95.2747 69.369L95.5534 71.8534ZM99.2032 68.9283C98.5172 69.0053 98.0234 69.6238 98.1004 70.3098C98.1773 70.9959 98.7959 71.4897 99.4819 71.4127L99.2032 68.9283ZM104.069 70.8982C104.755 70.8212 105.249 70.2027 105.172 69.5166C105.095 68.8306 104.476 68.3368 103.79 68.4137L104.069 70.8982ZM152.934 64.1705L162.236 132.013L164.713 131.674L155.411 63.8309L152.934 64.1705ZM162.234 131.996L163.069 138.802L165.551 138.497L164.715 131.691L162.234 131.996ZM163.069 138.802C163.254 140.31 162.182 141.682 160.675 141.867L160.98 144.348C163.857 143.995 165.904 141.375 165.551 138.497L163.069 138.802ZM160.675 141.867L102.115 149.057L102.419 151.538L160.98 144.348L160.675 141.867ZM102.115 149.057C100.607 149.242 99.2351 148.17 99.05 146.663L96.5686 146.967C96.922 149.845 99.5414 151.892 102.419 151.538L102.115 149.057ZM99.05 146.663L90.0342 73.2351L87.5529 73.5398L96.5686 146.967L99.05 146.663ZM90.0342 73.2351C89.9837 72.824 90.2761 72.4498 90.6872 72.3993L90.3826 69.9179C88.601 70.1367 87.3341 71.7582 87.5529 73.5398L90.0342 73.2351ZM90.6872 72.3993C90.6898 72.399 90.6924 72.3987 90.695 72.3984L90.4163 69.914C90.4051 69.9152 90.3938 69.9165 90.3826 69.9179L90.6872 72.3993ZM90.695 72.3984L95.5534 71.8534L95.2747 69.369L90.4163 69.914L90.695 72.3984ZM99.4819 71.4127L104.069 70.8982L103.79 68.4137L99.2032 68.9283L99.4819 71.4127Z"
            fill="#334466"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M151.64 68.2686L160.059 129.752L160.817 135.921C161.061 137.908 159.667 139.714 157.703 139.955L105.261 146.394C103.297 146.635 101.508 145.22 101.264 143.233L93.1139 76.8562C92.9792 75.7599 93.7589 74.762 94.8552 74.6274L101.343 73.8308"
            fill="#B8C6E5"
        />
        <path
            d="M108.422 54C108.422 52.4812 109.653 51.25 111.172 51.25H156.729C157.458 51.25 158.157 51.5395 158.673 52.0549L172.116 65.4898C172.632 66.0056 172.922 66.7053 172.922 67.4349V130C172.922 131.519 171.691 132.75 170.172 132.75H111.172C109.653 132.75 108.422 131.519 108.422 130V54Z"
            fill="white"
            stroke="#334466"
            strokeWidth="2.5"
        />
        <path
            d="M157.172 52.4023V63.9995C157.172 65.6564 158.515 66.9995 160.172 66.9995H168.105"
            stroke="#334466"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M118.5 118H144.5M118.5 67H144.5H118.5ZM118.5 79H161.5H118.5ZM118.5 92H161.5H118.5ZM118.5 105H161.5H118.5Z"
            stroke="#B8C6E5"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </SvgIcon>
);

export const CenterIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px' }} viewBox="0 0 16 16" fill="none">
        <path
            d="M11 12H5C4.73488 11.9997 4.4807 11.8942 4.29323 11.7068C4.10576 11.5193 4.0003 11.2651 4 11V5C4.0003 4.73488 4.10576 4.4807 4.29323 4.29323C4.4807 4.10576 4.73488 4.0003 5 4H11C11.2651 4.0003 11.5193 4.10576 11.7068 4.29323C11.8942 4.4807 11.9997 4.73488 12 5V11C11.9997 11.2651 11.8942 11.5193 11.7068 11.7068C11.5193 11.8942 11.2651 11.9997 11 12ZM5 5V11H11V5H5Z"
            fill="inherit"
        />
        <path d="M5.5 1H1V5.5H2V2H5.5V1Z" fill="inherit" />
        <path d="M1 10.5V15H5.5V14H2V10.5H1Z" fill="inherit" />
        <path d="M15 5.5V1H10.5V2H14V5.5H15Z" fill="inherit" />
        <path d="M10.5 15H15V10.5H14V14H10.5V15Z" fill="inherit" />
    </SvgIcon>
);

export const DownloadIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path
            d="M13 12V14H3V12H2V14C2 14.2652 2.10536 14.5196 2.29289 14.7071C2.48043 14.8946 2.73478 15 3 15H13C13.2652 15 13.5196 14.8946 13.7071 14.7071C13.8946 14.5196 14 14.2652 14 14V12H13Z"
            fill="inherit"
        />
        <path d="M13 7L12.295 6.295L8.5 10.085V1H7.5V10.085L3.705 6.295L3 7L8 12L13 7Z" fill="inherit" />
    </SvgIcon>
);
export const WarningIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} viewBox="0 0 24 24" fill="none">
        <path
            d="M12.0015 4.62856H11.9985L3.48653 20.9975L3.48796 21H20.5121L20.5135 20.9975L12.0015 4.62856ZM11.1563 9.00001H12.8438V15.75H11.1563V9.00001ZM12 19.5C11.7775 19.5 11.56 19.434 11.375 19.3104C11.19 19.1868 11.0458 19.0111 10.9606 18.8055C10.8755 18.6 10.8532 18.3738 10.8966 18.1555C10.94 17.9373 11.0472 17.7369 11.2045 17.5795C11.3618 17.4222 11.5623 17.315 11.7805 17.2716C11.9988 17.2282 12.225 17.2505 12.4305 17.3356C12.6361 17.4208 12.8118 17.565 12.9354 17.75C13.059 17.935 13.125 18.1525 13.125 18.375C13.125 18.6734 13.0065 18.9595 12.7955 19.1705C12.5845 19.3815 12.2984 19.5 12 19.5Z"
            fill="#E9CB0A"
        />
        <path
            d="M21.75 22.5H2.25001C2.12115 22.5 1.99447 22.4668 1.88218 22.4036C1.76988 22.3404 1.67576 22.2494 1.60889 22.1392C1.54203 22.0291 1.50466 21.9036 1.50041 21.7748C1.49615 21.646 1.52515 21.5183 1.58461 21.404L11.3346 2.65396C11.3979 2.53219 11.4935 2.43013 11.6108 2.3589C11.7281 2.28767 11.8627 2.25 12 2.25C12.1373 2.25 12.2719 2.28767 12.3892 2.3589C12.5065 2.43013 12.6021 2.53219 12.6654 2.65396L22.4154 21.404C22.4749 21.5183 22.5039 21.646 22.4996 21.7748C22.4953 21.9036 22.458 22.0291 22.3911 22.1392C22.3242 22.2494 22.2301 22.3404 22.1178 22.4036C22.0055 22.4668 21.8789 22.5 21.75 22.5ZM3.48796 21H20.5121L20.5135 20.9975L12.0015 4.62856H11.9985L3.48653 20.9975L3.48796 21Z"
            fill="#E9CB0A"
        />
    </SvgIcon>
);

export const ImageIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path
            d="M9.5 7C9.79667 7 10.0867 6.91203 10.3334 6.7472C10.58 6.58238 10.7723 6.34811 10.8858 6.07403C10.9994 5.79994 11.0291 5.49834 10.9712 5.20736C10.9133 4.91639 10.7704 4.64912 10.5607 4.43934C10.3509 4.22956 10.0836 4.0867 9.79264 4.02882C9.50166 3.97094 9.20006 4.00065 8.92597 4.11418C8.65189 4.22771 8.41762 4.41997 8.2528 4.66664C8.08797 4.91332 8 5.20333 8 5.5C8 5.89782 8.15804 6.27936 8.43934 6.56066C8.72064 6.84196 9.10218 7 9.5 7ZM9.5 5C9.59889 5 9.69556 5.02932 9.77779 5.08427C9.86001 5.13921 9.9241 5.2173 9.96194 5.30866C9.99978 5.40002 10.0097 5.50055 9.99039 5.59755C9.9711 5.69454 9.92348 5.78363 9.85355 5.85355C9.78363 5.92348 9.69454 5.9711 9.59755 5.99039C9.50056 6.00969 9.40002 5.99978 9.30866 5.96194C9.2173 5.9241 9.13921 5.86001 9.08426 5.77779C9.02932 5.69556 9 5.59889 9 5.5C9 5.36739 9.05268 5.24021 9.14645 5.14645C9.24021 5.05268 9.36739 5 9.5 5Z"
            fill="inherit"
        />
        <path
            d="M13 2H3C2.73478 2 2.48043 2.10536 2.29289 2.29289C2.10536 2.48043 2 2.73478 2 3V13C2 13.2652 2.10536 13.5196 2.29289 13.7071C2.48043 13.8946 2.73478 14 3 14H13C13.2652 14 13.5196 13.8946 13.7071 13.7071C13.8946 13.5196 14 13.2652 14 13V3C14 2.73478 13.8946 2.48043 13.7071 2.29289C13.5196 2.10536 13.2652 2 13 2ZM13 13H3V10L5.5 7.5L8.295 10.295C8.48236 10.4813 8.73581 10.5858 9 10.5858C9.26419 10.5858 9.51764 10.4813 9.705 10.295L10.5 9.5L13 12V13ZM13 10.585L11.205 8.79C11.0176 8.60375 10.7642 8.49921 10.5 8.49921C10.2358 8.49921 9.98236 8.60375 9.795 8.79L9 9.585L6.205 6.79C6.01764 6.60375 5.76419 6.49921 5.5 6.49921C5.23581 6.49921 4.98236 6.60375 4.795 6.79L3 8.585V3H13V10.585Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const TaskIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path d="M7 10.09L5.205 8.295L4.5 9L7 11.5L11.5 7L10.795 6.29L7 10.09Z" fill="inherit" />
        <path
            d="M12.5 2.5H11V2C11 1.73478 10.8946 1.48043 10.7071 1.29289C10.5196 1.10536 10.2652 1 10 1H6C5.73478 1 5.48043 1.10536 5.29289 1.29289C5.10536 1.48043 5 1.73478 5 2V2.5H3.5C3.23478 2.5 2.98043 2.60536 2.79289 2.79289C2.60536 2.98043 2.5 3.23478 2.5 3.5V14C2.5 14.2652 2.60536 14.5196 2.79289 14.7071C2.98043 14.8946 3.23478 15 3.5 15H12.5C12.7652 15 13.0196 14.8946 13.2071 14.7071C13.3946 14.5196 13.5 14.2652 13.5 14V3.5C13.5 3.23478 13.3946 2.98043 13.2071 2.79289C13.0196 2.60536 12.7652 2.5 12.5 2.5ZM6 2H10V4H6V2ZM12.5 14H3.5V3.5H5V5H11V3.5H12.5V14Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ListViewIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 14 14" fill="none">
        <path
            d="M12.824 0H1.176C0.8642 0.000308866 0.565259 0.124308 0.344784 0.344784C0.124308 0.565259 0.000308866 0.8642 0 1.176L0 12.824C0.000308866 13.1358 0.124308 13.4347 0.344784 13.6552C0.565259 13.8757 0.8642 13.9997 1.176 14H12.824C13.1358 13.9997 13.4347 13.8757 13.6552 13.6552C13.8757 13.4347 13.9997 13.1358 14 12.824V1.176C13.9997 0.8642 13.8757 0.565259 13.6552 0.344784C13.4347 0.124308 13.1358 0.000308866 12.824 0ZM13.4167 12.824C13.4165 12.9811 13.354 13.1318 13.2429 13.2429C13.1318 13.354 12.9811 13.4165 12.824 13.4167H1.176C1.01886 13.4165 0.868205 13.354 0.757092 13.2429C0.645979 13.1318 0.583488 12.9811 0.583333 12.824V1.176C0.583488 1.01886 0.645979 0.868205 0.757092 0.757092C0.868205 0.645979 1.01886 0.583488 1.176 0.583333H12.824C12.9811 0.583488 13.1318 0.645979 13.2429 0.757092C13.354 0.868205 13.4165 1.01886 13.4167 1.176V12.824ZM4.375 3.5C4.375 3.57735 4.34427 3.65154 4.28957 3.70624C4.23487 3.76094 4.16069 3.79167 4.08333 3.79167H2.91667C2.83931 3.79167 2.76513 3.76094 2.71043 3.70624C2.65573 3.65154 2.625 3.57735 2.625 3.5C2.625 3.42265 2.65573 3.34846 2.71043 3.29376C2.76513 3.23906 2.83931 3.20833 2.91667 3.20833H4.08333C4.16069 3.20833 4.23487 3.23906 4.28957 3.29376C4.34427 3.34846 4.375 3.42265 4.375 3.5ZM4.375 5.83333C4.375 5.91069 4.34427 5.98487 4.28957 6.03957C4.23487 6.09427 4.16069 6.125 4.08333 6.125H2.91667C2.83931 6.125 2.76513 6.09427 2.71043 6.03957C2.65573 5.98487 2.625 5.91069 2.625 5.83333C2.625 5.75598 2.65573 5.68179 2.71043 5.62709C2.76513 5.5724 2.83931 5.54167 2.91667 5.54167H4.08333C4.16069 5.54167 4.23487 5.5724 4.28957 5.62709C4.34427 5.68179 4.375 5.75598 4.375 5.83333ZM4.375 8.16667C4.375 8.24402 4.34427 8.31821 4.28957 8.37291C4.23487 8.4276 4.16069 8.45833 4.08333 8.45833H2.91667C2.83931 8.45833 2.76513 8.4276 2.71043 8.37291C2.65573 8.31821 2.625 8.24402 2.625 8.16667C2.625 8.08931 2.65573 8.01513 2.71043 7.96043C2.76513 7.90573 2.83931 7.875 2.91667 7.875H4.08333C4.16069 7.875 4.23487 7.90573 4.28957 7.96043C4.34427 8.01513 4.375 8.08931 4.375 8.16667ZM4.375 10.5C4.375 10.5774 4.34427 10.6515 4.28957 10.7062C4.23487 10.7609 4.16069 10.7917 4.08333 10.7917H2.91667C2.83931 10.7917 2.76513 10.7609 2.71043 10.7062C2.65573 10.6515 2.625 10.5774 2.625 10.5C2.625 10.4226 2.65573 10.3485 2.71043 10.2938C2.76513 10.2391 2.83931 10.2083 2.91667 10.2083H4.08333C4.16069 10.2083 4.23487 10.2391 4.28957 10.2938C4.34427 10.3485 4.375 10.4226 4.375 10.5ZM11.375 3.5C11.375 3.57735 11.3443 3.65154 11.2896 3.70624C11.2349 3.76094 11.1607 3.79167 11.0833 3.79167H6.41667C6.33931 3.79167 6.26512 3.76094 6.21043 3.70624C6.15573 3.65154 6.125 3.57735 6.125 3.5C6.125 3.42265 6.15573 3.34846 6.21043 3.29376C6.26512 3.23906 6.33931 3.20833 6.41667 3.20833H11.0833C11.1607 3.20833 11.2349 3.23906 11.2896 3.29376C11.3443 3.34846 11.375 3.42265 11.375 3.5ZM11.375 5.83333C11.375 5.91069 11.3443 5.98487 11.2896 6.03957C11.2349 6.09427 11.1607 6.125 11.0833 6.125H6.41667C6.33931 6.125 6.26512 6.09427 6.21043 6.03957C6.15573 5.98487 6.125 5.91069 6.125 5.83333C6.125 5.75598 6.15573 5.68179 6.21043 5.62709C6.26512 5.5724 6.33931 5.54167 6.41667 5.54167H11.0833C11.1607 5.54167 11.2349 5.5724 11.2896 5.62709C11.3443 5.68179 11.375 5.75598 11.375 5.83333ZM11.375 8.16667C11.375 8.24402 11.3443 8.31821 11.2896 8.37291C11.2349 8.4276 11.1607 8.45833 11.0833 8.45833H6.41667C6.33931 8.45833 6.26512 8.4276 6.21043 8.37291C6.15573 8.31821 6.125 8.24402 6.125 8.16667C6.125 8.08931 6.15573 8.01513 6.21043 7.96043C6.26512 7.90573 6.33931 7.875 6.41667 7.875H11.0833C11.1607 7.875 11.2349 7.90573 11.2896 7.96043C11.3443 8.01513 11.375 8.08931 11.375 8.16667ZM11.375 10.5C11.375 10.5774 11.3443 10.6515 11.2896 10.7062C11.2349 10.7609 11.1607 10.7917 11.0833 10.7917H6.41667C6.33931 10.7917 6.26512 10.7609 6.21043 10.7062C6.15573 10.6515 6.125 10.5774 6.125 10.5C6.125 10.4226 6.15573 10.3485 6.21043 10.2938C6.26512 10.2391 6.33931 10.2083 6.41667 10.2083H11.0833C11.1607 10.2083 11.2349 10.2391 11.2896 10.2938C11.3443 10.3485 11.375 10.4226 11.375 10.5Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const TileViewIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path
            d="M1.83333 7.5H7.16667C7.35 7.5 7.5 7.35 7.5 7.16667V1.83333C7.5 1.65 7.35 1.5 7.16667 1.5H1.83333C1.65 1.5 1.5 1.65 1.5 1.83333V7.16667C1.5 7.35 1.65 7.5 1.83333 7.5ZM2.16667 2.16667H6.83333V6.83333H2.16667V2.16667Z"
            fill="inherit"
        />
        <path
            d="M1.83333 14.5H7.16667C7.35 14.5 7.5 14.35 7.5 14.1667V8.83333C7.5 8.65 7.35 8.5 7.16667 8.5H1.83333C1.65 8.5 1.5 8.65 1.5 8.83333V14.1667C1.5 14.35 1.65 14.5 1.83333 14.5ZM2.16667 9.16667H6.83333V13.8333H2.16667V9.16667Z"
            fill="inherit"
        />
        <path
            d="M8.83333 7.5H14.1667C14.35 7.5 14.5 7.35 14.5 7.16667V1.83333C14.5 1.65 14.35 1.5 14.1667 1.5H8.83333C8.65 1.5 8.5 1.65 8.5 1.83333V7.16667C8.5 7.35 8.65 7.5 8.83333 7.5ZM9.16667 2.16667H13.8333V6.83333H9.16667V2.16667Z"
            fill="inherit"
        />
        <path
            d="M8.83333 14.5H14.1667C14.35 14.5 14.5 14.35 14.5 14.1667V8.83333C14.5 8.65 14.35 8.5 14.1667 8.5H8.83333C8.65 8.5 8.5 8.65 8.5 8.83333V14.1667C8.5 14.35 8.65 14.5 8.83333 14.5ZM9.16667 9.16667H13.8333V13.8333H9.16667V9.16667Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ProjectIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '24px', height: '24px' }} {...props} viewBox="0 0 24 24" fill="none">
        <path d="M10.5 13.5H6V15H10.5V13.5Z" fill="inherit" />
        <path d="M13.5 16.5H6V18H13.5V16.5Z" fill="inherit" />
        <path
            d="M19.5 3H4.5C4.10233 3.0005 3.72109 3.15869 3.43989 3.43989C3.15869 3.72109 3.0005 4.10233 3 4.5V19.5C3.0005 19.8977 3.15869 20.2789 3.43989 20.5601C3.72109 20.8413 4.10233 20.9995 4.5 21H19.5C19.8977 20.9995 20.2789 20.8413 20.5601 20.5601C20.8413 20.2789 20.9995 19.8977 21 19.5V4.5C20.9995 4.10233 20.8413 3.72109 20.5601 3.43989C20.2789 3.15869 19.8977 3.0005 19.5 3ZM13.5 4.5V7.5H10.5V4.5H13.5ZM4.5 19.5V4.5H9V9H15V4.5H19.5L19.5009 19.5H4.5Z"
            fill="inherit"
        />
    </SvgIcon>
);
export const NotificationIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ height: '18px', width: '18px' }}
            {...props}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M14.3535 9.6465L13 8.29295V6.5C12.9984 5.26098 12.5374 4.06655 11.7062 3.1477C10.875 2.22884 9.73266 1.65082 8.5 1.5254V0.5H7.5V1.5254C6.26734 1.65082 5.12496 2.22884 4.29377 3.1477C3.46257 4.06655 3.0016 5.26098 3 6.5V8.29295L1.64645 9.6465C1.55269 9.74025 1.50002 9.86741 1.5 10V11.5C1.5 11.6326 1.55268 11.7598 1.64645 11.8536C1.74021 11.9473 1.86739 12 2 12H5.5V12.3884C5.48916 13.0227 5.71276 13.6387 6.12794 14.1183C6.54313 14.598 7.1207 14.9076 7.75 14.9878C8.09757 15.0222 8.44852 14.9836 8.78026 14.8743C9.112 14.765 9.4172 14.5875 9.67622 14.3532C9.93524 14.1189 10.1423 13.8329 10.2842 13.5138C10.4261 13.1946 10.4996 12.8493 10.5 12.5V12H14C14.1326 12 14.2598 11.9473 14.3536 11.8536C14.4473 11.7598 14.5 11.6326 14.5 11.5V10C14.5 9.86741 14.4473 9.74025 14.3535 9.6465ZM9.5 12.5C9.5 12.8978 9.34196 13.2794 9.06066 13.5607C8.77936 13.842 8.39782 14 8 14C7.60218 14 7.22064 13.842 6.93934 13.5607C6.65804 13.2794 6.5 12.8978 6.5 12.5V12H9.5V12.5ZM13.5 11H2.5V10.2071L3.8535 8.8535C3.94727 8.75975 3.99997 8.6326 4 8.5V6.5C4 5.43913 4.42143 4.42172 5.17157 3.67157C5.92172 2.92143 6.93913 2.5 8 2.5C9.06087 2.5 10.0783 2.92143 10.8284 3.67157C11.5786 4.42172 12 5.43913 12 6.5V8.5C12 8.6326 12.0527 8.75975 12.1465 8.8535L13.5 10.2071V11Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const LogOutIcon = (props: SvgIconProps) => {
    return (
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M1.59374 7.43291H9.17894C9.42104 7.43291 9.61672 7.23691 9.61672 6.99541C9.61672 6.75391 9.42104 6.55791 9.17894 6.55791H1.6211L3.20477 4.97525C3.37573 4.80441 3.37573 4.52747 3.20477 4.35663C3.03382 4.18578 2.7567 4.18578 2.58575 4.35663L0 7.00897L2.58575 9.66131C2.67134 9.74684 2.78319 9.7895 2.89526 9.7895C3.00733 9.7895 3.11919 9.74684 3.20477 9.66131C3.37573 9.49047 3.37573 9.21353 3.20477 9.04269L1.59374 7.43291ZM13.1242 0H5.68194C5.19819 0 4.80638 0.391781 4.80638 0.875V4.8125H5.68763V1.40853C5.68763 1.11628 5.92491 0.879156 6.21734 0.879156H12.575C12.8677 0.879156 13.1047 1.11628 13.1047 1.40853L13.1185 12.5961C13.1185 12.8883 12.8814 13.1254 12.5888 13.1254H6.21758C5.92514 13.1254 5.68786 12.8883 5.68786 12.5961V9.17306L4.80661 9.17416V13.125C4.80661 13.6082 5.19842 14 5.68217 14H13.1242C13.608 14 14 13.6082 14 13.125V0.875C13.9998 0.391781 13.6078 0 13.1242 0H13.1242Z"
                fill="inherit"
            />
        </svg>
    );
};

export const SearchIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path
            d="M14.5 13.7931L10.7239 10.017C11.6313 8.9277 12.0838 7.5305 11.9872 6.11608C11.8907 4.70165 11.2525 3.37891 10.2055 2.423C9.15855 1.4671 7.78335 0.951637 6.366 0.983845C4.94865 1.01605 3.59828 1.59345 2.59581 2.59593C1.59333 3.5984 1.01593 4.94877 0.983723 6.36612C0.951515 7.78347 1.46698 9.15867 2.42288 10.2057C3.37879 11.2526 4.70153 11.8908 6.11596 11.9873C7.53038 12.0839 8.92758 11.6314 10.0169 10.7241L13.7929 14.5001L14.5 13.7931ZM2 6.50012C2 5.6101 2.26392 4.74007 2.75838 4.00005C3.25285 3.26003 3.95565 2.68325 4.77792 2.34266C5.60019 2.00207 6.50499 1.91295 7.3779 2.08658C8.25082 2.26022 9.05264 2.6888 9.68198 3.31814C10.3113 3.94747 10.7399 4.7493 10.9135 5.62221C11.0872 6.49513 10.998 7.39993 10.6575 8.22219C10.3169 9.04446 9.74008 9.74726 9.00006 10.2417C8.26004 10.7362 7.39001 11.0001 6.5 11.0001C5.30693 10.9988 4.1631 10.5243 3.31948 9.68064C2.47585 8.83701 2.00132 7.69319 2 6.50012Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const DragIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path d="M7 3H5V5H7V3Z" fill="inherit" />
        <path d="M11 3H9V5H11V3Z" fill="inherit" />
        <path d="M7 7H5V9H7V7Z" fill="inherit" />
        <path d="M11 7H9V9H11V7Z" fill="inherit" />
        <path d="M7 11H5V13H7V11Z" fill="inherit" />
        <path d="M11 11H9V13H11V11Z" fill="inherit" />
    </SvgIcon>
);

export const MailIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path
            d="M14 3H2C1.73478 3 1.48043 3.10536 1.29289 3.29289C1.10536 3.48043 1 3.73478 1 4V12C1 12.2652 1.10536 12.5196 1.29289 12.7071C1.48043 12.8946 1.73478 13 2 13H14C14.2652 13 14.5196 12.8946 14.7071 12.7071C14.8946 12.5196 15 12.2652 15 12V4C15 3.73478 14.8946 3.48043 14.7071 3.29289C14.5196 3.10536 14.2652 3 14 3ZM12.9 4L8 7.39L3.1 4H12.9ZM2 12V4.455L7.715 8.41C7.7987 8.46806 7.89813 8.49918 8 8.49918C8.10187 8.49918 8.2013 8.46806 8.285 8.41L14 4.455V12H2Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const UploadIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path d="M3 9L3.705 9.705L7.5 5.915V15H8.5V5.915L12.295 9.705L13 9L8 4L3 9Z" fill="inherit" />
        <path
            d="M3 4V2H13V4H14V2C14 1.73478 13.8946 1.48043 13.7071 1.29289C13.5196 1.10536 13.2652 1 13 1H3C2.73478 1 2.48043 1.10536 2.29289 1.29289C2.10536 1.48043 2 1.73478 2 2V4H3Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const LinkIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            {...props}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_4267_62462)">
                <path
                    d="M15.1243 3.38041C14.8455 3.10073 14.5144 2.87882 14.1497 2.72741C13.7851 2.57599 13.3941 2.49805 12.9993 2.49805C12.6044 2.49805 12.2135 2.57599 11.8488 2.72741C11.4841 2.87882 11.153 3.10073 10.8743 3.38041L11.5843 4.09041C11.7704 3.90426 11.9914 3.7566 12.2346 3.65586C12.4778 3.55512 12.7385 3.50326 13.0018 3.50326C13.265 3.50326 13.5257 3.55512 13.7689 3.65586C14.0121 3.7566 14.2331 3.90426 14.4193 4.09041C14.6054 4.27656 14.7531 4.49755 14.8538 4.74077C14.9545 4.98398 15.0064 5.24466 15.0064 5.50791C15.0064 5.77117 14.9545 6.03184 14.8538 6.27506C14.7531 6.51827 14.6054 6.73926 14.4193 6.92541L10.4193 10.9254C10.044 11.3014 9.53472 11.5128 9.00352 11.5133C8.47232 11.5138 7.9627 11.3032 7.58675 10.9279C7.21081 10.5526 6.99934 10.0434 6.99887 9.51218C6.9984 8.98098 7.20897 8.47136 7.58425 8.09541L8.28925 7.38541L7.58425 6.67541L6.87425 7.38541C6.59457 7.66412 6.37266 7.9953 6.22125 8.35996C6.06983 8.72461 5.99189 9.11557 5.99189 9.51041C5.99189 9.90525 6.06983 10.2962 6.22125 10.6609C6.37266 11.0255 6.59457 11.3567 6.87425 11.6354C7.44 12.1939 8.20429 12.505 8.99925 12.5004C9.39561 12.502 9.78836 12.4251 10.1548 12.2741C10.5213 12.123 10.8542 11.9009 11.1343 11.6204L15.1343 7.62041C15.6947 7.05659 16.0085 6.29335 16.0066 5.49834C16.0047 4.70334 15.6874 3.94159 15.1243 3.38041Z"
                    fill="inherit"
                />
                <path
                    d="M2.59425 12.4104C2.40755 12.2246 2.2594 12.0037 2.1583 11.7604C2.05721 11.5172 2.00517 11.2563 2.00517 10.9929C2.00517 10.7295 2.05721 10.4687 2.1583 10.2254C2.2594 9.98214 2.40755 9.76125 2.59425 9.57541L6.59425 5.57541C6.78009 5.38871 7.00098 5.24056 7.24424 5.13946C7.48749 5.03837 7.74833 4.98633 8.01175 4.98633C8.27518 4.98633 8.53601 5.03837 8.77927 5.13946C9.02253 5.24056 9.24342 5.38871 9.42925 5.57541C9.61478 5.76271 9.76082 5.98535 9.85873 6.23012C9.95664 6.4749 10.0044 6.73683 9.99925 7.00041C10.0008 7.26481 9.94986 7.5269 9.84945 7.77149C9.74903 8.01609 9.60112 8.23835 9.41425 8.42541L8.35425 9.50041L9.06425 10.2104L10.1243 9.15041C10.6885 8.58616 11.0055 7.82088 11.0055 7.02291C11.0055 6.22495 10.6885 5.45966 10.1243 4.89541C9.56 4.33116 8.79472 4.01417 7.99675 4.01417C7.19878 4.01417 6.4335 4.33116 5.86925 4.89541L1.86925 8.89541C1.58882 9.17421 1.36627 9.5057 1.21441 9.87082C1.06255 10.2359 0.984375 10.6275 0.984375 11.0229C0.984375 11.4184 1.06255 11.8099 1.21441 12.175C1.36627 12.5401 1.58882 12.8716 1.86925 13.1504C2.43866 13.7047 3.2047 14.0103 3.99925 14.0004C4.8008 14.0012 5.57011 13.6848 6.13925 13.1204L5.42925 12.4104C5.24342 12.5971 5.02253 12.7453 4.77927 12.8464C4.53601 12.9475 4.27518 12.9995 4.01175 12.9995C3.74833 12.9995 3.48749 12.9475 3.24424 12.8464C3.00098 12.7453 2.78009 12.5971 2.59425 12.4104Z"
                    fill="inherit"
                />
            </g>
        </SvgIcon>
    );
};

export const GroupContracted = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            {...props}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect y="0.5" width="16" height="16" rx="1" fill="inherit" />
            <path d="M8.5 8V4.5H7.5V8H4V9H7.5V12.5H8.5V9H12V8H8.5Z" fill="white" />
        </SvgIcon>
    );
};

export const GroupExpanded = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            {...props}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect y="0.5" width="16" height="16" rx="1" fill="#434343" />
            <path d="M12 8H4V9H12V8Z" fill="white" />
        </SvgIcon>
    );
};

export const LockIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
            <path
                d="M12 7H11V4C11 3.20435 10.6839 2.44129 10.1213 1.87868C9.55871 1.31607 8.79565 1 8 1C7.20435 1 6.44129 1.31607 5.87868 1.87868C5.31607 2.44129 5 3.20435 5 4V7H4C3.73478 7 3.48043 7.10536 3.29289 7.29289C3.10536 7.48043 3 7.73478 3 8V14C3 14.2652 3.10536 14.5196 3.29289 14.7071C3.48043 14.8946 3.73478 15 4 15H12C12.2652 15 12.5196 14.8946 12.7071 14.7071C12.8946 14.5196 13 14.2652 13 14V8C13 7.73478 12.8946 7.48043 12.7071 7.29289C12.5196 7.10536 12.2652 7 12 7ZM6 4C6 3.46957 6.21071 2.96086 6.58579 2.58579C6.96086 2.21071 7.46957 2 8 2C8.53043 2 9.03914 2.21071 9.41421 2.58579C9.78929 2.96086 10 3.46957 10 4V7H6V4ZM12 14H4V8H12V14Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const DeleteIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none" {...props}>
            <path d="M7 6H6V12H7V6Z" fill="inherit" />
            <path d="M10 6H9V12H10V6Z" fill="inherit" />
            <path
                d="M2 3V4H3V14C3 14.2652 3.10536 14.5196 3.29289 14.7071C3.48043 14.8946 3.73478 15 4 15H12C12.2652 15 12.5196 14.8946 12.7071 14.7071C12.8946 14.5196 13 14.2652 13 14V4H14V3H2ZM4 14V4H12V14H4Z"
                fill="inherit"
            />
            <path d="M10 1H6V2H10V1Z" fill="inherit" />
        </SvgIcon>
    );
};

export const MarkerIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
            <path
                d="M8.00001 1C6.54186 1.00177 5.14394 1.5818 4.11288 2.61287C3.08181 3.64394 2.50178 5.04185 2.50001 6.5C2.49828 7.69163 2.88758 8.85092 3.60816 9.8C3.60816 9.8 3.75816 9.99725 3.78226 10.0259L8.00001 15L12.2195 10.0237C12.2417 9.99705 12.3919 9.7998 12.3919 9.7998L12.3926 9.7986C13.1128 8.84987 13.5018 7.69109 13.5 6.5C13.4982 5.04185 12.9182 3.64394 11.8871 2.61287C10.8561 1.5818 9.45815 1.00177 8.00001 1ZM11 7.5C10.9997 7.76512 10.8942 8.0193 10.7068 8.20677C10.5193 8.39424 10.2651 8.4997 10 8.5H9.26761L8.41601 9.77735L7.58401 9.22265L8.73241 7.5H10V5H6.00001V7.5H7.00001V8.5H6.00001C5.73488 8.4997 5.48071 8.39424 5.29324 8.20677C5.10576 8.0193 5.00031 7.76512 5.00001 7.5V5C5.00031 4.73488 5.10576 4.4807 5.29324 4.29323C5.48071 4.10576 5.73488 4.0003 6.00001 4H10C10.2651 4.0003 10.5193 4.10576 10.7068 4.29323C10.8942 4.4807 10.9997 4.73488 11 5V7.5Z"
                fill="currentColor"
            />
        </SvgIcon>
    );
};

export const FilterIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
            <path
                d="M9 14H7C6.73478 14 6.48043 13.8946 6.29289 13.7071C6.10536 13.5196 6 13.2652 6 13V9.205L2.295 5.5C2.10721 5.31332 2.00112 5.05979 2 4.795V3C2 2.73478 2.10536 2.48043 2.29289 2.29289C2.48043 2.10536 2.73478 2 3 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289C13.8946 2.48043 14 2.73478 14 3V4.795C13.9989 5.05979 13.8928 5.31332 13.705 5.5L10 9.205V13C10 13.2652 9.89464 13.5196 9.70711 13.7071C9.51957 13.8946 9.26522 14 9 14ZM3 3V4.795L7 8.795V13H9V8.795L13 4.795V3H3Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const Increase = () => {
    return (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M10 4V5H13.2929L9 9.29295L6.8535 7.1465C6.80709 7.10005 6.75199 7.0632 6.69133 7.03806C6.63067 7.01292 6.56566 6.99998 6.5 6.99998C6.43434 6.99998 6.36933 7.01292 6.30867 7.03806C6.24801 7.0632 6.19291 7.10005 6.1465 7.1465L1 12.2929L1.70705 13L6.5 8.20705L8.6465 10.3535C8.69291 10.3999 8.74801 10.4368 8.80867 10.4619C8.86932 10.4871 8.93434 10.5 9 10.5C9.06566 10.5 9.13068 10.4871 9.19133 10.4619C9.25199 10.4368 9.30709 10.3999 9.3535 10.3535L14 5.70705V9H15V4H10Z"
                fill="#161616"
            />
        </svg>
    );
};

export const Decrease = () => {
    return (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M10 13V12H13.2929L9 7.70705L6.8535 9.8535C6.80709 9.89995 6.75199 9.9368 6.69133 9.96194C6.63067 9.98708 6.56566 10 6.5 10C6.43434 10 6.36933 9.98708 6.30867 9.96194C6.24801 9.9368 6.19291 9.89995 6.1465 9.8535L1 4.70705L1.70705 4L6.5 8.79295L8.6465 6.6465C8.69291 6.60005 8.74801 6.5632 8.80867 6.53806C8.86932 6.51292 8.93434 6.49998 9 6.49998C9.06566 6.49998 9.13068 6.51292 9.19133 6.53806C9.25199 6.5632 9.30709 6.60005 9.3535 6.6465L14 11.293V8H15V13H10Z"
                fill="#161616"
            />
        </svg>
    );
};

export const ArrowRight = (props: SvgIconProps) => {
    return (
        <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M9 3L8.285 3.6965L12.075 7.5H2V8.5H12.075L8.285 12.2865L9 13L14 8L9 3Z"
                fill={props.fill ?? '#161616'}
            />
        </SvgIcon>
    );
};

export const ComponentLifecycle = (props: SvgIconProps) => {
    return (
        <SvgIcon style={{ width: '16px', height: '16px' }} {...props} viewBox="0 0 16 16" fill="none">
            <path
                d="M3.78835 11.0342L3.02245 11.6768C2.53616 11.0956 2.15541 10.4337 1.89745 9.7212L2.8362 9.3795C3.05451 9.98245 3.37677 10.5425 3.78835 11.0342Z"
                fill="inherit"
            />
            <path
                d="M6.8308 2.1281C6.7512 2.1454 6.6736 2.1681 6.5952 2.18885C6.48865 2.21695 6.3823 2.24575 6.27845 2.27995C6.1963 2.3073 6.11595 2.3383 6.0355 2.36905C5.9392 2.40615 5.84375 2.4445 5.7505 2.4865C5.66995 2.52285 5.59085 2.5615 5.51235 2.6015C5.42312 2.64693 5.33542 2.69472 5.24925 2.74485C5.17275 2.7895 5.09695 2.83485 5.0227 2.88275C4.93875 2.9372 4.8573 2.99485 4.7766 3.0534C4.7063 3.1047 4.63575 3.15525 4.5681 3.20965C4.4873 3.27435 4.4103 3.34345 4.3331 3.4128C4.27185 3.4678 4.2092 3.5212 4.1505 3.5788C4.0671 3.6606 3.98935 3.74775 3.91125 3.83465C3.00341 4.83972 2.50057 6.14577 2.5 7.50015H1.5C1.4992 5.97203 2.03945 4.49298 3.025 3.32515L3.02265 3.3232C3.06855 3.2685 3.12115 3.22015 3.1689 3.16695C3.26095 3.0644 3.3525 2.96195 3.45065 2.86545C3.51975 2.79755 3.59345 2.7348 3.66565 2.6701C3.7572 2.58785 3.8485 2.5058 3.9447 2.4289C4.0243 2.36495 4.1069 2.3056 4.18945 2.24555C4.2855 2.17555 4.3822 2.10715 4.48215 2.04245C4.5693 1.98605 4.6583 1.93305 4.74825 1.88055C4.85095 1.82102 4.95517 1.76422 5.0609 1.71015C5.1532 1.66305 5.2459 1.61785 5.3409 1.57515C5.4521 1.52515 5.5654 1.47945 5.6799 1.4355C5.7746 1.39935 5.86875 1.36275 5.96555 1.331C6.0887 1.29025 6.21455 1.2563 6.3409 1.22285C6.4333 1.1982 6.5246 1.1716 6.6186 1.1513C6.7636 1.1196 6.91185 1.0976 7.06025 1.0759C7.13975 1.0644 7.2175 1.0483 7.29795 1.0395C8.36566 0.920946 9.44608 1.07026 10.4416 1.47395C11.4371 1.87764 12.3164 2.52299 13 3.3517V1.50015H14V5.50015H10V4.50015H12.5943C12.0975 3.73358 11.417 3.10333 10.6146 2.66672C9.81224 2.23011 8.91347 2.00097 8 2.00015C7.80138 2.00067 7.60293 2.01183 7.4055 2.0336C7.3374 2.0409 7.2715 2.0546 7.2042 2.06435C7.07875 2.08265 6.9535 2.10145 6.8308 2.1281Z"
                fill="inherit"
            />
            <path
                d="M14.796 8.09893L11.196 6.05477C11.1352 6.01889 11.0662 6 10.996 6C10.9258 6 10.8568 6.01889 10.796 6.05477L7.19608 8.09893C7.13518 8.13581 7.08496 8.18855 7.05052 8.25175C7.01609 8.31496 6.99868 8.38637 7.00008 8.4587V12.547C7.0006 12.6199 7.02015 12.6912 7.05672 12.7538C7.09329 12.8163 7.14554 12.8677 7.20808 12.9027L10.808 14.9469C10.8666 14.9808 10.9327 14.9991 11 15C11.0686 14.9989 11.1359 14.9807 11.196 14.9469L14.796 12.9027C14.8578 12.8672 14.9092 12.8156 14.9451 12.7531C14.9809 12.6906 14.9999 12.6195 15 12.547V8.4587C15.0006 8.38556 14.982 8.31361 14.9461 8.25033C14.9102 8.18705 14.8584 8.13476 14.796 8.09893ZM10.6 13.8962L7.80007 12.3058V9.14963L10.6 10.74V13.8962ZM11 10.0327L8.22406 8.4587L11 6.88061L13.776 8.4587L11 10.0327ZM14.2 12.3058L11.4 13.8962V10.74L14.2 9.14963V12.3058Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const RequestQuoteIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
            <path
                d="M11 11V14H3V2H8V1H3C2.73478 1 2.48043 1.10536 2.29289 1.29289C2.10536 1.48043 2 1.73478 2 2V14C2 14.2652 2.10536 14.5196 2.29289 14.7071C2.48043 14.8946 2.73478 15 3 15H11C11.2652 15 11.5196 14.8946 11.7071 14.7071C11.8946 14.5196 12 14.2652 12 14V11H11Z"
                fill="inherit"
            />
            <path
                d="M14.77 2.88L13.12 1.23C12.9705 1.08342 12.7694 1.00131 12.56 1.00131C12.3506 1.00131 12.1495 1.08342 12 1.23L5 8.23V11H7.765L14.765 4C14.9116 3.85046 14.9937 3.6494 14.9937 3.44C14.9937 3.2306 14.9116 3.02954 14.765 2.88H14.77ZM7.35 10H6V8.65L10.72 3.925L12.075 5.28L7.35 10ZM12.78 4.575L11.425 3.22L12.56 2.085L13.915 3.44L12.78 4.575Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const EmptyDocument = (props: SvgIconProps) => {
    return (
        <SvgIcon style={{ width: 251, height: 200 }} {...props} viewBox="0 0 251 200" fill="none">
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M207.5 65C211.366 65 214.5 68.134 214.5 72C214.5 75.866 211.366 79 207.5 79H167.5C171.366 79 174.5 82.134 174.5 86C174.5 89.866 171.366 93 167.5 93H189.5C193.366 93 196.5 96.134 196.5 100C196.5 103.866 193.366 107 189.5 107H179.326C174.452 107 170.5 110.134 170.5 114C170.5 116.577 172.5 118.911 176.5 121C180.366 121 183.5 124.134 183.5 128C183.5 131.866 180.366 135 176.5 135H93.5C89.634 135 86.5 131.866 86.5 128C86.5 124.134 89.634 121 93.5 121H54.5C50.634 121 47.5 117.866 47.5 114C47.5 110.134 50.634 107 54.5 107H94.5C98.366 107 101.5 103.866 101.5 100C101.5 96.134 98.366 93 94.5 93H69.5C65.634 93 62.5 89.866 62.5 86C62.5 82.134 65.634 79 69.5 79H109.5C105.634 79 102.5 75.866 102.5 72C102.5 68.134 105.634 65 109.5 65H207.5ZM207.5 93C211.366 93 214.5 96.134 214.5 100C214.5 103.866 211.366 107 207.5 107C203.634 107 200.5 103.866 200.5 100C200.5 96.134 203.634 93 207.5 93Z"
                fill="#F0F5FF"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M154.172 64.0007L163.474 131.843L164.31 138.65C164.579 140.842 163.02 142.838 160.827 143.107L102.267 150.298C100.074 150.567 98.0785 149.008 97.8093 146.815L88.7935 73.3874C88.6589 72.2911 89.4386 71.2932 90.5349 71.1586C90.5418 71.1578 90.5488 71.1569 90.5557 71.1562L95.4141 70.6112M99.3426 70.1705L103.93 69.656L99.3426 70.1705Z"
                fill="white"
            />
            <path
                d="M155.411 63.8309C155.317 63.1469 154.686 62.6685 154.003 62.7623C153.319 62.856 152.84 63.4865 152.934 64.1705L155.411 63.8309ZM163.474 131.843L164.715 131.691C164.714 131.685 164.713 131.679 164.713 131.674L163.474 131.843ZM164.31 138.65L165.551 138.497L164.31 138.65ZM160.827 143.107L160.98 144.348L160.827 143.107ZM102.267 150.298L102.419 151.538L102.267 150.298ZM97.8093 146.815L99.05 146.663L97.8093 146.815ZM88.7935 73.3874L87.5529 73.5398L88.7935 73.3874ZM90.5557 71.1562L90.695 72.3984L90.5557 71.1562ZM95.5534 71.8534C96.2394 71.7764 96.7332 71.1579 96.6563 70.4718C96.5793 69.7858 95.9608 69.292 95.2747 69.369L95.5534 71.8534ZM99.2032 68.9283C98.5172 69.0053 98.0234 69.6238 98.1004 70.3098C98.1773 70.9959 98.7959 71.4897 99.4819 71.4127L99.2032 68.9283ZM104.069 70.8982C104.755 70.8212 105.249 70.2027 105.172 69.5166C105.095 68.8306 104.476 68.3368 103.79 68.4137L104.069 70.8982ZM152.934 64.1705L162.236 132.013L164.713 131.674L155.411 63.8309L152.934 64.1705ZM162.234 131.996L163.069 138.802L165.551 138.497L164.715 131.691L162.234 131.996ZM163.069 138.802C163.254 140.31 162.182 141.682 160.675 141.867L160.98 144.348C163.857 143.995 165.904 141.375 165.551 138.497L163.069 138.802ZM160.675 141.867L102.115 149.057L102.419 151.538L160.98 144.348L160.675 141.867ZM102.115 149.057C100.607 149.242 99.2351 148.17 99.05 146.663L96.5686 146.967C96.922 149.845 99.5414 151.892 102.419 151.538L102.115 149.057ZM99.05 146.663L90.0342 73.2351L87.5529 73.5398L96.5686 146.967L99.05 146.663ZM90.0342 73.2351C89.9837 72.824 90.2761 72.4498 90.6872 72.3993L90.3826 69.9179C88.601 70.1367 87.3341 71.7582 87.5529 73.5398L90.0342 73.2351ZM90.6872 72.3993C90.6898 72.399 90.6924 72.3987 90.695 72.3984L90.4163 69.914C90.4051 69.9152 90.3938 69.9165 90.3826 69.9179L90.6872 72.3993ZM90.695 72.3984L95.5534 71.8534L95.2747 69.369L90.4163 69.914L90.695 72.3984ZM99.4819 71.4127L104.069 70.8982L103.79 68.4137L99.2032 68.9283L99.4819 71.4127Z"
                fill="#2F54EB"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M151.64 68.2686L160.059 129.752L160.817 135.921C161.061 137.908 159.667 139.714 157.703 139.955L105.261 146.394C103.297 146.635 101.508 145.22 101.264 143.233L93.1139 76.8562C92.9792 75.7599 93.7589 74.762 94.8552 74.6274L101.343 73.8308"
                fill="#F0F5FF"
            />
            <path
                d="M108.422 54C108.422 52.4812 109.653 51.25 111.172 51.25H156.729C157.458 51.25 158.157 51.5395 158.673 52.0549L172.116 65.4898C172.632 66.0056 172.922 66.7053 172.922 67.4349V130C172.922 131.519 171.691 132.75 170.172 132.75H111.172C109.653 132.75 108.422 131.519 108.422 130V54Z"
                fill="white"
                stroke="#2F54EB"
                strokeWidth="2.5"
            />
            <path
                d="M157.172 52.4023V63.9995C157.172 65.6564 158.515 66.9995 160.172 66.9995H168.105"
                stroke="#2F54EB"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M118.5 118H144.5M118.5 67H144.5H118.5ZM118.5 79H161.5H118.5ZM118.5 92H161.5H118.5ZM118.5 105H161.5H118.5Z"
                stroke="#D6E4FF"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </SvgIcon>
    );
};

export const ArrowLeft = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none" {...props}>
        <path d="M7 13L7.705 12.295L3.915 8.5H14V7.5H3.915L7.705 3.705L7 3L2 8L7 13Z" fill="#161616" />
    </SvgIcon>
);

export const ViewOffIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path
            d="M2.61979 11.255L3.33479 10.545C2.5519 9.84206 1.93677 8.97234 1.53479 8C2.54979 5.465 5.34979 3.5 7.99979 3.5C8.68173 3.509 9.3575 3.63064 9.99979 3.86L10.7748 3.08C9.89611 2.70866 8.95363 2.51167 7.99979 2.5C6.37005 2.56129 4.79398 3.09878 3.46639 4.04604C2.1388 4.99331 1.11787 6.30882 0.529787 7.83C0.490071 7.93985 0.490071 8.06015 0.529787 8.17C0.973911 9.34851 1.69 10.4055 2.61979 11.255Z"
            fill="inherit"
        />
        <path
            d="M5.99979 7.865C6.03456 7.38586 6.24063 6.93524 6.58033 6.59554C6.92003 6.25584 7.37064 6.04977 7.84979 6.015L8.75479 5.105C8.24767 4.97147 7.71441 4.97321 7.20817 5.11003C6.70193 5.24685 6.24039 5.51399 5.86958 5.8848C5.49878 6.25561 5.23164 6.71714 5.09482 7.22338C4.95799 7.72962 4.95626 8.26288 5.08979 8.77L5.99979 7.865Z"
            fill="inherit"
        />
        <path
            d="M15.4698 7.83C14.8965 6.3366 13.8988 5.04351 12.5998 4.11L14.9998 1.705L14.2948 1L0.999787 14.295L1.70479 15L4.25479 12.45C5.39173 13.117 6.68181 13.4787 7.99979 13.5C9.62953 13.4387 11.2056 12.9012 12.5332 11.954C13.8608 11.0067 14.8817 9.69118 15.4698 8.17C15.5095 8.06015 15.5095 7.93985 15.4698 7.83ZM9.99979 8C9.99768 8.35005 9.90374 8.69342 9.72734 8.99578C9.55094 9.29815 9.29828 9.54892 8.99459 9.72303C8.69091 9.89714 8.34684 9.9885 7.99678 9.98797C7.64673 9.98744 7.30294 9.89503 6.99979 9.72L9.71979 7C9.89954 7.30287 9.99613 7.64783 9.99979 8ZM7.99979 12.5C6.95082 12.4817 5.92173 12.2107 4.99979 11.71L6.26979 10.44C6.84746 10.8408 7.54756 11.026 8.24785 10.9632C8.94815 10.9005 9.60415 10.5937 10.1013 10.0965C10.5985 9.59937 10.9052 8.94336 10.968 8.24307C11.0308 7.54277 10.8456 6.84267 10.4448 6.265L11.8798 4.83C13.0271 5.61749 13.9243 6.71771 14.4648 8C13.4498 10.535 10.6498 12.5 7.99979 12.5Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ViewOnIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path
            d="M15.47 7.83C14.882 6.30882 13.861 4.99331 12.5334 4.04604C11.2058 3.09878 9.62977 2.56129 8.00003 2.5C6.37029 2.56129 4.79423 3.09878 3.46663 4.04604C2.13904 4.99331 1.11811 6.30882 0.530031 7.83C0.490315 7.93985 0.490315 8.06015 0.530031 8.17C1.11811 9.69118 2.13904 11.0067 3.46663 11.954C4.79423 12.9012 6.37029 13.4387 8.00003 13.5C9.62977 13.4387 11.2058 12.9012 12.5334 11.954C13.861 11.0067 14.882 9.69118 15.47 8.17C15.5097 8.06015 15.5097 7.93985 15.47 7.83ZM8.00003 12.5C5.35003 12.5 2.55003 10.535 1.53503 8C2.55003 5.465 5.35003 3.5 8.00003 3.5C10.65 3.5 13.45 5.465 14.465 8C13.45 10.535 10.65 12.5 8.00003 12.5Z"
            fill="inherit"
        />
        <path
            d="M8.00003 5C7.40669 5 6.82667 5.17595 6.33332 5.50559C5.83997 5.83524 5.45546 6.30377 5.22839 6.85195C5.00133 7.40013 4.94192 8.00333 5.05768 8.58527C5.17343 9.16721 5.45915 9.70176 5.87871 10.1213C6.29827 10.5409 6.83282 10.8266 7.41476 10.9424C7.9967 11.0581 8.5999 10.9987 9.14808 10.7716C9.69626 10.5446 10.1648 10.1601 10.4944 9.66671C10.8241 9.17336 11 8.59334 11 8C11 7.20435 10.684 6.44129 10.1214 5.87868C9.55874 5.31607 8.79568 5 8.00003 5ZM8.00003 10C7.60447 10 7.21779 9.8827 6.88889 9.66294C6.55999 9.44318 6.30365 9.13082 6.15227 8.76537C6.0009 8.39991 5.96129 7.99778 6.03846 7.60982C6.11563 7.22186 6.30611 6.86549 6.58582 6.58579C6.86552 6.30608 7.22189 6.1156 7.60985 6.03843C7.99781 5.96126 8.39995 6.00087 8.7654 6.15224C9.13085 6.30362 9.44321 6.55996 9.66297 6.88886C9.88273 7.21776 10 7.60444 10 8C10 8.53043 9.78932 9.03914 9.41424 9.41421C9.03917 9.78929 8.53046 10 8.00003 10Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const ArrowLeftIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none">
        <path d="M10 12L5 8L10 4V12Z" fill="inherit" />
    </SvgIcon>
);

export const EditIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none" {...props}>
        <path d="M15 13H1V14H15V13Z" fill="inherit" />
        <path
            d="M12.7 4.5C13.1 4.1 13.1 3.5 12.7 3.1L10.9 1.3C10.5 0.9 9.9 0.9 9.5 1.3L2 8.8V12H5.2L12.7 4.5ZM10.2 2L12 3.8L10.5 5.3L8.7 3.5L10.2 2ZM3 11V9.2L8 4.2L9.8 6L4.8 11H3Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const DocumentIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '16px', height: '16px' }} viewBox="0 0 16 16" fill="none" {...props}>
        <path
            d="M12.85 4.65L9.35 1.15C9.25 1.05 9.15 1 9 1H4C3.45 1 3 1.45 3 2V14C3 14.55 3.45 15 4 15H12C12.55 15 13 14.55 13 14V5C13 4.85 12.95 4.75 12.85 4.65ZM9 2.2L11.8 5H9V2.2ZM12 14H4V2H8V5C8 5.55 8.45 6 9 6H12V14Z"
            fill="inherit"
        />
        <path d="M11 11H5V12H11V11Z" fill="inherit" />
        <path d="M11 8H5V9H11V8Z" fill="inherit" />
    </SvgIcon>
);

export const DocumentImportIcon = (props: SvgIconProps) => (
    <SvgIcon
        {...props}
        style={{ width: '16px', height: '16px' }}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M14 9.50001H7.415L8.705 8.20501L8 7.50001L5.5 10L8 12.5L8.705 11.795L7.415 10.5H14V9.50001Z"
            fill="white"
        />
        <path
            d="M12 7.00001V5.00001C12.0004 4.93421 11.9878 4.86897 11.9629 4.80806C11.938 4.74714 11.9013 4.69173 11.855 4.64501L8.355 1.14501C8.30828 1.09867 8.25287 1.06201 8.19195 1.03712C8.13103 1.01224 8.0658 0.999628 8 1.00001H3C2.73478 1.00001 2.48043 1.10537 2.29289 1.2929C2.10536 1.48044 2 1.73479 2 2.00001V14C2 14.2652 2.10536 14.5196 2.29289 14.7071C2.48043 14.8947 2.73478 15 3 15H11C11.2652 15 11.5196 14.8947 11.7071 14.7071C11.8946 14.5196 12 14.2652 12 14V13H11V14H3V2.00001H7V5.00001C7 5.26523 7.10536 5.51958 7.29289 5.70712C7.48043 5.89465 7.73478 6.00001 8 6.00001H11V7.00001H12ZM8 5.00001V2.20501L10.795 5.00001H8Z"
            fill="white"
        />
    </SvgIcon>
);

export const TransparentIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="inherit"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <g clipPath="url(#clip0_2524_16626)">
                <path d="M7 8.5H5V10.5H7V8.5Z" fill="black" />
                <path d="M10.5 5H8.5V7H10.5V5Z" fill="black" />
                <path d="M10.5 8.5H8.5V10.5H10.5V8.5Z" fill="black" />
                <path
                    d="M9.879 13.325L8 14.421L2.5 11.213V9H1.5V11.5C1.5 11.6775 1.5945 11.8425 1.748 11.932L7.748 15.432C7.826 15.4775 7.913 15.5 8 15.5C8.087 15.5 8.174 15.4775 8.252 15.432L10.393 14.183L9.879 13.325Z"
                    fill="black"
                />
                <path
                    d="M14.252 4.06824L12.1175 2.82324L11.603 3.68074L13.5 4.78724V11.2132L11.6065 12.3177L12.121 13.1752L14.252 11.9322C14.4055 11.8427 14.5 11.6777 14.5 11.5002V4.50024C14.5 4.32274 14.4055 4.15774 14.252 4.06824Z"
                    fill="black"
                />
                <path
                    d="M2.5 4.787L8 1.5785L9.8755 2.6725L10.39 1.815L8.252 0.568C8.174 0.5225 8.087 0.5 8 0.5C7.913 0.5 7.826 0.5225 7.748 0.568L1.748 4.068C1.5945 4.1575 1.5 4.3225 1.5 4.5V7H2.5V4.787Z"
                    fill="black"
                />
            </g>
            <defs>
                <clipPath id="clip0_2524_16626">
                    <rect width="16" height="16" fill="white" />
                </clipPath>
            </defs>
        </SvgIcon>
    );
};

export const CameraIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="inherit"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M14.5 13H1.5C1.36739 13 1.24021 12.9473 1.14645 12.8536C1.05268 12.7598 1 12.6326 1 12.5V4C1 3.86739 1.05268 3.74021 1.14645 3.64645C1.24021 3.55268 1.36739 3.5 1.5 3.5H4.73L5.585 2.225C5.63029 2.15622 5.69188 2.09969 5.76427 2.06044C5.83667 2.02119 5.91765 2.00043 6 2H10C10.0824 2.00043 10.1633 2.02119 10.2357 2.06044C10.3081 2.09969 10.3697 2.15622 10.415 2.225L11.27 3.5H14.5C14.6326 3.5 14.7598 3.55268 14.8536 3.64645C14.9473 3.74021 15 3.86739 15 4V12.5C15 12.6326 14.9473 12.7598 14.8536 12.8536C14.7598 12.9473 14.6326 13 14.5 13ZM2 12H14V4.5H11C10.9176 4.49957 10.8367 4.47881 10.7643 4.43956C10.6919 4.40031 10.6303 4.34378 10.585 4.275L9.73 3H6.27L5.415 4.275C5.36971 4.34378 5.30812 4.40031 5.23573 4.43956C5.16333 4.47881 5.08235 4.49957 5 4.5H2V12Z"
                fill="#161616"
            />
            <path
                d="M8 11C7.40666 11 6.82664 10.8241 6.33329 10.4944C5.83994 10.1648 5.45542 9.69623 5.22836 9.14805C5.0013 8.59987 4.94189 7.99667 5.05764 7.41473C5.1734 6.83279 5.45912 6.29824 5.87868 5.87868C6.29824 5.45912 6.83279 5.1734 7.41473 5.05764C7.99667 4.94189 8.59987 5.0013 9.14805 5.22836C9.69623 5.45542 10.1648 5.83994 10.4944 6.33329C10.8241 6.82664 11 7.40666 11 8C11 8.79565 10.6839 9.55871 10.1213 10.1213C9.55871 10.6839 8.79565 11 8 11ZM8 6C7.60444 6 7.21776 6.1173 6.88886 6.33706C6.55996 6.55682 6.30362 6.86918 6.15224 7.23463C6.00087 7.60009 5.96126 8.00222 6.03843 8.39018C6.1156 8.77814 6.30608 9.13451 6.58579 9.41421C6.86549 9.69392 7.22186 9.8844 7.60982 9.96157C7.99778 10.0387 8.39991 9.99913 8.76537 9.84776C9.13082 9.69638 9.44318 9.44004 9.66294 9.11114C9.8827 8.78224 10 8.39556 10 8C10 7.46957 9.78929 6.96086 9.41421 6.58579C9.03914 6.21071 8.53043 6 8 6Z"
                fill="#161616"
            />
        </SvgIcon>
    );
};

export const HandIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="inherit"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M12.3674 3.9535C12.0485 3.63456 11.57 3.55483 11.1714 3.7143C11.1714 3.4751 11.0916 3.23589 10.8524 2.99669C10.693 2.83722 10.374 2.67776 9.97537 2.67776C9.73616 2.67776 9.5767 2.75749 9.41723 2.75749C9.41723 2.59802 9.33749 2.51829 9.25776 2.35882C8.85909 1.88041 8.06175 1.88041 7.66308 2.35882C7.50361 2.51829 7.34414 2.67776 7.34414 2.83722C7.18467 2.83722 7.0252 2.75749 6.86573 2.75749C6.46706 2.75749 6.22786 2.91696 5.98866 3.15616C5.58999 3.55483 5.58999 4.1927 5.58999 4.1927V7.2226C5.35079 6.9834 4.95212 6.58473 4.39398 6.58473C4.23451 6.58473 3.99531 6.66446 3.83584 6.7442C3.5169 6.90366 3.35743 7.14287 3.2777 7.4618C3.0385 8.25915 3.7561 9.37542 3.7561 9.45516C3.83584 9.53489 4.71291 11.608 5.51025 12.4851C6.3076 13.4419 7.18467 14 9.41723 14C11.7295 14 12.7661 12.7243 12.7661 9.93356V5.54818C12.7661 5.46845 12.8458 4.51164 12.3674 3.9535ZM7.98201 3.63456C7.98201 3.39536 7.90228 2.83722 8.38068 2.83722C8.77935 2.83722 8.77935 3.23589 8.77935 3.63456V6.82393C8.77935 7.06313 8.93882 7.2226 9.17802 7.2226C9.41723 7.2226 9.5767 7.06313 9.5767 6.82393V3.79403C9.5767 3.79403 9.5767 3.4751 9.97537 3.4751C10.4538 3.4751 10.374 4.1927 10.374 4.1927V6.82393C10.374 7.06313 10.5335 7.2226 10.7727 7.2226C11.0119 7.2226 11.1714 7.06313 11.1714 6.82393V4.91031C11.1714 4.83058 11.1714 4.43191 11.57 4.43191C11.9687 4.43191 11.9687 5.22925 11.9687 5.22925V9.93356C11.9687 12.6445 10.9322 13.2027 9.41723 13.2027C7.50361 13.2027 6.786 12.804 6.14813 11.9269C5.43052 11.1296 4.47371 9.05649 4.47371 8.97675C4.23451 8.73755 3.91557 8.01994 3.99531 7.70101C3.99531 7.62127 4.07504 7.54154 4.15477 7.4618C4.23451 7.4618 4.31424 7.38207 4.31424 7.38207C4.63318 7.38207 4.95212 7.78074 5.03185 7.94021L5.51025 8.65782C5.58999 8.81728 5.82919 8.89702 5.98866 8.81728C6.3076 8.81728 6.38733 8.65782 6.38733 8.49835V4.35217C6.38733 4.03324 6.38733 3.55483 6.786 3.55483C7.10494 3.55483 7.18467 3.79403 7.18467 4.1927V6.82393C7.18467 7.06313 7.34414 7.2226 7.58334 7.2226C7.82254 7.2226 7.98201 7.06313 7.98201 6.82393V3.63456Z"
                fill="#444444"
            />
        </SvgIcon>
    );
};

export const DataViewIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M11 13C11.5523 13 12 12.5523 12 12C12 11.4477 11.5523 11 11 11C10.4477 11 10 11.4477 10 12C10 12.5523 10.4477 13 11 13Z"
                fill="#161616"
            />
            <path
                d="M14.8884 11.7393C14.5795 10.9522 14.0464 10.2732 13.3552 9.78629C12.664 9.29937 11.8451 9.02598 11 9C10.1549 9.02598 9.33604 9.29937 8.64484 9.78629C7.95365 10.2732 7.42052 10.9522 7.11155 11.7393L7 12L7.11155 12.2607C7.42052 13.0478 7.95365 13.7268 8.64484 14.2137C9.33604 14.7006 10.1549 14.974 11 15C11.8451 14.974 12.664 14.7006 13.3552 14.2137C14.0464 13.7268 14.5795 13.0478 14.8884 12.2607L15 12L14.8884 11.7393ZM11 14C10.6044 14 10.2178 13.8827 9.88886 13.6629C9.55996 13.4432 9.30362 13.1308 9.15224 12.7654C9.00087 12.3999 8.96126 11.9978 9.03843 11.6098C9.1156 11.2219 9.30608 10.8655 9.58579 10.5858C9.86549 10.3061 10.2219 10.1156 10.6098 10.0384C10.9978 9.96126 11.3999 10.0009 11.7654 10.1522C12.1308 10.3036 12.4432 10.56 12.6629 10.8889C12.8827 11.2178 13 11.6044 13 12C12.9994 12.5303 12.7885 13.0386 12.4136 13.4136C12.0386 13.7885 11.5303 13.9994 11 14Z"
                fill="#161616"
            />
            <path
                d="M4 4.5C4.27614 4.5 4.5 4.27614 4.5 4C4.5 3.72386 4.27614 3.5 4 3.5C3.72386 3.5 3.5 3.72386 3.5 4C3.5 4.27614 3.72386 4.5 4 4.5Z"
                fill="#161616"
            />
            <path
                d="M4 8.5C4.27614 8.5 4.5 8.27614 4.5 8C4.5 7.72386 4.27614 7.5 4 7.5C3.72386 7.5 3.5 7.72386 3.5 8C3.5 8.27614 3.72386 8.5 4 8.5Z"
                fill="#161616"
            />
            <path
                d="M4 12.5C4.27614 12.5 4.5 12.2761 4.5 12C4.5 11.7239 4.27614 11.5 4 11.5C3.72386 11.5 3.5 11.7239 3.5 12C3.5 12.2761 3.72386 12.5 4 12.5Z"
                fill="#161616"
            />
            <path
                d="M2.5 10.5H6V9.5H2.5V6.5H10.5V8H11.5V2.5C11.5 2.23478 11.3946 1.98043 11.2071 1.79289C11.0196 1.60536 10.7652 1.5 10.5 1.5H2.5C2.23478 1.5 1.98043 1.60536 1.79289 1.79289C1.60536 1.98043 1.5 2.23478 1.5 2.5V13.5C1.5 13.7652 1.60536 14.0196 1.79289 14.2071C1.98043 14.3946 2.23478 14.5 2.5 14.5H6V13.5H2.5V10.5ZM2.5 2.5H10.5V5.5H2.5V2.5Z"
                fill="#161616"
            />
        </SvgIcon>
    );
};

export const DecisionTreeIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M15 6V2H11V3.5H9C8.73488 3.5003 8.4807 3.60576 8.29323 3.79323C8.10576 3.9807 8.0003 4.23488 8 4.5V7.5H5V6H1V10H5V8.5H8V11.5C8.0003 11.7651 8.10576 12.0193 8.29323 12.2068C8.4807 12.3942 8.73488 12.4997 9 12.5H11V14H15V10H11V11.5H9V4.5H11V6H15ZM4 9H2V7H4V9ZM12 11H14V13H12V11ZM12 3H14V5H12V3Z"
                fill="#161616"
            />
        </SvgIcon>
    );
};

export const LayerIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M8.00001 12C7.91724 12 7.83577 11.9794 7.76296 11.94L1.26296 8.43999L1.73706 7.55999L8.00001 10.9321L14.263 7.55999L14.7371 8.44034L8.23706 11.9403C8.16422 11.9796 8.08275 12.0001 8.00001 12Z"
                fill="#161616"
            />
            <path
                d="M8.00001 15C7.91724 15 7.83577 14.9794 7.76296 14.94L1.26296 11.44L1.73706 10.56L8.00001 13.9321L14.263 10.56L14.7371 11.4403L8.23706 14.9403C8.16422 14.9796 8.08275 15.0001 8.00001 15Z"
                fill="#161616"
            />
            <path
                d="M8.00001 8.99999C7.91724 8.99999 7.83577 8.97937 7.76296 8.93999L1.26296 5.43999C1.1835 5.39718 1.11711 5.33365 1.07083 5.25616C1.02456 5.17866 1.00012 5.09008 1.00012 4.99982C1.00012 4.90956 1.02456 4.82098 1.07083 4.74348C1.11711 4.66598 1.1835 4.60246 1.26296 4.55964L7.76296 1.05964C7.83579 1.02034 7.91725 0.999756 8.00001 0.999756C8.08277 0.999756 8.16423 1.02034 8.23706 1.05964L14.7371 4.55964C14.8165 4.60246 14.8829 4.66598 14.9292 4.74348C14.9755 4.82098 14.9999 4.90956 14.9999 4.99982C14.9999 5.09008 14.9755 5.17866 14.9292 5.25616C14.8829 5.33365 14.8165 5.39718 14.7371 5.43999L8.23706 8.93999C8.16425 8.97937 8.08278 8.99999 8.00001 8.99999ZM2.55471 4.99999L8.00001 7.93214L13.4453 4.99999L8.00001 2.06789L2.55471 4.99999Z"
                fill="#161616"
            />
        </SvgIcon>
    );
};

export const BookIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path d="M13 5H9.5V6H13V5Z" fill="#161616" />
            <path d="M13 7.5H9.5V8.5H13V7.5Z" fill="#161616" />
            <path d="M13 10H9.5V11H13V10Z" fill="#161616" />
            <path d="M6.5 5H3V6H6.5V5Z" fill="#161616" />
            <path d="M6.5 7.5H3V8.5H6.5V7.5Z" fill="#161616" />
            <path d="M6.5 10H3V11H6.5V10Z" fill="#161616" />
            <path
                d="M14 2.5H2C1.73486 2.50026 1.48066 2.60571 1.29319 2.79319C1.10571 2.98066 1.00026 3.23486 1 3.5V12.5C1.00026 12.7651 1.10571 13.0193 1.29319 13.2068C1.48066 13.3943 1.73486 13.4997 2 13.5H14C14.2651 13.4997 14.5193 13.3943 14.7068 13.2068C14.8943 13.0193 14.9997 12.7651 15 12.5V3.5C14.9997 3.23486 14.8943 2.98066 14.7068 2.79319C14.5193 2.60571 14.2651 2.50026 14 2.5ZM2 3.5H7.5V12.5H2V3.5ZM8.5 12.5V3.5H14V12.5H8.5Z"
                fill="#161616"
            />
        </SvgIcon>
    );
};

export const ConfigViewIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M11 13C11.5523 13 12 12.5523 12 12C12 11.4477 11.5523 11 11 11C10.4477 11 10 11.4477 10 12C10 12.5523 10.4477 13 11 13Z"
                fill="#161616"
            />
            <path
                d="M14.8884 11.7393C14.5795 10.9522 14.0464 10.2732 13.3552 9.78629C12.664 9.29937 11.8451 9.02598 11 9C10.1549 9.02598 9.33603 9.29937 8.64484 9.78629C7.95365 10.2732 7.42052 10.9522 7.11155 11.7393L7 12L7.11155 12.2607C7.42052 13.0478 7.95365 13.7268 8.64484 14.2137C9.33603 14.7006 10.1549 14.974 11 15C11.8451 14.974 12.664 14.7006 13.3552 14.2137C14.0464 13.7268 14.5795 13.0478 14.8884 12.2607L15 12L14.8884 11.7393ZM11 14C10.6044 14 10.2178 13.8827 9.88886 13.6629C9.55996 13.4432 9.30362 13.1308 9.15224 12.7654C9.00087 12.3999 8.96126 11.9978 9.03843 11.6098C9.1156 11.2219 9.30608 10.8655 9.58579 10.5858C9.86549 10.3061 10.2219 10.1156 10.6098 10.0384C10.9978 9.96126 11.3999 10.0009 11.7654 10.1522C12.1308 10.3036 12.4432 10.56 12.6629 10.8889C12.8827 11.2178 13 11.6044 13 12C12.9994 12.5303 12.7885 13.0386 12.4136 13.4136C12.0386 13.7885 11.5303 13.9994 11 14Z"
                fill="#161616"
            />
            <path d="M6 8.5H3.5V9.5H6V8.5Z" fill="#161616" />
            <path d="M9.5 6H3.5V7H9.5V6Z" fill="#161616" />
            <path d="M9.5 3.5H3.5V4.5H9.5V3.5Z" fill="#161616" />
            <path
                d="M11 1H2C1.73502 1.00077 1.48111 1.10637 1.29374 1.29374C1.10637 1.48111 1.00077 1.73502 1 2V14C1.00077 14.265 1.10637 14.5189 1.29374 14.7063C1.48111 14.8936 1.73502 14.9992 2 15H6V14H2V2H11V7.5H12V2C11.9992 1.73502 11.8936 1.48111 11.7063 1.29374C11.5189 1.10637 11.265 1.00077 11 1Z"
                fill="#161616"
            />
        </SvgIcon>
    );
};

export const PrivateIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M8 4.00195C7.50555 4.00195 7.0222 4.14858 6.61108 4.42328C6.19995 4.69798 5.87952 5.08843 5.6903 5.54524C5.50108 6.00206 5.45158 6.50473 5.54804 6.98968C5.6445 7.47463 5.88261 7.92009 6.23224 8.26972C6.58187 8.61935 7.02733 8.85745 7.51228 8.95392C7.99723 9.05038 8.4999 9.00087 8.95671 8.81165C9.41353 8.62243 9.80397 8.302 10.0787 7.89088C10.3534 7.47976 10.5 6.99641 10.5 6.50195C10.5 5.83891 10.2366 5.20303 9.76777 4.73419C9.29893 4.26535 8.66304 4.00195 8 4.00195ZM8 8.00195C7.70333 8.00195 7.41332 7.91398 7.16665 7.74916C6.91997 7.58434 6.72772 7.35007 6.61418 7.07598C6.50065 6.80189 6.47095 6.50029 6.52882 6.20932C6.5867 5.91835 6.72956 5.65107 6.93934 5.44129C7.14912 5.23151 7.4164 5.08865 7.70737 5.03078C7.99834 4.9729 8.29994 5.0026 8.57403 5.11613C8.84812 5.22967 9.08239 5.42192 9.24721 5.6686C9.41203 5.91527 9.5 6.20528 9.5 6.50195C9.49955 6.89964 9.34137 7.28091 9.06017 7.56212C8.77896 7.84332 8.39769 8.0015 8 8.00195Z"
                fill="inherit"
            />
            <path
                d="M8 1.00195C6.61553 1.00195 5.26216 1.4125 4.11101 2.18167C2.95987 2.95084 2.06266 4.04409 1.53285 5.32317C1.00303 6.60225 0.86441 8.00972 1.13451 9.36759C1.4046 10.7255 2.07129 11.9727 3.05026 12.9517C4.02922 13.9307 5.2765 14.5974 6.63437 14.8674C7.99224 15.1375 9.3997 14.9989 10.6788 14.4691C11.9579 13.9393 13.0511 13.0421 13.8203 11.8909C14.5895 10.7398 15 9.38642 15 8.00195C14.9979 6.14608 14.2597 4.36682 12.9474 3.05452C11.6351 1.74221 9.85588 1.00404 8 1.00195ZM5 13.1902V12.502C5.00044 12.1043 5.15862 11.723 5.43983 11.4418C5.72104 11.1606 6.10231 11.0024 6.5 11.002H9.5C9.89769 11.0024 10.279 11.1606 10.5602 11.4418C10.8414 11.723 10.9996 12.1043 11 12.502V13.1902C10.0896 13.7218 9.05426 14.002 8 14.002C6.94574 14.002 5.91042 13.7218 5 13.1902ZM11.9963 12.4649C11.9863 11.809 11.7191 11.1833 11.2521 10.7226C10.7852 10.2619 10.156 10.0031 9.5 10.002H6.5C5.84405 10.0031 5.2148 10.2619 4.74786 10.7226C4.28093 11.1833 4.01369 11.809 4.00375 12.4649C3.09703 11.6552 2.45762 10.5893 2.17017 9.40819C1.88272 8.22708 1.9608 6.98653 2.39407 5.85078C2.82734 4.71504 3.59536 3.73769 4.59644 3.04814C5.59751 2.35859 6.78442 1.98937 8 1.98937C9.21558 1.98937 10.4025 2.35859 11.4036 3.04814C12.4046 3.73769 13.1727 4.71504 13.6059 5.85078C14.0392 6.98653 14.1173 8.22708 13.8298 9.40819C13.5424 10.5893 12.903 11.6552 11.9963 12.4649Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const UserIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M8 2.00195C8.49445 2.00195 8.9778 2.14858 9.38893 2.42328C9.80005 2.69798 10.1205 3.08843 10.3097 3.54524C10.4989 4.00206 10.5484 4.50473 10.452 4.98968C10.3555 5.47463 10.1174 5.92009 9.76777 6.26972C9.41814 6.61935 8.97268 6.85745 8.48773 6.95392C8.00277 7.05038 7.50011 7.00087 7.04329 6.81165C6.58648 6.62243 6.19603 6.302 5.92133 5.89088C5.64662 5.47976 5.5 4.99641 5.5 4.50195C5.5 3.83891 5.76339 3.20303 6.23223 2.73419C6.70107 2.26535 7.33696 2.00195 8 2.00195ZM8 1.00195C7.30777 1.00195 6.63108 1.20722 6.0555 1.59181C5.47993 1.97639 5.03133 2.52302 4.76642 3.16256C4.50152 3.8021 4.4322 4.50584 4.56725 5.18477C4.7023 5.8637 5.03564 6.48734 5.52513 6.97683C6.01461 7.46631 6.63825 7.79965 7.31718 7.9347C7.99612 8.06975 8.69985 8.00044 9.33939 7.73553C9.97893 7.47062 10.5256 7.02202 10.9101 6.44645C11.2947 5.87088 11.5 5.19419 11.5 4.50195C11.5 3.5737 11.1313 2.68346 10.4749 2.02708C9.8185 1.3707 8.92826 1.00195 8 1.00195Z"
                fill="inherit"
            />
            <path
                d="M13 15.002H12V12.502C12 12.1736 11.9353 11.8486 11.8097 11.5452C11.6841 11.2419 11.4999 10.9663 11.2678 10.7342C11.0356 10.502 10.76 10.3179 10.4567 10.1923C10.1534 10.0666 9.8283 10.002 9.5 10.002H6.5C5.83696 10.002 5.20107 10.2653 4.73223 10.7342C4.26339 11.203 4 11.8389 4 12.502V15.002H3V12.502C3 11.5737 3.36875 10.6835 4.02513 10.0271C4.6815 9.3707 5.57174 9.00195 6.5 9.00195H9.5C10.4283 9.00195 11.3185 9.3707 11.9749 10.0271C12.6313 10.6835 13 11.5737 13 12.502V15.002Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const TeamIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M15 15.002H14V12.502C13.9992 11.8391 13.7356 11.2037 13.2669 10.735C12.7983 10.2663 12.1628 10.0027 11.5 10.002V9.00195C12.4279 9.00299 13.3176 9.37207 13.9737 10.0282C14.6299 10.6844 14.999 11.574 15 12.502V15.002Z"
                fill="inherit"
            />
            <path
                d="M11 15.002H10V12.502C9.99922 11.8392 9.73558 11.2037 9.2669 10.735C8.79823 10.2664 8.1628 10.0027 7.5 10.002H4.5C3.8372 10.0027 3.20177 10.2664 2.7331 10.735C2.26442 11.2037 2.00078 11.8392 2 12.502V15.002H1V12.502C1.00109 11.574 1.37018 10.6844 2.02632 10.0283C2.68247 9.37214 3.57208 9.00304 4.5 9.00195H7.5C8.42792 9.00304 9.31753 9.37214 9.97368 10.0283C10.6298 10.6844 10.9989 11.574 11 12.502V15.002Z"
                fill="inherit"
            />
            <path
                d="M10 1.00195V2.00195C10.663 2.00195 11.2989 2.26535 11.7678 2.73419C12.2366 3.20303 12.5 3.83891 12.5 4.50195C12.5 5.16499 12.2366 5.80088 11.7678 6.26972C11.2989 6.73856 10.663 7.00195 10 7.00195V8.00195C10.9283 8.00195 11.8185 7.6332 12.4749 6.97683C13.1313 6.32045 13.5 5.43021 13.5 4.50195C13.5 3.5737 13.1313 2.68346 12.4749 2.02708C11.8185 1.3707 10.9283 1.00195 10 1.00195Z"
                fill="inherit"
            />
            <path
                d="M6 2.00195C6.49445 2.00195 6.9778 2.14858 7.38893 2.42328C7.80005 2.69798 8.12048 3.08843 8.3097 3.54524C8.49892 4.00206 8.54843 4.50473 8.45196 4.98968C8.3555 5.47463 8.1174 5.92009 7.76777 6.26972C7.41814 6.61935 6.97268 6.85745 6.48773 6.95392C6.00277 7.05038 5.50011 7.00087 5.04329 6.81165C4.58648 6.62243 4.19603 6.302 3.92133 5.89088C3.64662 5.47976 3.5 4.99641 3.5 4.50195C3.5 3.83891 3.76339 3.20303 4.23223 2.73419C4.70107 2.26535 5.33696 2.00195 6 2.00195ZM6 1.00195C5.30777 1.00195 4.63108 1.20722 4.0555 1.59181C3.47993 1.97639 3.03133 2.52302 2.76642 3.16256C2.50151 3.8021 2.4322 4.50584 2.56725 5.18477C2.7023 5.8637 3.03564 6.48734 3.52513 6.97683C4.01461 7.46631 4.63825 7.79965 5.31718 7.9347C5.99612 8.06975 6.69985 8.00044 7.33939 7.73553C7.97893 7.47062 8.52556 7.02202 8.91014 6.44645C9.29473 5.87088 9.5 5.19419 9.5 4.50195C9.5 3.5737 9.13125 2.68346 8.47487 2.02708C7.8185 1.3707 6.92826 1.00195 6 1.00195Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const DepartmentIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M15 5.00195V1.00195H11V2.50195H8.5C8.23488 2.50226 7.9807 2.60771 7.79323 2.79518C7.60576 2.98265 7.5003 3.23683 7.5 3.50195V7.50195H5V6.00195H1V10.002H5V8.50195H7.5V12.502C7.5003 12.7671 7.60576 13.0213 7.79323 13.2087C7.9807 13.3962 8.23488 13.5016 8.5 13.502H11V15.002H15V11.002H11V12.502H8.5V8.50195H11V10.002H15V6.00195H11V7.50195H8.5V3.50195H11V5.00195H15ZM4 9.00195H2V7.00195H4V9.00195ZM12 12.002H14V14.002H12V12.002ZM12 7.00195H14V9.00195H12V7.00195ZM12 2.00195H14V4.00195H12V2.00195Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const InternalCompanyIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path d="M5 4.00195H4V6.00195H5V4.00195Z" fill="inherit" />
            <path d="M5 7.00195H4V9.00195H5V7.00195Z" fill="inherit" />
            <path d="M8 4.00195H7V6.00195H8V4.00195Z" fill="inherit" />
            <path d="M8 7.00195H7V9.00195H8V7.00195Z" fill="inherit" />
            <path d="M5 10.002H4V12.002H5V10.002Z" fill="inherit" />
            <path d="M8 10.002H7V12.002H8V10.002Z" fill="inherit" />
            <path
                d="M15 7.00195C15 6.73674 14.8946 6.48238 14.7071 6.29485C14.5196 6.10731 14.2652 6.00195 14 6.00195H11V2.00195C11 1.73674 10.8946 1.48238 10.7071 1.29485C10.5196 1.10731 10.2652 1.00195 10 1.00195H2C1.73478 1.00195 1.48043 1.10731 1.29289 1.29485C1.10536 1.48238 1 1.73674 1 2.00195V15.002H15V7.00195ZM2 2.00195H10V14.002H2V2.00195ZM11 14.002V7.00195H14V14.002H11Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const ExternalCompanyIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon
            style={{ width: '16px', height: '16px' }}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path d="M5 4.00195H4V6.00195H5V4.00195Z" fill="inherit" />
            <path d="M5 7.00195H4V9.00195H5V7.00195Z" fill="inherit" />
            <path d="M8 4.00195H7V6.00195H8V4.00195Z" fill="inherit" />
            <path d="M8 7.00195H7V9.00195H8V7.00195Z" fill="inherit" />
            <path d="M5 10.002H4V12.002H5V10.002Z" fill="inherit" />
            <path d="M8 10.002H7V12.002H8V10.002Z" fill="inherit" />
            <path
                d="M9 7.00195H13.085L11.795 8.29695L12.5 9.00195L15 6.50195L12.5 4.00195L11.795 4.70695L13.085 6.00195H9V7.00195Z"
                fill="inherit"
            />
            <path
                d="M14 10.002V14.002H11V10.002H10V14.002H2V2.00195H10V3.00195H11V2.00195C11 1.73674 10.8946 1.48238 10.7071 1.29485C10.5196 1.10731 10.2652 1.00195 10 1.00195H2C1.73478 1.00195 1.48043 1.10731 1.29289 1.29485C1.10536 1.48238 1 1.73674 1 2.00195V15.002H15V10.002H14Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const NewTemplateIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '32px', height: '32px' }} {...props} viewBox="0 0 32 32" fill="none">
        <path d="M17 10H15V15H10V17H15V22H17V17H22V15H17V10Z" fill="inherit" />
        <path
            d="M30 8V2H24V4H8V2H2V8H4V24H2V30H8V28H24V30H30V24H28V8H30ZM26 4H28V6H26V4ZM4 4H6V6H4V4ZM6 28H4V26H6V28ZM28 28H26V26H28V28ZM26 24H24V26H8V24H6V8H8V6H24V8H26V24Z"
            fill="inherit"
        />
    </SvgIcon>
);
export const ExistingTemplateIcon = (props: SvgIconProps) => (
    <SvgIcon style={{ width: '32px', height: '32px' }} {...props} viewBox="0 0 32 32" fill="none">
        <path
            d="M26 6V10H6V6H26ZM26 4H6C5.46957 4 4.96086 4.21071 4.58579 4.58579C4.21071 4.96086 4 5.46957 4 6V10C4 10.5304 4.21071 11.0391 4.58579 11.4142C4.96086 11.7893 5.46957 12 6 12H26C26.5304 12 27.0391 11.7893 27.4142 11.4142C27.7893 11.0391 28 10.5304 28 10V6C28 5.46957 27.7893 4.96086 27.4142 4.58579C27.0391 4.21071 26.5304 4 26 4Z"
            fill="inherit"
        />
        <path
            d="M10 16V26H6V16H10ZM10 14H6C5.46957 14 4.96086 14.2107 4.58579 14.5858C4.21071 14.9609 4 15.4696 4 16V26C4 26.5304 4.21071 27.0391 4.58579 27.4142C4.96086 27.7893 5.46957 28 6 28H10C10.5304 28 11.0391 27.7893 11.4142 27.4142C11.7893 27.0391 12 26.5304 12 26V16C12 15.4696 11.7893 14.9609 11.4142 14.5858C11.0391 14.2107 10.5304 14 10 14Z"
            fill="inherit"
        />
        <path
            d="M26 16V26H16V16H26ZM26 14H16C15.4696 14 14.9609 14.2107 14.5858 14.5858C14.2107 14.9609 14 15.4696 14 16V26C14 26.5304 14.2107 27.0391 14.5858 27.4142C14.9609 27.7893 15.4696 28 16 28H26C26.5304 28 27.0391 27.7893 27.4142 27.4142C27.7893 27.0391 28 26.5304 28 26V16C28 15.4696 27.7893 14.9609 27.4142 14.5858C27.0391 14.2107 26.5304 14 26 14Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const StarIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <g clipPath="url(#clip0_5115_11442)">
                <rect width="24" height="24" rx="2" fill="transparent" style={{ mixBlendMode: 'multiply' }} />
                <path
                    d="M12.0006 5L9.72563 9.61L4.64062 10.345L8.32063 13.935L7.45062 19L12.0006 16.61L16.5506 19L15.6806 13.935L19.3606 10.35L14.2756 9.61L12.0006 5Z"
                    fill="#FAC114"
                />
            </g>
            <defs>
                <clipPath id="clip0_5115_11442">
                    <rect width="24" height="24" rx="2" fill="white" />
                </clipPath>
            </defs>
        </SvgIcon>
    );
};

export const BookmarkIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M12 8V13.3757L8 11.3525L4 13.374V2H9V1H4C3.73478 1 3.48043 1.10536 3.29289 1.29289C3.10536 1.48043 3 1.73478 3 2V15L8 12.4732L13 15V8H12Z"
                fill={props.fill ?? '#161616'}
            />
            <path d="M13 3V1H12V3H10V4H12V6H13V4H15V3H13Z" fill={props.fill ?? '#161616'} />
        </SvgIcon>
    );
};

export const BookmarkFilledIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M12 1H4C3.73478 1 3.48043 1.10536 3.29289 1.29289C3.10536 1.48043 3 1.73478 3 2V15L8 12.4731L13 15V2C13 1.73478 12.8946 1.48043 12.7071 1.29289C12.5196 1.10536 12.2652 1 12 1Z"
                fill={props.fill ?? '#161616'}
            />
        </SvgIcon>
    );
};

export const BookmarkOutlineIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M12 2V13.375L8.45 11.58L8 11.355L7.55 11.58L4 13.375V2H12ZM12 1H4C3.73478 1 3.48043 1.10536 3.29289 1.29289C3.10536 1.48043 3 1.73478 3 2V15L8 12.5L13 15V2C13 1.73478 12.8946 1.48043 12.7071 1.29289C12.5196 1.10536 12.2652 1 12 1Z"
                fill={props.fill}
            />
        </SvgIcon>
    );
};

export const MoreIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" {...props}>
            <path
                d="M8 5C8.55228 5 9 4.55228 9 4C9 3.44772 8.55228 3 8 3C7.44772 3 7 3.44772 7 4C7 4.55228 7.44772 5 8 5Z"
                fill="inherit"
            />
            <path
                d="M8 9C8.55228 9 9 8.55228 9 8C9 7.44772 8.55228 7 8 7C7.44772 7 7 7.44772 7 8C7 8.55228 7.44772 9 8 9Z"
                fill="inherit"
            />
            <path
                d="M8 13C8.55228 13 9 12.5523 9 12C9 11.4477 8.55228 11 8 11C7.44772 11 7 11.4477 7 12C7 12.5523 7.44772 13 8 13Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const SortIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon viewBox="0 0 16 16" fill="none" style={{ width: '16px', height: '16px' }} {...props}>
            <path
                d="M13.8 10.302L12 12.102V2.00195H11V12.102L9.2 10.302L8.5 11.002L11.5 14.002L14.5 11.002L13.8 10.302Z"
                fill="inherit"
            />
            <path
                d="M4.5 2.00195L1.5 5.00195L2.2 5.70195L4 3.90195V14.002H5V3.90195L6.8 5.70195L7.5 5.00195L4.5 2.00195Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};

export const StepCompletedIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon viewBox="0 0 16 16" fill="none" style={{ width: '16px', height: '16px' }} {...props}>
            <rect width="16" height="16" transform="translate(0 0.000976562)" fill="white" />
            <path
                d="M7 10.708L4.5 8.20748L5.2065 7.50098L7 9.29398L10.7925 5.50098L11.5 6.20848L7 10.708Z"
                fill="#1D39C4"
            />
            <path
                d="M8 1.00098C6.61553 1.00098 5.26216 1.41152 4.11101 2.18069C2.95987 2.94986 2.06266 4.04311 1.53285 5.32219C1.00303 6.60128 0.86441 8.00874 1.13451 9.36661C1.4046 10.7245 2.07129 11.9718 3.05026 12.9507C4.02922 13.9297 5.2765 14.5964 6.63437 14.8665C7.99224 15.1366 9.3997 14.9979 10.6788 14.4681C11.9579 13.9383 13.0511 13.0411 13.8203 11.89C14.5895 10.7388 15 9.38545 15 8.00098C15 6.14446 14.2625 4.36398 12.9497 3.05123C11.637 1.73847 9.85652 1.00098 8 1.00098ZM8 14.001C6.81332 14.001 5.65328 13.6491 4.66658 12.9898C3.67989 12.3305 2.91085 11.3934 2.45673 10.2971C2.0026 9.20072 1.88378 7.99432 2.11529 6.83043C2.3468 5.66655 2.91825 4.59745 3.75736 3.75834C4.59648 2.91922 5.66558 2.34778 6.82946 2.11626C7.99335 1.88475 9.19975 2.00357 10.2961 2.4577C11.3925 2.91183 12.3295 3.68086 12.9888 4.66756C13.6481 5.65425 14 6.81429 14 8.00098C14 9.59228 13.3679 11.1184 12.2426 12.2436C11.1174 13.3688 9.5913 14.001 8 14.001Z"
                fill="#1D39C4"
            />
        </SvgIcon>
    );
};

export const StepIncompleteIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon viewBox="0 0 16 16" fill="none" style={{ width: '16px', height: '16px' }} {...props}>
            <rect width="16" height="16" transform="translate(0 0.000976562)" fill="white" />
            <path
                d="M3.85009 2.35107C3.27659 2.79116 2.77115 3.31345 2.35009 3.90107L3.15009 4.50107C3.51732 3.99178 3.95502 3.53724 4.45009 3.15107L3.85009 2.35107Z"
                fill="#C1C9D2"
            />
            <path
                d="M2.30009 6.15107L1.35009 5.85107C1.10858 6.54175 0.990112 7.26945 1.00009 8.00107H2.00009C1.99806 7.37213 2.09942 6.74714 2.30009 6.15107Z"
                fill="#C1C9D2"
            />
            <path
                d="M1.35009 10.2011C1.58197 10.8983 1.91921 11.5559 2.35009 12.1511L3.15009 11.5511C2.78863 11.045 2.50233 10.4892 2.30009 9.90107L1.35009 10.2011Z"
                fill="#C1C9D2"
            />
            <path
                d="M3.90009 13.6511C4.49526 14.082 5.15288 14.4192 5.85009 14.6511L6.15009 13.7011C5.56196 13.4988 5.0062 13.2125 4.50009 12.8511L3.90009 13.6511Z"
                fill="#C1C9D2"
            />
            <path
                d="M5.85009 1.35107L6.15009 2.30107C6.74616 2.10039 7.37116 1.99904 8.00009 2.00107V1.00107C7.26847 0.991089 6.54077 1.10955 5.85009 1.35107Z"
                fill="#C1C9D2"
            />
            <path
                d="M12.1001 13.6511C12.689 13.2121 13.2111 12.69 13.6501 12.1011L12.8501 11.5011C12.4783 12.023 12.022 12.4792 11.5001 12.8511L12.1001 13.6511Z"
                fill="#C1C9D2"
            />
            <path
                d="M13.7001 9.85107L14.6501 10.1511C14.8676 9.4545 14.9854 8.73066 15.0001 8.00107H14.0001C14.0021 8.63001 13.9008 9.255 13.7001 9.85107Z"
                fill="#C1C9D2"
            />
            <path
                d="M14.6001 5.80107C14.3682 5.10385 14.031 4.44624 13.6001 3.85107L12.8001 4.45107C13.1616 4.95718 13.4479 5.51294 13.6501 6.10107L14.6001 5.80107Z"
                fill="#C1C9D2"
            />
            <path
                d="M12.0501 2.30107C11.4549 1.87019 10.7973 1.53295 10.1001 1.30107L9.80009 2.25107C10.3882 2.45331 10.944 2.73961 11.4501 3.10107L12.0501 2.30107Z"
                fill="#C1C9D2"
            />
            <path
                d="M10.1501 14.6511L9.85009 13.7011C9.25403 13.9017 8.62903 14.0031 8.00009 14.0011V15.0011C8.72679 14.9578 9.44718 14.8405 10.1501 14.6511Z"
                fill="#C1C9D2"
            />
        </SvgIcon>
    );
};

export const StepInProgressIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon viewBox="0 0 16 16" fill="none" style={{ width: '16px', height: '16px' }} {...props}>
            <rect width="16" height="16" transform="translate(0 0.000976562)" fill="white" />
            <path
                d="M11.8821 3.43063L12.5246 2.66488C11.904 2.13951 11.1963 1.72651 10.4336 1.44448L10.0918 2.38328C10.745 2.62556 11.3508 2.98 11.8821 3.43063Z"
                fill="#1D39C4"
            />
            <path
                d="M13.905 7.00098L14.8889 6.79458C14.7506 5.99566 14.4734 5.22714 14.07 4.52383L13.2044 5.00098C13.5499 5.62298 13.7868 6.29934 13.905 7.00098Z"
                fill="#1D39C4"
            />
            <path
                d="M10.0918 13.6187L10.4336 14.5575C11.1963 14.2754 11.904 13.8624 12.5246 13.3371L11.8821 12.5713C11.3508 13.022 10.745 13.3764 10.0918 13.6187Z"
                fill="#1D39C4"
            />
            <path
                d="M13.2044 11.001L14.07 11.501C14.4737 10.7896 14.7508 10.0135 14.8891 9.20738L13.905 9.03393C13.7867 9.72513 13.5497 10.3906 13.2044 11.001Z"
                fill="#1D39C4"
            />
            <path
                d="M8 15.001V1.00098C6.14348 1.00098 4.36301 1.73847 3.05025 3.05123C1.7375 4.36398 1 6.14446 1 8.00098C1 9.85749 1.7375 11.638 3.05025 12.9507C4.36301 14.2635 6.14348 15.001 8 15.001Z"
                fill="#1D39C4"
            />
        </SvgIcon>
    );
};

export const OverflowMenuHorizontal = (props: SvgIconProps) => {
    return (
        <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M4 9C4.55228 9 5 8.55228 5 8C5 7.44772 4.55228 7 4 7C3.44772 7 3 7.44772 3 8C3 8.55228 3.44772 9 4 9Z"
                fill="#334466"
            />
            <path
                d="M8 9C8.55228 9 9 8.55228 9 8C9 7.44772 8.55228 7 8 7C7.44772 7 7 7.44772 7 8C7 8.55228 7.44772 9 8 9Z"
                fill="#334466"
            />
            <path
                d="M12 9C12.5523 9 13 8.55228 13 8C13 7.44772 12.5523 7 12 7C11.4477 7 11 7.44772 11 8C11 8.55228 11.4477 9 12 9Z"
                fill="#334466"
            />
        </SvgIcon>
    );
};

export const LocalFileIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M28 20.002H26V22.002H28V28.002H4V22.002H6V20.002H4C3.46976 20.0026 2.96142 20.2135 2.58649 20.5884C2.21155 20.9634 2.00064 21.4717 2 22.002V28.002C2.00064 28.5322 2.21155 29.0405 2.58649 29.4155C2.96142 29.7904 3.46976 30.0013 4 30.002H28C28.5302 30.0013 29.0386 29.7904 29.4135 29.4155C29.7884 29.0405 29.9994 28.5322 30 28.002V22.002C29.9994 21.4717 29.7884 20.9634 29.4135 20.5884C29.0386 20.2135 28.5302 20.0026 28 20.002Z"
                fill="#0D1011"
            />
            <path
                d="M7 26.002C7.55228 26.002 8 25.5542 8 25.002C8 24.4497 7.55228 24.002 7 24.002C6.44772 24.002 6 24.4497 6 25.002C6 25.5542 6.44772 26.002 7 26.002Z"
                fill="#0D1011"
            />
            <path
                d="M22.707 7.29495L17.707 2.29495C17.5195 2.1074 17.2652 2.00201 17 2.00195H11C10.4698 2.00256 9.9614 2.21347 9.58646 2.58841C9.21152 2.96335 9.00061 3.47171 9 4.00195V20.002C9.00061 20.5322 9.21152 21.0406 9.58646 21.4155C9.9614 21.7904 10.4698 22.0013 11 22.002H21C21.5302 22.0013 22.0386 21.7904 22.4135 21.4155C22.7885 21.0406 22.9994 20.5322 23 20.002V8.00195C22.9999 7.73676 22.8946 7.48245 22.707 7.29495ZM20.5857 8.00195H17V4.41605L20.5857 8.00195ZM11 20.002V4.00195H15V8.00195C15.0006 8.5322 15.2115 9.04055 15.5865 9.41549C15.9614 9.79043 16.4698 10.0013 17 10.002H21V20.002H11Z"
                fill="#0D1011"
            />
        </SvgIcon>
    );
};

export const OtherSourcesIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M16 18.002H6C5.46957 18.002 4.96086 17.7912 4.58579 17.4162C4.21071 17.0411 4 16.5324 4 16.002V6.00195C4 5.47152 4.21071 4.96281 4.58579 4.58774C4.96086 4.21267 5.46957 4.00195 6 4.00195H16C16.5304 4.00195 17.0391 4.21267 17.4142 4.58774C17.7893 4.96281 18 5.47152 18 6.00195V16.002C18 16.5324 17.7893 17.0411 17.4142 17.4162C17.0391 17.7912 16.5304 18.002 16 18.002ZM6 6.00195V16.002H16V6.00195H6Z"
                fill="#0D1011"
            />
            <path
                d="M26 12.002V16.002H22V12.002H26ZM26 10.002H22C21.4696 10.002 20.9609 10.2127 20.5858 10.5877C20.2107 10.9628 20 11.4715 20 12.002V16.002C20 16.5324 20.2107 17.0411 20.5858 17.4162C20.9609 17.7912 21.4696 18.002 22 18.002H26C26.5304 18.002 27.0391 17.7912 27.4142 17.4162C27.7893 17.0411 28 16.5324 28 16.002V12.002C28 11.4715 27.7893 10.9628 27.4142 10.5877C27.0391 10.2127 26.5304 10.002 26 10.002Z"
                fill="#0D1011"
            />
            <path
                d="M26 22.002V26.002H22V22.002H26ZM26 20.002H22C21.4696 20.002 20.9609 20.2127 20.5858 20.5877C20.2107 20.9628 20 21.4715 20 22.002V26.002C20 26.5324 20.2107 27.0411 20.5858 27.4162C20.9609 27.7912 21.4696 28.002 22 28.002H26C26.5304 28.002 27.0391 27.7912 27.4142 27.4162C27.7893 27.0411 28 26.5324 28 26.002V22.002C28 21.4715 27.7893 20.9628 27.4142 20.5877C27.0391 20.2127 26.5304 20.002 26 20.002Z"
                fill="#0D1011"
            />
            <path
                d="M16 22.002V26.002H12V22.002H16ZM16 20.002H12C11.4696 20.002 10.9609 20.2127 10.5858 20.5877C10.2107 20.9628 10 21.4715 10 22.002V26.002C10 26.5324 10.2107 27.0411 10.5858 27.4162C10.9609 27.7912 11.4696 28.002 12 28.002H16C16.5304 28.002 17.0391 27.7912 17.4142 27.4162C17.7893 27.0411 18 26.5324 18 26.002V22.002C18 21.4715 17.7893 20.9628 17.4142 20.5877C17.0391 20.2127 16.5304 20.002 16 20.002Z"
                fill="#0D1011"
            />
        </SvgIcon>
    );
};

export const ExcelIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <g opacity="0.2">
                <g opacity="0.2">
                    <path
                        d="M27.8889 8.02124H12.6901C12.162 8.02124 11.7339 8.44937 11.7339 8.97749V27.3487C11.7339 27.8769 12.162 28.305 12.6901 28.305H27.8889C28.417 28.305 28.8451 27.8769 28.8451 27.3487V8.97749C28.8451 8.44937 28.417 8.02124 27.8889 8.02124Z"
                        fill="#161616"
                    />
                </g>
            </g>
            <g opacity="0.12">
                <g opacity="0.12">
                    <path
                        d="M27.8889 8.02124H12.6901C12.162 8.02124 11.7339 8.44937 11.7339 8.97749V27.3487C11.7339 27.8769 12.162 28.305 12.6901 28.305H27.8889C28.417 28.305 28.8451 27.8769 28.8451 27.3487V8.97749C28.8451 8.44937 28.417 8.02124 27.8889 8.02124Z"
                        fill="white"
                    />
                </g>
            </g>
            <path
                d="M20.5999 8.03217H12.7249C12.6003 8.03066 12.4768 8.05408 12.3614 8.10103C12.2461 8.14799 12.1413 8.21754 12.0533 8.30559C11.9652 8.39365 11.8957 8.49842 11.8487 8.61376C11.8018 8.72909 11.7783 8.85266 11.7799 8.97717V13.0947H20.6449L20.5999 8.03217Z"
                fill="#21A366"
            />
            <path
                d="M27.8896 8.03217H20.5996V13.0947H28.8459V8.96592C28.8444 8.84182 28.8185 8.71922 28.7696 8.60513C28.7208 8.49104 28.6499 8.38769 28.5611 8.30098C28.4723 8.21427 28.3673 8.14591 28.2521 8.09978C28.1369 8.05366 28.0137 8.03069 27.8896 8.03217Z"
                fill="#33C481"
            />
            <path d="M28.8346 18.1571H20.5996V23.2309H28.8346V18.1571Z" fill="#107C41" />
            <path
                d="M20.599 23.2309V18.1571H11.734V27.3484C11.7324 27.4729 11.7559 27.5964 11.8028 27.7118C11.8498 27.8271 11.9193 27.9319 12.0074 28.0199C12.0954 28.108 12.2002 28.1775 12.3155 28.2245C12.4309 28.2715 12.5544 28.2949 12.679 28.2934H27.889C28.014 28.2948 28.1381 28.2715 28.254 28.2247C28.37 28.1779 28.4755 28.1085 28.5645 28.0206C28.6534 27.9327 28.724 27.828 28.7722 27.7126C28.8204 27.5972 28.8452 27.4734 28.8452 27.3484V23.2309H20.599Z"
                fill="#185C37"
            />
            <path d="M20.6101 13.0837H11.7339V18.1575H20.6101V13.0837Z" fill="#107C41" />
            <path d="M28.8346 13.0836H20.5996V18.1574H28.8346V13.0836Z" fill="#21A366" />
            <path
                d="M17.1114 12.4541H7.61641C7.08828 12.4541 6.66016 12.8822 6.66016 13.4104V22.9054C6.66016 23.4335 7.08828 23.8616 7.61641 23.8616H17.1114C17.6395 23.8616 18.0677 23.4335 18.0677 22.9054V13.4104C18.0677 12.8822 17.6395 12.4541 17.1114 12.4541Z"
                fill="#107C41"
            />
            <path
                d="M9.60693 21.2509L11.5982 18.1571L9.77568 15.0746H11.2494L12.2394 17.0321C12.3148 17.1691 12.3787 17.3121 12.4307 17.4596C12.4929 17.3091 12.5643 17.1626 12.6444 17.0209L13.7694 15.0634H15.1194L13.1957 18.1571L15.1194 21.2734H13.6794L12.5544 19.1021C12.4945 19.0146 12.4454 18.9202 12.4082 18.8209C12.3708 18.9181 12.3256 19.0121 12.2732 19.1021L11.0807 21.2734L9.60693 21.2509Z"
                fill="white"
            />
            <g opacity="0.5">
                <path
                    opacity="0.5"
                    d="M17.1114 12.4541H7.61641C7.08828 12.4541 6.66016 12.8822 6.66016 13.4104V22.9054C6.66016 23.4335 7.08828 23.8616 7.61641 23.8616H17.1114C17.6395 23.8616 18.0677 23.4335 18.0677 22.9054V13.4104C18.0677 12.8822 17.6395 12.4541 17.1114 12.4541Z"
                    fill="url(#paint0_linear_5558_25490)"
                />
            </g>
            <defs>
                <linearGradient
                    id="paint0_linear_5558_25490"
                    x1="8.64016"
                    y1="11.7116"
                    x2="16.0877"
                    y2="24.6041"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="white" stopOpacity="0.5" />
                    <stop offset="1" stopOpacity="0.7" />
                </linearGradient>
            </defs>
        </SvgIcon>
    );
};

export const BackIcon = (props: SvgIconProps) => (
    <SvgIcon {...props} viewBox="0 0 24 24" fill="none">
        <path
            d="M10.5 19.5L11.5575 18.4425L5.8725 12.75H21V11.25H5.8725L11.5575 5.5575L10.5 4.5L3 12L10.5 19.5Z"
            fill="inherit"
        />
    </SvgIcon>
);

export const CloseIconOutlineIcon = (props: SvgIconProps) => (
    <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
            d="M8 1C4.1 1 1 4.1 1 8C1 11.9 4.1 15 8 15C11.9 15 15 11.9 15 8C15 4.1 11.9 1 8 1ZM8 14C4.7 14 2 11.3 2 8C2 4.7 4.7 2 8 2C11.3 2 14 4.7 14 8C14 11.3 11.3 14 8 14Z"
            fill={props.fill || '#161616'}
        />
        <path
            d="M10.7 11.5L8 8.8L5.3 11.5L4.5 10.7L7.2 8L4.5 5.3L5.3 4.5L8 7.2L10.7 4.5L11.5 5.3L8.8 8L11.5 10.7L10.7 11.5Z"
            fill={props.fill || '#161616'}
        />
    </SvgIcon>
);

export const IncreaseIcon = (props: SvgIconProps) => (
    <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
            d="M10 4V5H13.2929L9 9.29295L6.8535 7.1465C6.80709 7.10005 6.75199 7.0632 6.69133 7.03806C6.63067 7.01292 6.56566 6.99998 6.5 6.99998C6.43434 6.99998 6.36933 7.01292 6.30867 7.03806C6.24801 7.0632 6.19291 7.10005 6.1465 7.1465L1 12.2929L1.70705 13L6.5 8.20705L8.6465 10.3535C8.69291 10.3999 8.74801 10.4368 8.80867 10.4619C8.86932 10.4871 8.93434 10.5 9 10.5C9.06566 10.5 9.13068 10.4871 9.19133 10.4619C9.25199 10.4368 9.30709 10.3999 9.3535 10.3535L14 5.70705V9H15V4H10Z"
            fill={props.fill || '#161616'}
        />
    </SvgIcon>
);

export const TimerIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path
                d="M8 15C6.61553 15 5.26216 14.5895 4.11101 13.8203C2.95987 13.0511 2.06266 11.9579 1.53285 10.6788C1.00303 9.3997 0.86441 7.99224 1.13451 6.63437C1.4046 5.2765 2.07129 4.02922 3.05026 3.05026C4.02922 2.07129 5.2765 1.4046 6.63437 1.13451C7.99224 0.86441 9.3997 1.00303 10.6788 1.53285C11.9579 2.06266 13.0511 2.95987 13.8203 4.11101C14.5895 5.26216 15 6.61553 15 8C15 9.85652 14.2625 11.637 12.9497 12.9497C11.637 14.2625 9.85652 15 8 15ZM8 2C6.81332 2 5.65328 2.3519 4.66658 3.01119C3.67989 3.67047 2.91085 4.60755 2.45673 5.7039C2.0026 6.80026 1.88378 8.00666 2.11529 9.17054C2.3468 10.3344 2.91825 11.4035 3.75736 12.2426C4.59648 13.0818 5.66558 13.6532 6.82946 13.8847C7.99335 14.1162 9.19975 13.9974 10.2961 13.5433C11.3925 13.0892 12.3295 12.3201 12.9888 11.3334C13.6481 10.3467 14 9.18669 14 8C14 6.4087 13.3679 4.88258 12.2426 3.75736C11.1174 2.63214 9.5913 2 8 2Z"
                fill="#161616"
            />
            <path d="M10.295 11L7.5 8.205V3.5H8.5V7.79L11 10.295L10.295 11Z" fill="#161616" />
        </SvgIcon>
    );
};

export const ClearIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path d="M14.9999 13.5H3.49989V14.5H14.9999V13.5Z" fill="#161616" />
            <path
                d="M13.6899 5.255L9.72489 1.295C9.63202 1.20202 9.52173 1.12826 9.40033 1.07794C9.27893 1.02761 9.1488 1.00171 9.01739 1.00171C8.88597 1.00171 8.75585 1.02761 8.63445 1.07794C8.51305 1.12826 8.40276 1.20202 8.30989 1.295L1.30989 8.295C1.21691 8.38787 1.14315 8.49816 1.09283 8.61955C1.0425 8.74095 1.0166 8.87108 1.0166 9.00249C1.0166 9.13391 1.0425 9.26404 1.09283 9.38544C1.14315 9.50683 1.21691 9.61712 1.30989 9.71L3.56489 12H8.35989L13.6899 6.67C13.7829 6.57712 13.8566 6.46683 13.9069 6.34544C13.9573 6.22404 13.9832 6.09391 13.9832 5.9625C13.9832 5.83108 13.9573 5.70095 13.9069 5.57956C13.8566 5.45816 13.7829 5.34787 13.6899 5.255ZM7.94489 11H3.99989L1.99989 9L5.15489 5.845L9.11989 9.805L7.94489 11ZM9.82489 9.12L5.86489 5.155L8.99989 2L12.9999 5.965L9.82489 9.12Z"
                fill="inherit"
            />
        </SvgIcon>
    );
};
