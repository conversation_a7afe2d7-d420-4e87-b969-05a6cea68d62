const singleSpaDefaults = require('webpack-config-single-spa-react-ts');
const { rspack } = require('@rspack/core');
const { merge } = require('webpack-merge');
const dotenv = require('dotenv');
dotenv.config();
module.exports = (webpackConfigEnv, argv) => {
    const defaultConfig = singleSpaDefaults({
        orgName: 'tripudiotech',
        projectName: 'api',
        webpackConfigEnv,
        argv,
    });

    const isDevelopment = process.env.SOURCE_MAP || false;
    return merge(defaultConfig, {
        // modify the webpack config however you'd like to by adding to this object
        plugins: [
            new rspack.DefinePlugin({
                'process.env': JSON.stringify(process.env),
            }),
        ],
        devtool: isDevelopment,
    });
};
