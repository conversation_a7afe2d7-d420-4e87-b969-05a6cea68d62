import { Method } from '../services/fetch';
declare const trackingService: {
    trackViewedEntity(entityType: string, entityId: string, signal?: AbortSignal): Promise<void>;
    getRecentlyViewed(actorId: any, limit: any, offset: any): Promise<{
        data: any[];
        pageInfo: {
            nextOffset: number;
            lastRow: boolean;
        };
    } | {
        data: any;
        pageInfo?: undefined;
    }>;
    getActivity: {
        method: Method;
        url: string;
    };
};
export default trackingService;
//# sourceMappingURL=index.d.ts.map