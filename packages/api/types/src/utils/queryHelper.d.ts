import type Attribute from '../schema/model/attributes.model';
export declare const QUERY: {
    readonly OR: "$or";
    readonly AND: "$and";
    readonly EXACT: "$exact";
    readonly NOT_IN: "$not_in";
    readonly IN: "$in";
    readonly IS_NULL: "$is_null";
    readonly IS_NON_NULL: "$is_non_null";
    readonly LT: "$lt";
    readonly LTE: "$lte";
    readonly GT: "$gt";
    readonly GTE: "$gte";
    readonly REGEX: "$regex";
    readonly CONTAINS: "$contains";
    readonly NOT_CONTAINS: "$not_contains";
    readonly EQUAL: "$eq";
    readonly NOT: "$not";
    readonly NOT_EQUAL: "$neq";
    readonly BETWEEN: "$btw";
};
export declare const QUERY_TYPES: ("$or" | "$and" | "$exact" | "$not_in" | "$in" | "$is_null" | "$is_non_null" | "$lt" | "$lte" | "$gt" | "$gte" | "$regex" | "$contains" | "$not_contains" | "$eq" | "$not" | "$neq" | "$btw")[];
export declare const QUERY_OPERATOR_OPTIONS: readonly [{
    readonly label: "Or";
    readonly value: "$or";
}, {
    readonly label: "And";
    readonly value: "$and";
}, {
    readonly label: "Contains";
    readonly value: "$contains";
}, {
    readonly label: "Exact";
    readonly value: "$exact";
}, {
    readonly label: "In";
    readonly value: "$in";
}, {
    readonly label: "Not In";
    readonly value: "$not_in";
}, {
    readonly label: "Is Null";
    readonly value: "$is_null";
}, {
    readonly label: "Is not Null";
    readonly value: "$is_non_null";
}, {
    readonly label: "Less Than";
    readonly value: "$lt";
}, {
    readonly label: "Less Than or Equal";
    readonly value: "$lte";
}, {
    readonly label: "Greater Than";
    readonly value: "$gt";
}, {
    readonly label: "Greater Than or Equal";
    readonly value: "$gte";
}, {
    readonly label: "Regular Expression";
    readonly value: "$regex";
}, {
    readonly label: "Equal";
    readonly value: "$eq";
}, {
    readonly label: "Not Equal";
    readonly value: "$neq";
}];
export declare const buildNotEqualOperatorQuery: (field: any, value: any) => Record<string, any>;
export declare const buildNotOperatorQuery: (field: any, value: any) => Record<string, any>;
export declare const buildEqualOperatorQuery: (field: any, value: any) => Record<string, any>;
export declare const buildOrOperatorQuery: (queryList: any) => Record<string, any>;
export declare const buildAndOperatorQuery: (queryList: any) => Record<string, any>;
export declare const buildExactQuery: (field: any, value: any) => Record<string, any>;
export declare const buildNotInQuery: (field: any, value: any) => Record<string, any>;
export declare const buildInQuery: (field: any, value: any) => Record<string, any>;
export declare const buildNotNullQuery: (field: any) => Record<string, any>;
export declare const buildNullQuery: (field: any) => Record<string, any>;
export declare const buildLessThanQuery: (field: any, value: any) => Record<string, any>;
export declare const buildlessThanOrEqualQuery: (field: any, value: any) => Record<string, any>;
export declare const buildGreaterThanQuery: (field: any, value: any) => Record<string, any>;
export declare const buildGreaterThanOrEqualQuery: (field: any, value: any) => Record<string, any>;
export declare const buildRegexQuery: (field: any, value: any) => Record<string, any>;
export declare const buildContainsQuery: (field: string, value: string) => Record<string, any>;
export declare const getCriteriasQuery: (key: any, item: any) => Record<string, any>;
export declare const extractAndOrCriterias: (operation: any, condition: any) => Record<string, any>;
export interface QueryCondition {
    [QUERY.AND]: QueryCondition[];
    [QUERY.OR]: QueryCondition[];
    [QUERY.IN]: {
        [key: string]: string[];
    };
    [QUERY.NOT_IN]: {
        [key: string]: string[];
    };
    [QUERY.CONTAINS]: {
        [key: string]: string;
    };
    [QUERY.EXACT]: {
        [key: string]: string;
    };
    [QUERY.IS_NULL]: string;
    [QUERY.IS_NON_NULL]: string;
    [QUERY.LT]: {
        [key: string]: string;
    };
    [QUERY.LTE]: {
        [key: string]: string;
    };
    [QUERY.GT]: {
        [key: string]: string;
    };
    [QUERY.GTE]: {
        [key: string]: string;
    };
    [QUERY.REGEX]: {
        [key: string]: string;
    };
    [QUERY.EQUAL]: {
        [key: string]: string;
    };
    [QUERY.NOT_EQUAL]: {
        [key: string]: string;
    };
    [QUERY.BETWEEN]: {
        [key: string]: string[];
    };
    [key: string]: any;
}
export declare const convertQuery: (queryString: string) => string;
export declare const convertQueryObject: (query: Partial<QueryCondition>) => string;
export declare const getQueryStringFromGridFilterModel: (filterModel: Record<string, any>, selectedTypes: string[]) => string;
export declare const extractSchemaTypes: (criteria: any) => any;
export declare const buildCascadedQueries: (attributes: Attribute[], fieldName: string, values: Record<string, any>) => Record<string, any>[];
//# sourceMappingURL=queryHelper.d.ts.map