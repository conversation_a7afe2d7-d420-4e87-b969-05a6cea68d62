import Schema from '../schema/model/schema.model';
import Attribute from '../schema/model/attributes.model';
export declare const DEFAULT_SELECTED_COLUMNS: {
    field: string;
    headerName: string;
}[];
export declare const FIXED_COLUMNS: {
    field: string;
    headerName: string;
}[];
export declare const filterAndSortAttributes: (attributes: Record<string, Attribute>, attributeOrder?: string[]) => Attribute[];
export declare const createLayoutFromSchema: (schema: Schema) => {
    Name: string;
};
//# sourceMappingURL=reportHelper.d.ts.map