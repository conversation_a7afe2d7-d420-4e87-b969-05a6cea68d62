import { Method } from '../services/fetch';
declare const assetServiceUrl: {
    checkinDocument: {
        method: Method;
        url: string;
    };
    documentCheckout: {
        method: Method;
        url: string;
    };
    getAllDocument: {
        method: Method;
        url: string;
    };
    deleteDocument: {
        method: Method;
        url: string;
    };
    previewFile: {
        method: Method;
        url: string;
    };
    getFolders: {
        method: Method;
        url: string;
    };
    updateFolder: {
        method: Method;
        url: string;
    };
    getFolderFiles: {
        method: Method;
        url: string;
    };
    deleteFolder: {
        method: Method;
        url: string;
    };
    createFolder: {
        method: Method;
        url: string;
    };
    moveFile: {
        method: Method;
        url: string;
    };
    copyFile: {
        method: Method;
        url: string;
    };
    downloadFile: {
        method: Method;
        url: string;
    };
    uploadFile: {
        method: Method;
        url: string;
    };
    deleteFile: {
        method: Method;
        url: string;
    };
};
export default assetServiceUrl;
/**
    Form data doesn't accept boolean values only string. All the boolean fields are string here.
*/
interface BuildCheckInForm {
    file: File;
    override?: string;
    revise?: string;
    local?: string;
    primary?: string;
    lock?: string;
}
export declare const buildCheckInForm: ({ file, override, revise, lock, primary, }: BuildCheckInForm) => FormData;
export declare const buildDownloadDocumentResponseError: (status: any) => "There's no document uploaded to this entity." | "An unexpected error has occurred while loading the document.";
//# sourceMappingURL=index.d.ts.map