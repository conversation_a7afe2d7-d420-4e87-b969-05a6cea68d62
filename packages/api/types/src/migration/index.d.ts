import { Method } from '../services/fetch';
declare const migrationUrls: {
    readonly import: {
        readonly method: Method.POST;
        readonly url: `${string}/import`;
    };
    readonly getImportJobs: {
        readonly method: Method.GET;
        readonly url: `${string}/import`;
    };
    readonly getImportDetails: {
        readonly method: Method.GET;
        readonly url: `${string}/import/:id`;
    };
    readonly getFailedImport: {
        readonly method: Method.GET;
        readonly url: `${string}/import/:id/fail`;
    };
    readonly getSuccessImport: {
        readonly method: Method.GET;
        readonly url: `${string}/import/:id/success`;
    };
    readonly export: {
        readonly method: Method.POST;
        readonly url: `${string}/export`;
    };
    readonly createExportTemplate: {
        readonly method: Method.POST;
        readonly url: `${string}/export_template`;
    };
    readonly updateExportTemplate: {
        readonly method: Method.PUT;
        readonly url: `${string}/export_template/:id`;
    };
    readonly deleteExportTemplate: {
        readonly method: Method.DELETE;
        readonly url: `${string}/export_template/:id`;
    };
    readonly getExportTemplates: {
        readonly method: Method.GET;
        readonly url: `${string}/export_template`;
    };
    readonly getExportTemplateDetail: {
        readonly method: Method.GET;
        readonly url: `${string}/export_template/:id`;
    };
    readonly createImportTemplate: {
        readonly method: Method.POST;
        readonly url: `${string}/import_template`;
    };
    readonly updateImportTemplate: {
        readonly method: Method.PUT;
        readonly url: `${string}/import_template/:id`;
    };
    readonly getImportTemplates: {
        readonly method: Method.GET;
        readonly url: `${string}/import_template`;
    };
    readonly downloadFailedRows: {
        readonly method: Method.GET;
        readonly url: `${string}/import/:id/fail/download`;
    };
    readonly downloadOriginalFile: {
        readonly method: Method.GET;
        readonly url: `${string}/import/:id/download`;
    };
};
export default migrationUrls;
//# sourceMappingURL=index.d.ts.map