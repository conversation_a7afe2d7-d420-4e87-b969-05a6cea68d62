import Attribute from './attributes.model';
import EntityType from './entity-type.model';
import { RelationType } from './relation.model';
export default interface Schema {
    entityType: EntityType;
    attributes: Record<string, Attribute>;
    schemaName: string;
    lifeCycles: LifecycleInfo[];
    relationTypes: RelationType[];
    classifications?: string[];
    displayName: string;
}
export interface LifecycleInfo {
    id: string;
    isDefault: boolean;
}
//# sourceMappingURL=schema.model.d.ts.map