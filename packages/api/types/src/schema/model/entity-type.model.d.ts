export default interface EntityType {
    createdAt: string;
    disabled: boolean;
    id: string;
    updatedAt: string;
    abstract: boolean;
    description: string;
    extendable: boolean;
    master: boolean;
    name: string;
    revisable: boolean;
    system: boolean;
    displayName: string;
    attributeOrder: string[];
    uniqueKeys: string[];
}
//# sourceMappingURL=entity-type.model.d.ts.map