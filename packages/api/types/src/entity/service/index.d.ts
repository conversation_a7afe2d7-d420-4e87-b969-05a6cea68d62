import { Method } from '../../services/fetch';
declare const entityUrls: {
    readonly getListEntity: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityType`;
    };
    readonly getEntityById: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityType/:entityId`;
    };
    readonly getEntityAccesses: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityId/access`;
    };
    readonly createEntity: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:entityType`;
    };
    readonly updateEntity: {
        readonly method: Method.PUT;
        readonly url: `${string}/entity/:entityId`;
    };
    readonly deleteEntity: {
        readonly method: Method.DELETE;
        readonly url: `${string}/entity/:entityId`;
    };
    readonly getEntityRelations: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:fromEntityId/:relationType/:entityType`;
    };
    readonly createRelation: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:fromEntityId/:relationType/:toEntityId`;
    };
    readonly deleteRelation: {
        readonly method: Method.DELETE;
        readonly url: `${string}/entity/:fromEntityId/:relationType/:toEntityId`;
    };
    readonly getListEntitySysRoot: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/SysRoot`;
    };
    readonly getRelationsUnderEntity: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityType/:entityId/relation`;
    };
    readonly getAllRelationsUnderEntity: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityId/relation`;
    };
    readonly getBOMList: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityId/bom`;
    };
    readonly lockEntity: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:entityId/lock`;
    };
    readonly unlockEntity: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:entityId/unlock`;
    };
    readonly grantPermission: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:entityId/grant/:stateName`;
    };
    readonly revokePermission: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:entityId/revoke/:stateName`;
    };
    readonly whereUsed: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityId/usedIn`;
    };
    readonly batchRequest: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/batch`;
    };
    readonly swapBOM: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:bomId/swap/:toComponentId`;
    };
    readonly applyClassifications: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:entityId/classification`;
    };
    readonly updateRelation: {
        readonly method: Method.PUT;
        readonly url: `${string}/entity/:fromEntityId/relation/:toEntityId`;
    };
    readonly promote: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:entityId/promote/:stateName`;
    };
    readonly demote: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:entityId/demote/:stateName`;
    };
    readonly revise: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:entityId/revise`;
    };
    readonly generateEBom: {
        readonly method: Method.POST;
        readonly url: `${string}/entity/:entityId/convert-ebom`;
    };
    readonly getManufacturerParts: {
        readonly method: Method.GET;
        readonly url: `${string}/part/:entityId/manufacturer`;
    };
    readonly createDiscussion: {
        readonly method: Method.POST;
        readonly url: `${string}/discussion`;
    };
    readonly getDiscussions: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityId/discussion`;
    };
    readonly getDiscussionMessages: {
        readonly method: Method.GET;
        readonly url: `${string}/discussion/:id/message`;
    };
    readonly deleteDiscussion: {
        readonly method: Method.DELETE;
        readonly url: `${string}/discussion/:id`;
    };
    readonly addMessageToDiscussion: {
        readonly method: Method.POST;
        readonly url: `${string}/discussion/:id/message`;
    };
    readonly replyMessage: {
        readonly method: Method.POST;
        readonly url: `${string}/discussion/:discussionId/message/:messageId/reply`;
    };
    readonly deleteMessage: {
        readonly method: Method.DELETE;
        readonly url: `${string}/discussion/:discussionId/message/:messageId`;
    };
    readonly getRollup: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityType/:entityId/rollup/:bugEntityType`;
    };
    readonly getImpactAnalysis: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityType/:entityId/impactAnalysis`;
    };
    readonly getBugRevisionSummary: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/:entityType/:entityId/bugRevisionSummary`;
    };
    readonly getAllEventType: {
        readonly method: Method.GET;
        readonly url: `${string}/entity/event-type`;
    };
    readonly createDecomposition: {
        readonly method: Method.POST;
        readonly url: `${string}/decomposition/:decompositionType`;
    };
};
export default entityUrls;
//# sourceMappingURL=index.d.ts.map