declare const entityTypeService: {
    getListEntity(entityType: string, offset?: number, limit?: number): Promise<import("axios").AxiosResponse<any, any>>;
    getEntityById(entityType: string, entityId: string): Promise<import("axios").AxiosResponse<any, any>>;
    createEntity(entityType: string, data: any): Promise<import("axios").AxiosResponse<any, any>>;
    updateEntity(entityId: any, data: any): Promise<import("axios").AxiosResponse<any, any>>;
    deleteEntity(entityId: any): Promise<import("axios").AxiosResponse<any, any>>;
};
export default entityTypeService;
//# sourceMappingURL=entity.service.d.ts.map