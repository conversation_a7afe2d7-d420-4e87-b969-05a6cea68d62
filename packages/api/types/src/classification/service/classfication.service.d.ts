import ClassificationSchemaTree from "../model/classfication-tree.model";
import Classification from "../model/classfication.model";
declare const classificationService: {
    getClassificationTree(): Promise<import("axios").AxiosResponse<ClassificationSchemaTree, any>>;
    createClassification(name: any, classification: any): Promise<import("axios").AxiosResponse<any, any>>;
    getClassification(name: string): Promise<import("axios").AxiosResponse<Classification, any>>;
    updateClassification(name: string, classification: any): Promise<import("axios").AxiosResponse<any, any>>;
    deleteClassification(name: string): Promise<import("axios").AxiosResponse<any, any>>;
    createClassificationAttribute(classificationName: string, attribute: any): Promise<import("axios").AxiosResponse<any, any>>;
    deleteAttribute(classificationName: string, attributeName: string): Promise<import("axios").AxiosResponse<any, any>>;
};
export default classificationService;
//# sourceMappingURL=classfication.service.d.ts.map