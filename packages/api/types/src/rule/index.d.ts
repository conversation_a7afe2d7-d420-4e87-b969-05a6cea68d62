import { Method } from '../services/fetch';
declare const ruleUrls: {
    getRules: {
        method: Method;
        url: string;
    };
    invokeRuleById: {
        method: Method;
        url: string;
    };
    invokeRuleByEntityType: {
        method: Method;
        url: string;
    };
};
export interface RuleResult {
    id: string;
    name: string;
    description: string;
    status: string;
    message: string;
    ruleType: string;
}
export default ruleUrls;
//# sourceMappingURL=index.d.ts.map