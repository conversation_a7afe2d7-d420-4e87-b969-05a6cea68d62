import { Method } from '../services/fetch';
declare const reportingUrls: {
    readonly getReports: {
        readonly method: Method.GET;
        readonly url: `${string}/report`;
    };
    readonly createReport: {
        readonly method: Method.POST;
        readonly url: `${string}/report`;
    };
    readonly getReportDetail: {
        readonly method: Method.GET;
        readonly url: `${string}/report/:reportId`;
    };
    readonly getReportExecutions: {
        readonly method: Method.GET;
        readonly url: `${string}/report/:reportId/execution`;
    };
    readonly updateReport: {
        readonly method: Method.PUT;
        readonly url: `${string}/report/:reportId`;
    };
    readonly deleteReport: {
        readonly method: Method.DELETE;
        readonly url: `${string}/report/:reportId`;
    };
    readonly getReportLayouts: {
        readonly method: Method.GET;
        readonly url: `${string}/report/:reportId/report-layout`;
    };
    readonly createReportLayout: {
        readonly method: Method.POST;
        readonly url: `${string}/report/:reportId/report-layout`;
    };
    readonly getReportSchedules: {
        readonly method: Method.GET;
        readonly url: `${string}/report/:reportId/schedule`;
    };
    readonly createReportSchedule: {
        readonly method: Method.POST;
        readonly url: `${string}/report/:reportId/schedule`;
    };
    readonly updateReportSchedule: {
        readonly method: Method.PUT;
        readonly url: `${string}/report/:reportId/schedule/:scheduleId`;
    };
    readonly deleteReportSchedule: {
        readonly method: Method.DELETE;
        readonly url: `${string}/report/:reportId/schedule/:scheduleId`;
    };
    readonly disableReportSchedule: {
        readonly method: Method.POST;
        readonly url: `${string}/report/:reportId/schedule/:scheduleId/disable`;
    };
    readonly enableReportSchedule: {
        readonly method: Method.POST;
        readonly url: `${string}/report/:reportId/schedule/:scheduleId/enable`;
    };
    readonly getReportLayoutDetail: {
        readonly method: Method.GET;
        readonly url: `${string}/report-layout/:reportLayoutId`;
    };
    readonly updateReportLayout: {
        readonly method: Method.PUT;
        readonly url: `${string}/report-layout/:reportLayoutId`;
    };
    readonly deleteReportLayout: {
        readonly method: Method.DELETE;
        readonly url: `${string}/report-layout/:reportLayoutId`;
    };
};
export default reportingUrls;
//# sourceMappingURL=index.d.ts.map