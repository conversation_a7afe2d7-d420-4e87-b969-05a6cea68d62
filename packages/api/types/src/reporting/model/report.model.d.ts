export interface ReportSchedule {
    id: string;
    name: string;
    description: string;
    recipients: string[];
    layoutId: string;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy: string;
    disabled: boolean;
    timezone: string;
    cron: string;
    startDate: string;
    endDate: string;
}
export declare enum ScheduleStatus {
    ACTIVE = "ACTIVE",
    DISABLED = "DISABLED"
}
//# sourceMappingURL=report.model.d.ts.map