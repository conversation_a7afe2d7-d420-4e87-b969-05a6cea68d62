import { Method } from '../services/fetch';
declare const processUrls: {
    readonly getTasks: {
        readonly method: Method.GET;
        readonly url: `${string}/tasks`;
    };
    readonly getEngineUserTasks: {
        readonly method: Method.GET;
        readonly url: `${string}/task`;
    };
    readonly createTasks: {
        readonly method: Method.POST;
        readonly url: `${string}/tasks`;
    };
    readonly getProcessInstances: {
        readonly method: Method.GET;
        readonly url: `${string}/process-instance`;
    };
    readonly getHistoryProcessInstances: {
        readonly method: Method.GET;
        readonly url: `${string}/history/process-instance`;
    };
    readonly getHistoryProcessActivityInstances: {
        readonly method: Method.GET;
        readonly url: `${string}/history/activity-instance`;
    };
    readonly getProcessInstanceActivities: {
        readonly method: Method.GET;
        readonly url: `${string}/process-instance/:processInstanceId/activity-instances`;
    };
    readonly getProcessDefinition: {
        readonly method: Method.GET;
        readonly url: `${string}/process-definition/:definitionId/xml`;
    };
    readonly getProcessInstanceIncidents: {
        readonly method: Method.GET;
        readonly url: `${string}/incident`;
    };
    readonly getProcessTaskDetails: {
        readonly method: Method.GET;
        readonly url: `${string}/task/:taskId`;
    };
    readonly getProcessTaskVariables: {
        readonly method: Method.GET;
        readonly url: `${string}/task/:taskId/variables`;
    };
    readonly completeTask: {
        readonly method: Method.POST;
        readonly url: `${string}/task/:taskId/complete`;
    };
    readonly queryTask: {
        readonly method: Method.POST;
        readonly url: `${string}/task`;
    };
    readonly updateTask: {
        readonly method: Method.PUT;
        readonly url: `${string}/task/:taskId`;
    };
    readonly getTaskComments: {
        readonly method: Method.GET;
        readonly url: `${string}/task/:taskId/comment`;
    };
    readonly addTaskComments: {
        readonly method: Method.POST;
        readonly url: `${string}/task/:taskId/comment/create`;
    };
    readonly getHistoryUserOperation: {
        readonly method: Method.GET;
        readonly url: `${string}/history/user-operation`;
    };
    readonly getTaskAttachments: {
        readonly method: Method.GET;
        readonly url: `${string}/task/:taskId/attachment`;
    };
    readonly createTaskAttachment: {
        readonly method: Method.POST;
        readonly url: `${string}/task/:taskId/attachment/create`;
    };
    readonly downloadAttachment: {
        readonly method: Method.GET;
        readonly url: `${string}/task/:taskId/attachment/:attachmentId/data`;
    };
    readonly deleteAttachment: {
        readonly method: Method.DELETE;
        readonly url: `${string}/task/:taskId/attachment/:attachmentId`;
    };
    readonly claimTask: {
        readonly method: Method.POST;
        readonly url: `${string}/task/:taskId/claim`;
    };
    readonly setRetries: {
        readonly method: Method.PUT;
        readonly url: `${string}/external-task/:taskId/retries`;
    };
};
export default processUrls;
export declare const buildAttachmentForm: (file: any, description?: any) => FormData;
//# sourceMappingURL=index.d.ts.map