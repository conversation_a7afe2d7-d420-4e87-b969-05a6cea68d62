export declare const CAD_3D_VIEW_ATTRIBUTE = "3dDocumentId";
export declare enum SYSTEM_RELATION {
    HAS_MASTER = "HAS_MASTER",
    HAS_ASSEMBLY = "HAS_ASSEMBLY",
    AFFECTED_ITEM = "AFFECTED_ITEM",
    RESOLVED_BY = "RESOLVED_BY",
    REPORTED_ON = "REPORTED_ON",
    INTRODUCED_IN = "INTRODUCED_IN",
    LINKED_ISSUE = "LINKED_ISSUE",
    ALTERNATES_TO = "ALTERNATES_TO",
    CHANGING_ITEM = "CHANGING_ITEM",
    CHANGE_REQUEST = "CHANGE_REQUEST",
    REFERENCE_TO = "REFERENCE_TO",
    HAS_COMPONENT = "HAS_COMPONENT",
    SUBSTITUTES_TO = "SUBSTITUTES_TO",
    IMPLEMENTS = "IMPLEMENTS",
    BELONGS_TO = "BELONGS_TO",
    TASK_OF = "TASK_OF",
    PROPOSAL_OF = "PROPOSAL_OF",
    ENGINEERING_REVISION_OF = "ENGINEERING_REVISION_OF",
    IMPACT_ANALYSIS = "IMPACT_ANALYSIS",
    SUB_OF = "SUB_OF",
    MANUF_EQUIV_PRT_NO = "MANUF_EQUIV_PRT_NO",
    ACCESSOR = "ACCESSOR",
    HAS_SUPPLIER = "HAS_SUPPLIER",
    HAS_ROLE = "HAS_ROLE",
    OWNED_BY = "OWNED_BY",
    AUTHORED_BY = "AUTHORED_BY",
    WORKS_FOR = "WORKS_FOR",
    IMPLEMENTATION_TASK = "IMPLEMENTATION_TASK"
}
export declare enum BASE_LF_STATE {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE"
}
export declare enum SYSTEM_ENTITY_TYPE {
    SYS_ROOT = "SysRoot",
    DOCUMENT = "Document",
    DOCUMENT_MASTER = "DocumentMaster",
    PART_MASTER = "PartMaster",
    PART = "Part",
    CAD_PART = "CADPart",
    SPECIFICATION = "Specification",
    ISSUE = "Issue",
    BUG = "bug",
    CHANGE_REQUEST = "ChangeRequest",
    PLANNED_CHANGE = "PlannedChange",
    CHANGE_ORDER = "ChangeOrder",
    CHANGE_ITEM = "ChangeItem",
    TASK = "Task",
    PERSON = "Person",
    USER_GROUP = "UserGroup",
    BOM = "BillOfMaterial",
    CAD_BOM = "CadBOM",
    ENGINEERING_BOM = "eBOM",
    MANUFACTURING_BOM = "mBOM",
    IMPACT_ANALYSIS = "ImpactAnalysis",
    FOLDER = "Folder",
    FILE = "File",
    MPN_QUALIFICATION = "MpnQualification",
    MANUFACTURER_PART = "ManufPart",
    CUSTOMER_PROJECT = "CustomerProject",
    SUPPLIER_INFO = "SupplierInfo",
    EXTERNAL_COMPANY = "ExternalCompany",
    THREED_CAD_MODEL = "3DCadModel",
    SEARCH_QUERY = "SearchQuery",
    AGENT = "Agent",
    REPORT = "Report",
    REPORT_LAYOUT = "ReportLayout"
}
export declare enum CHANGE_ACTION {
    UPDATE_ATTRIBUTE = "UPDATE_ATTRIBUTE"
}
export declare enum RELATION_DIRECTION {
    OUTGOING = "Outgoing",
    INCOMING = "Incoming"
}
export declare enum SYSTEM_ATTRIBUTE {
    NAME = "name",
    DISPLAY_NAME = "displayName",
    DESCRIPTION = "description",
    REQUIRED_INFORMATION = "requiredInformation"
}
export declare enum DISTRIBUTION_LIST_RELATION {
    CHANGE_MANAGEMENT_GROUP = "CHANGE_MANAGEMENT_GROUP",
    FEEDBACK_PROVIDER = "FEEDBACK_PROVIDER",
    ANALYSIS_PERFORMER = "ANALYSIS_PERFORMER",
    DECISION_TEAM = "DECISION_TEAM",
    AGENT = "AGENT",
    NOTIFICATION_ONLY = "NOTIFICATION_ONLY"
}
export declare enum DISTRIBUTION_LIST_TYPE {
    CHANGE_MANAGEMENT_GROUP = "Change Management Group",
    FEEDBACK_PROVIDER = "Feedback Provider",
    ANALYSIS_PERFORMER = "Analysis Performer",
    DECISION_TEAM = "Decision Team",
    NOTIFICATION_ONLY = "Notification Only",
    AGENT = "Agent"
}
export declare enum SORT_DIRECTION {
    DESCENDING = "DESCENDING",
    ASCENDING = "ASCENDING"
}
export declare enum PERMISSION {
    CAN_CHECK_IN = "canCheckIn",
    CAN_CHECK_OUT = "canCheckOut",
    CAN_CONNECT_AS_FROM_SIDE = "canConnectAsFromSide",
    CAN_CONNECT_AS_TO_SIDE = "canConnectAsToSide",
    CAN_COPY = "canCopy",
    CAN_CREATE = "canCreate",
    CAN_DELETE = "canDelete",
    CAN_DEMOTE = "canDemote",
    CAN_DISCONNECT_AS_FROM_SIDE = "canDisconnectAsFromSide",
    CAN_DISCONNECT_AS_TO_SIDE = "canDisconnectAsToSide",
    CAN_DOWNLOAD = "canDownload",
    CAN_GRANT_ACCESS = "canGrantAccess",
    CAN_LOCK = "canLock",
    CAN_MODIFY = "canModify",
    CAN_PROMOTE = "canPromote",
    CAN_PUBLISH = "canPublish",
    CAN_READ = "canRead",
    CAN_REVISE = "canRevise",
    CAN_REVOKE_ACCESS = "canRevokeAccess",
    CAN_UNLOCK = "canUnlock",
    CAN_CLASSIFY = "canClassify"
}
export declare enum PERMISSION_TYPES {
    ANY = "Any",
    VIEWER = "Viewer",
    CONTRIBUTOR = "Contributor",
    OWNER = "Owner",
    ADMIN = "Admin"
}
export declare const AGENT_TYPE: {
    readonly PERSON: "Person";
    readonly TEAM: "Team";
    readonly DEPARTMENT: "Department";
    readonly COMPANY: "Company";
    readonly INTERNAL_COMPANY: "InternalCompany";
    readonly EXTERNAL_COMPANY: "ExternalCompany";
};
export declare const AGENT_TYPE_OPTIONS: readonly [{
    readonly label: "User";
    readonly value: "Person";
}, {
    readonly label: "Team";
    readonly value: "Team";
}, {
    readonly label: "Department";
    readonly value: "Department";
}, {
    readonly label: "External Company";
    readonly value: "ExternalCompany";
}, {
    readonly label: "Internal Company";
    readonly value: "InternalCompany";
}];
export declare const PLANNED_CHANGE_STATE: {
    ACCEPTED: string;
    REJECTED: string;
    DRAFT: string;
};
export declare const ROOT_FOLDER_ID: "Root";
//# sourceMappingURL=system-type.d.ts.map