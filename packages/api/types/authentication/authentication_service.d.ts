import { Workspace } from '../tripudiotech-api';
declare const authenticationService: {
    getToken(): string;
    getAuthUrl(): string;
    getTenant(): string;
    getWorkspace(): Workspace | null;
    getUserInfo(): Promise<any>;
    getAvatar(email: any): string;
    login(): void;
    getGlobalConfigs(): Promise<any>;
};
export default authenticationService;
//# sourceMappingURL=authentication_service.d.ts.map