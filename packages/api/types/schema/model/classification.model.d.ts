import Attribute from './attributes.model';
export default interface ClassificationSchema {
    classification: Classification;
    attributes: Record<string, Attribute>;
    applicableEntityTypes: string[];
}
export interface Classification {
    id: string;
    name: string;
    description: string;
    attributeOrder: string[];
    canRead: boolean;
    canEdit: boolean;
    allowAccessOverride: boolean;
}
//# sourceMappingURL=classification.model.d.ts.map
