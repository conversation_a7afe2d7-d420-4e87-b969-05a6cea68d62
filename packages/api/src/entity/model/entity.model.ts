/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Classification, Schema } from '../../tripudiotech-api';

/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export default interface SysRoot {
    name: string;
    description: string;
    type: string;
    title: string;
    hasThumbnail: boolean;
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
    id: string;
    system: boolean;
    disabled: boolean;
}

export interface EntityDetail {
    id: string;
    alternates: any[];
    authoredIn?: {
        id: string;
        description: string;
        name: string;
        isDefault: boolean;
        isSystem: boolean;
        logo: string;
    };
    classifications: Classification[];
    createdAt: string;
    createdBy: string;
    updatedAt: string;
    updatedBy: string;
    disabled: boolean;
    entitySchema: Schema;
    lifecycle: {
        name: string;
        id: string;
    };
    locked: boolean;
    lockedBy: string;
    owner: {
        id: string;
        name: string;
        type: string;
    };
    permissions: {
        canCheckIn: boolean;
        canCheckOut: boolean;
        canClassify: boolean;
        canConnectAsFromSide: boolean;
        canConnectAsToSide: boolean;
        canCopy: boolean;
        canCreate: boolean;
        canDelete: boolean;
        canDemote: boolean;
        canDisconnectAsFromSide: boolean;
        canDisconnectAsToSide: boolean;
        canDownload: boolean;
        canGrantAccess: boolean;
        canLock: boolean;
        canModify: boolean;
        canPromote: boolean;
        canPublish: boolean;
        canRead: boolean;
        canRevise: boolean;
        canRevokeAccess: boolean;
        canUnlock: boolean;
    };
    properties: Record<'type' | 'name' | 'title', any> & Record<string, any>;
    schemaType: string[];
    state: {
        name: string;
        id: string;
        isObsoleted: boolean;
        isOfficial: boolean;
        isSuperseded: boolean;
    };
    relationInfo?: Record<string, Array<Record<string, any>>>;
}

export interface EventType {
    name: string;
    description: string;
    isProcess: boolean;
}
