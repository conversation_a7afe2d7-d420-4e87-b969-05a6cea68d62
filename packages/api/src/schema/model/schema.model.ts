/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import Attribute from './attributes.model';
import EntityType from './entity-type.model';
import { RelationType } from './relation.model';

export default interface Schema {
    entityType: EntityType;
    attributes: Record<string, Attribute>;
    schemaName: string;
    lifeCycles: LifecycleInfo[];
    relationTypes: RelationType[];
    classifications?: string[];
    displayName: string;
    masterModel: boolean;
}

export interface LifecycleInfo {
    id: string;
    isDefault: boolean;
}
