/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
// Anything exported from this file is importable by other in-browser modules.
export { default as Schema } from './schema/model/schema.model';
export { default as SchemaTree } from './schema/model/schema-tree.model';
export {
    default as Attribute,
    AttributeGroup,
    AttributeGroupWithValue,
    AttributeOrGroup,
} from './schema/model/attributes.model';
export { default as EntityType } from './schema/model/entity-type.model';
export { type ReportSchedule, ScheduleStatus } from './reporting/model/report.model';
export { default as AttributeType } from './schema/model/attribute-type.enum';
export { default as schemaService } from './schema/schema.service';
export { default as schemaUrls } from './schema';
export { genAiUrls } from './gen-ai';
export { default as migrationUrls } from './migration';
export { default as workspaceUrls } from './workspace';
export { default as AttributePageInfo } from './schema/model/attributes-info.model';
export { type PageResponse } from './model/page-response.model';
export { type PageInfo } from './model/page-info.model';
export * from './model/api-response-error.model';
export { default as EntityLifeCycle } from './schema/model/entity-lifecycle.model';
export type { RelationType, RelationSide, RelationTypePermission, RelationSchema } from './schema/model/relation.model';
export { default as DigitalThreadSchema } from './schema/model/digital-thread.model';
export { default as SysRoot, EntityDetail, EventType } from './entity/model/entity.model';
export { RevisionPattern } from './lifecycles/model/revision-pattern.enum';
export { default as Lifecycle, LifecycleState } from './lifecycles/model/lifecycle.model';
export { default as classificationService } from './classification/service/classfication.service';
export { default as Classification } from './classification/model/classfication.model';
export { default as ClassificationDetail } from './classification/model/classification-detail.model';
export { default as ClassificationSchemaTree } from './classification/model/classfication-tree.model';
export { default as ruleUrls, type RuleResult } from './rule';
export function publicApiFunction() {}
export { default as entityService } from './entity/service/entity.service';
export { default as savedFilterUrls } from './saved-filter';
export { default as fetch, Method, generatePath } from './services/fetch';
export { getAvatarUrl, getThumbnailUrl, getCadPreviewFolder } from './services/media';
export { default as entityUrls } from './entity/service';
export { default as classificationUrls } from './classification/service';
export { default as User } from './users/model/user.model';
export { default as Users } from './users/model/users.model';
export { default as userService } from './users/service/users.service';
export { default as userUrls } from './users';
export const COOKIE_DOMAIN = process.env.COOKIE_DOMAIN;
export { default as authenticationService } from './authentication/authentication_service';
export { default as trackingService } from './tracking';
export { default as changeUrls } from './change';
export { default as reportingUrls } from './reporting';
export const IDENTITY_SERVICE_URL = process.env.IDENTITY_SERVICE_URL;
export {
    buildContainsQuery,
    buildOrOperatorQuery,
    buildAndOperatorQuery,
    buildExactQuery,
    buildNotInQuery,
    buildlessThanOrEqualQuery,
    buildInQuery,
    getCriteriasQuery,
    buildNullQuery,
    extractAndOrCriterias,
    buildRegexQuery,
    QUERY,
    QUERY_TYPES,
    QUERY_OPERATOR_OPTIONS,
    buildNotOperatorQuery,
    buildEqualOperatorQuery,
    buildNotEqualOperatorQuery,
    convertQuery,
    convertQueryObject,
    getQueryStringFromGridFilterModel,
    type QueryCondition,
    extractSchemaTypes,
    buildCascadedQueries,
} from './utils/queryHelper';
export {
    default as batchRequestBody,
    batchRequest,
    handleBatchRequestRes,
    batchWithProgress,
    type BatchRequestData,
    type BatchRequestResponse,
} from './utils/batchRequest';
export { downloadFile, downloadBlob } from './utils/download';
export { default as Lifecycles } from './schema/model/lifecycle.model';
export { default as assetServiceUrl, buildCheckInForm, buildDownloadDocumentResponseError } from './assets';
export { CONTENT_ROLE } from './constants/roles';
export * from './constants/export';
export { AGENT_SUBTYPE } from './constants/agent';
export { CUSTOM_EVENTS } from './constants/events';
export * from './constants/system-type';
export { RULE_CONDITION } from './constants/rule';
export { default as processUrls, buildAttachmentForm } from './process';
export const SYNCFUSION_WS_URL = `${process.env.BASE_URL}/syncfusion`;
export { default as batchRequestUrls } from './batchRequestUrl';
export * from './workspace/model/workspace.model';
export * from './utils/reportHelper';
export const DEFAULT_CLIENT_SIDE_LIMIT = 2000;
export const DEFAULT_BATCH_CHUNK_SIZE = 40;
export { type AxiosResponse } from 'axios';
export * from './constants/error-type';
export * from './google';
