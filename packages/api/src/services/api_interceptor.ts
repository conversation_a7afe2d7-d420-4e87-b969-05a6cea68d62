/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import axios, { AxiosRequestConfig } from 'axios';
import authenticationService from '../authentication/authentication_service';
import { Method } from './fetch';
import toUpper from 'lodash/toUpper';

const axiosInstance = axios.create({
    baseURL: process.env.BASE_URL,
    headers: {
        'X-Tenant-Id': authenticationService.getTenant(),
    },
});

// Match root segments: /entity, /process, /asset, /change-manager
const WORKPACE_SCOPED_SERVICE_PATHS = ['/entity', '/process', '/asset', '/change-manager'];

function isWorkspaceScopedRequest(config: AxiosRequestConfig): boolean {
    const url = config.url || '';
    const baseUrl = config.baseURL || '';
    return WORKPACE_SCOPED_SERVICE_PATHS.some((path) => url.startsWith(baseUrl + path));
}

// Request Interceptor
axiosInstance.interceptors.request.use(
    function (config) {
        const token = authenticationService.getToken();
        const tenantId = authenticationService.getTenant();
        const selectedWorkspace = authenticationService.getWorkspace()?.name;

        if (token && toUpper(config.method) !== Method.OPTIONS) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        if (tenantId) {
            config.headers['X-Tenant-Id'] = tenantId;
        }
        
        if (selectedWorkspace && isWorkspaceScopedRequest(config)) {
            config.headers['X-Glide-Workspace'] = selectedWorkspace;
        }

        return config;
    },
    function (error) {
        return Promise.reject(error);
    }
);

// Response Interceptor
axiosInstance.interceptors.response.use(
    function (response) {
        return response;
    },
    function (error) {
        return Promise.reject(error);
    }
);

export default axiosInstance;
