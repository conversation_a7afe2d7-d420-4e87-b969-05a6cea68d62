/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import axiosInstance from '../services/api_interceptor';

import KeyCloak from 'keycloak-js';

const parseTenant = () => {
    return window.localStorage.getItem('tenantId');
};

const getPlantName = () => {
    return window.localStorage.getItem('plantName');
};

const DEFAULT_KC_CLIENT_ID = 'sp-services';

const authenticationService = {
    getToken() {
        const token: string = window.sessionStorage.getItem('token');
        return token;
    },
    getAuthUrl() {
        return `${process.env.AUTH_SERVICE_URL}`;
    },
    getTenant() {
        return parseTenant();
    },
    getPlant() {
        return getPlantName();
    },
    async getUserInfo() {
        const headers = {
            'Content-Type': 'application/json',
            'X-Tenant-Id': parseTenant(),
        };
        return axiosInstance
            .get(`${process.env.AUTH_SERVICE_URL}/user/me`, { headers })
            .then((response) => response.data);
    },
    getAvatar(email) {
        return `${
            process.env.IDENTITY_SERVICE_URL
        }/auth/realms/${authenticationService.getTenant()}/avatar-provider/${email}`;
    },
    login() {
        const keycloak = new KeyCloak({
            url: `${process.env.IDENTITY_SERVICE_URL}/auth`,
            realm: parseTenant(),
            clientId: DEFAULT_KC_CLIENT_ID,
        });
        keycloak
            .init({
                onLoad: 'login-required',
                checkLoginIframe: false,
            })
            .then(() => keycloak.login());
    },
    async getGlobalConfigs() {
        const headers = {
            'Content-Type': 'application/json',
            'X-Tenant-Id': parseTenant(),
        };
        return axiosInstance
            .get(`${process.env.AUTH_SERVICE_URL}/tenant/${parseTenant()}/config`, { headers })
            .then((response) => response.data);
    },
};

export default authenticationService;
