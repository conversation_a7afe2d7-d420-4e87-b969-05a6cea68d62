/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { trackingService, SYSTEM_ENTITY_TYPE, getThumbnailUrl } from '@tripudiotech/api';
import { useAuth } from '@tripudiotech/caching-store';
import {
    Loading,
    tableStyles,
    ComponentLifecycle,
    DocThumbnail,
    formatDateTime,
    Thumbnail,
    EmptyDocument,
} from '@tripudiotech/styleguide';
import isNull from 'lodash/isNull';
import { AgGridReact } from '@ag-grid-community/react';
import { Box, Chip, styled, Typography } from '@mui/material';
import get from 'lodash/get';
import { TimeIcon, TypeIcon } from '../icons';
import { getStatusVariant } from '../../utils/common';
import { Link } from 'react-router-dom';
import { INFINITE_BATCH_SIZE } from '../../constant';
import { useStore } from '../../store';

type EntityViewedEvent = {
    actorId: string;
    entity: {
        id: string;
        lifecycle: {
            name: string;
            id: string;
        };
        locked: boolean;
        lockedBy: {};
        owner: {
            id: string;
            name: string;
            type: string;
        };
        properties: {
            hasThumbnail: boolean;
            name: string;
            title: string;
            type: string;
            description: string;
            revision: string | null;
            primaryFileExtension: string | null;
            isMaster: boolean | null;
        };
    };
};

const getProperty = (entity, key) => {
    return get(entity, ['properties', key]);
};

const EntityWrapper = styled(Box)(({ theme }) => ({
    display: 'flex',
    gap: '16px',
    width: '100%',
    alignItems: 'center',
    position: 'relative',
    cursor: 'pointer',
    textDecoration: 'none',
    color: 'inherit',
    '& .state': {
        position: 'absolute',
        right: 0,
    },
    '& .info': {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'space-between',
        width: '100%',
        gap: '2px',
    },
    '& .thumbnailContainer': {
        height: '36px',
        minWidth: '36px',
        width: '36px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },
    '& .title': {
        display: 'flex',
        fontSize: '14px',
        fontWeight: 500,
        gap: '4px',
        '& .text': { fontWeight: 500, fontSize: '14px' },
        '& .name': {
            color: theme.palette.glide.text.normal.inverseTertiary,
        },
        '& .revision': {
            color: theme.palette.success.main,
        },
        '& .master': {
            color: theme.palette.info.main,
        },
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        maxWidth: '88%',
    },
    '& .basicInfo': {
        display: 'flex',
        gap: '12px',
    },
    '& .text': {
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
    },
    '& .field': {
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        '& .text': {
            fontSize: '10px',
            fontWeight: 400,
            color: theme.palette.glide.text.normal.inverseSecondary,
            '&.description': {
                color: theme.palette.glide.text.normal.inverseTertiary,
                fontWeight: 500,
            },
        },
        maxWidth: '50%',
    },
}));

const NoRowsOverlay = () => (
    <div style={{ transform: 'translateY(-20%)' }}>
        <EmptyDocument />
        <Typography
            sx={{
                fontSize: '16px',
                color: (theme) => theme.palette.glide.text.normal.main,
                fontWeight: 500,
                textAlign: 'center',
                px: '6px',
            }}
        >
            Your recently viewed items will appear here. Browse to get started!
        </Typography>
    </div>
);

const EntityViewedRenderer = ({ data }: { data: EntityViewedEvent }) => {
    const entity: any = get(data, 'entity', {});
    const isMaster = getProperty(entity, 'isMaster');
    const revision = getProperty(entity, 'revision');
    const hasThumbnail = getProperty(entity, 'hasThumbnail');
    const primaryFileExtension = getProperty(entity, 'primaryFileExtension');
    const entityType = getProperty(entity, 'type');
    const entityId = get(entity, 'id');
    const isDocument = get(data, 'entity.schemaType', []).some((type) =>
        [SYSTEM_ENTITY_TYPE.DOCUMENT, SYSTEM_ENTITY_TYPE.DOCUMENT_MASTER].includes(type)
    );
    const title = getProperty(entity, 'title');
    return data ? (
        /**@ts-ignore */
        <EntityWrapper component={Link} to={`/detail/${entityType}/${entityId}/properties`}>
            <Box className="thumbnailContainer">
                {isDocument ? (
                    <DocThumbnail type={primaryFileExtension} />
                ) : (
                    <Thumbnail
                        hasThumbnail={hasThumbnail}
                        size={{ width: 32, height: 32 }}
                        url={getThumbnailUrl(entityType, entityId)}
                    />
                )}
            </Box>
            <Box className="info">
                <div className="title">
                    <Typography className="text name">
                        {getProperty(entity, 'name')}
                        {isMaster || revision ? '  - ' : ''}
                    </Typography>
                    {revision && <Typography className="text revision">{revision}</Typography>}
                    {isMaster && <Typography className="text master">Master</Typography>}
                </div>
                <div className="basicInfo">
                    <div className="field">
                        <TypeIcon />
                        <Typography className="text">{entityType}</Typography>
                    </div>
                    <div className="field">
                        <ComponentLifecycle style={{ height: '12px', width: '12px' }} />
                        <Typography className="text">{get(entity, 'lifecycle.name')}</Typography>
                    </div>
                </div>
                <div className="basicInfo">
                    {title && (
                        <div className="field">
                            <Typography className="text description">{title}</Typography>
                        </div>
                    )}
                    <div className="field">
                        <TimeIcon />
                        <Typography className="text">{formatDateTime(get(data, 'eventTimeStamp'))}</Typography>
                    </div>
                </div>
            </Box>
            <div className="state">
                <Chip size="small" label={get(entity, 'state.name')} variant={getStatusVariant(get(entity, 'state'))} />
            </div>
        </EntityWrapper>
    ) : (
        ''
    );
};

const RecentlyViewed = () => {
    const gridRef = useRef<AgGridReact>();

    const userInfo = useAuth((state) => state.userInfo);
    const offset = useRef(0);
    const { widgets } = useStore();
    useEffect(() => {
        // To avoid blank page when scroll offset is reset when widget is dropped but not updating to the library
        gridRef.current?.api?.ensureIndexVisible(0);
    }, [widgets]);
    const createServerSideDataSource = useCallback(() => {
        return {
            getRows: (params: any) => {
                offset.current === 0 && gridRef.current.api.showLoadingOverlay();
                trackingService
                    .getRecentlyViewed(userInfo.email, INFINITE_BATCH_SIZE, offset.current)
                    .then(({ data, pageInfo }: any) => {
                        if (isNull(data)) {
                            params.failCallback();
                            return;
                        }
                        offset.current = pageInfo.nextOffset;
                        const rowsThisPage = data;
                        const { lastRow } = pageInfo;
                        params.successCallback(rowsThisPage, lastRow ? params.startRow + data.length : -1);
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();
                        if (gridRef.current.api.getDisplayedRowCount() === 0) {
                            gridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, [userInfo]);

    const gridOptions: any = useMemo(() => {
        return {
            rowHeight: 62,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: false,
                resizable: false,
                flex: 1,
                filter: false,
            },
            getRowId: (params) => params.data.messageKey,
            columnDefs: [
                {
                    field: 'messageKey',
                    headerName: 'Entity',
                    flex: 1,
                    cellRenderer: EntityViewedRenderer,
                },
            ],
            cacheBlockSize: INFINITE_BATCH_SIZE,
            rowModelType: 'infinite',
            rowStyle: {
                border: 'none',
            },
        };
    }, []);

    const handleSetDataSource = useCallback(
        (event) => {
            const dataSource = createServerSideDataSource();
            event.api.setDatasource(dataSource);
        },
        [userInfo, gridOptions]
    );
    return (
        <div className="scrollableContainer">
            <div
                className="content"
                style={{
                    height: '100%',
                    width: '100%',
                }}
            >
                {userInfo && (
                    <Box
                        sx={{
                            height: '100%',
                            width: '100%',
                            '& .ag-header': {
                                display: 'none',
                            },
                            ...tableStyles,
                        }}
                    >
                        <AgGridReact
                            className="ag-theme-alpine"
                            ref={gridRef}
                            {...gridOptions}
                            onGridReady={handleSetDataSource}
                            noRowsOverlayComponent={NoRowsOverlay}
                        />
                    </Box>
                )}
            </div>
        </div>
    );
};

export default RecentlyViewed;
