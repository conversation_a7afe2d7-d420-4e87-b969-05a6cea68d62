/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useRef, useMemo, useCallback, useState, memo } from 'react';
import { Chip, styled, Typography, Avatar, Box } from '@mui/material';
import { processUrls, fetch, getAvatarUrl, authenticationService } from '@tripudiotech/api';
import { MainTooltip, notifyError, tableStyles, Loading, EmptyDocument } from '@tripudiotech/styleguide';
import { useAuth } from '@tripudiotech/caching-store';
import { Task } from '../../model/index';
import get from 'lodash/get';
import WidgetItemTitle from '../Base/WidgetItemTitle';
import { TimeIcon } from '../icons';
import { diffDays } from '../../utils/common';
import isEmpty from 'lodash/isEmpty';
import isNil from 'lodash/isNil';
import WidgetField from '../Base/WidgetField';
import { useNavigate } from 'react-router-dom';
import { INFINITE_BATCH_SIZE } from '../../constant';
import { useStore } from '../../store';
import { AgGridReact } from '@ag-grid-community/react';

const TaskWrapper = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    cursor: 'pointer',
    '& .taskInfo': {
        display: 'flex',
        flexDirection: 'column',
        width: '70%',
        gap: '2px',
    },
    '& .status': {
        display: 'flex',
        gap: '12.75px',
    },
    '& .avatar': {
        width: '24px',
        height: '24px',
    },
}));

const PROCESS_TASK_STATUS = {
    NEW: {
        label: 'New',
        variant: 'status-secondary',
    },
    ASSIGNED: {
        label: 'Assigned',
        variant: 'status-info',
    },
    OVERDUE: {
        label: 'Overdue',
        variant: 'status-error',
    },
    SUSPENDED: {
        label: 'Suspended',
        variant: 'status-warning',
    },
};

const RemainingDays = ({ date }) => {
    if (isNil(date) || isEmpty(date)) return <WidgetField icon={<TimeIcon />} value="No due" />;

    const parsedDate = new Date(date);
    const now = new Date();
    const duration = diffDays(parsedDate, now);
    const absoluteDuration = Math.abs(duration);

    if (duration < 0) {
        return (
            <WidgetField
                textClassName="overdue"
                icon={<TimeIcon />}
                value={`${absoluteDuration} day${absoluteDuration === 1 ? '' : 's'} overdue`}
            />
        );
    }
    return (
        <WidgetField
            icon={<TimeIcon />}
            textClassName={duration < 3 ? 'warning' : ''}
            value={`${absoluteDuration} day${absoluteDuration === 1 ? '' : 's'} remaining`}
        />
    );
};

const getTaskStatus = (task: Task) => {
    const { due, assignee } = task;

    const parsedDate = new Date(due);
    const now = new Date();
    const duration = diffDays(parsedDate, now);

    if (task.suspended) return PROCESS_TASK_STATUS.SUSPENDED;

    if (due && duration < 0) return PROCESS_TASK_STATUS.OVERDUE;

    if (assignee) {
        return PROCESS_TASK_STATUS.ASSIGNED;
    }

    return PROCESS_TASK_STATUS.NEW;
};

const TaskItem = ({ data, onClick }) => {
    if (!data) return '';

    const status = useMemo(() => {
        return getTaskStatus(data);
    }, [data]);

    const assignee = get(data, 'assignee', '');
    const description = get(data, 'description');

    return (
        <TaskWrapper onClick={() => onClick(data)}>
            <div className="taskInfo">
                <WidgetItemTitle>{data.name}</WidgetItemTitle>
                {description && <WidgetField value={description} />}
                <RemainingDays date={data.due} />
            </div>
            <div className="status">
                <Chip size="small" variant={status.variant as any} label={status.label} />
                <MainTooltip title={isEmpty(assignee) ? 'No assignee' : assignee}>
                    <span>
                        <Avatar className="avatar" alt={assignee} src={assignee ? getAvatarUrl(assignee) : ''} />
                    </span>
                </MainTooltip>
            </div>
        </TaskWrapper>
    );
};

const NoRowsOverlay = () => (
    <div style={{ transform: 'translateY(-20%)' }}>
        <EmptyDocument />
        <Typography
            sx={{
                fontSize: '16px',
                color: (theme) => theme.palette.glide.text.normal.main,
                fontWeight: 500,
                textAlign: 'center',
                px: '6px',
            }}
        >
            You're all caught up! No tasks assigned. Check back later.
        </Typography>
    </div>
);

const MyTasks = () => {
    const gridRef = useRef<AgGridReact>();
    const { userInfo } = useAuth();
    const widgets = useStore((state) => state.widgets);
    const [error, setError] = useState(null);
    useEffect(() => {
        // To avoid blank page when scroll offset is reset when widget is dropped but not updating to the library
        gridRef.current?.api?.ensureIndexVisible(0);
    }, [widgets]);
    const createServerSideDataSource = useCallback(() => {
        return {
            getRows: (params: any) => {
                params.startRow === 0 && gridRef.current.api.showLoadingOverlay();
                fetch({
                    ...processUrls.getEngineUserTasks,
                    qs: {
                        tenantIdIn: authenticationService.getTenant(),
                        sortBy: 'created',
                        sortOrder: 'desc',
                        maxResults: INFINITE_BATCH_SIZE,
                        firstResult: params.startRow,
                        involvedUser: userInfo?.email,
                    },
                    skipToast: true,
                })
                    .then(({ data }) => {
                        const lastRow = data.length < INFINITE_BATCH_SIZE ? params.startRow + data.length : -1;
                        params.successCallback(data, lastRow);
                    })
                    .catch(() => {
                        params.failCallback();
                        setError('An unxpected error occurred while fetching your tasks');
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();
                        if (gridRef.current.api.getDisplayedRowCount() === 0) {
                            gridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, [userInfo]);

    const onTaskClick = useCallback(async (task) => {
        gridRef.current.api.showLoadingOverlay();
        try {
            const { data } = await fetch({
                ...processUrls.getProcessTaskVariables,
                params: {
                    taskId: task.id,
                },
            });
            const entityId = get(data, ['entityId', 'value']);
            const type = get(data, ['type', 'value']);
            if (!entityId || !entityId) {
                return notifyError(
                    'Invalid task variables. Please make sure the process instance is setup with correct variables.'
                );
            }
            navigate(
                `/detail/${type}/${entityId}/process?processInstanceId=${task.processInstanceId}&taskId=${task.id}`
            );
        } catch (error) {
            notifyError('An error occurred while fetching the task');
        } finally {
            gridRef.current.api.hideOverlay();
        }
    }, []);
    const gridOptions: any = useMemo(() => {
        return {
            rowHeight: 62,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: false,
                resizable: false,
                flex: 1,
                filter: false,
            },
            getRowId: (params) => params.data.id,
            columnDefs: [
                {
                    field: 'doc',
                    headerName: 'Entity',
                    flex: 1,
                    cellRenderer: TaskItem,
                    cellRendererParams: {
                        onClick: onTaskClick,
                    },
                },
            ],
            cacheBlockSize: 20,
            rowModelType: 'infinite',
            rowStyle: {
                border: 'none',
            },
            noRowsOverlayComponent: NoRowsOverlay,
        };
    }, []);

    const handleSetDataSource = useCallback(
        (event) => {
            const dataSource = createServerSideDataSource();
            gridOptions.dataSource = dataSource;
            event.api.setDatasource(dataSource);
        },
        [gridOptions, userInfo]
    );

    const navigate = useNavigate();

    return error ? (
        <div className="scrollableContainer">
            <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    px: '32px',
                }}
            >
                <Typography>{error}</Typography>
            </Box>
        </div>
    ) : (
        <div className="scrollableContainer">
            <div className="content" style={{ height: '100%', width: '100%' }}>
                <Box
                    sx={{
                        height: '100%',
                        width: '100%',
                        '& .ag-header': {
                            display: 'none',
                        },
                        ...tableStyles,
                    }}
                >
                    {userInfo && (
                        <AgGridReact
                            className="ag-theme-alpine"
                            ref={gridRef}
                            {...gridOptions}
                            onGridReady={handleSetDataSource}
                        />
                    )}
                </Box>
            </div>
        </div>
    );
};

export default memo(MyTasks);
