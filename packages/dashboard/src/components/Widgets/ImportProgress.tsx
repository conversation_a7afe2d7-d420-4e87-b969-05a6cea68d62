/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, Button, Chip, Typography } from '@mui/material';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import { useStore } from '../../store';
import { fetch, migrationUrls } from '@tripudiotech/api';
import { IMPORT_STATUS_VARIANT, INFINITE_BATCH_SIZE } from '../../constant';
import { Loading, DocThumbnail, tableStyles, formatDateTime, EmptyDocument, PlusIcon } from '@tripudiotech/styleguide';
import WidgetItemTitle from '../Base/WidgetItemTitle';
import WidgetField from '../Base/WidgetField';
import { TimeIcon } from '../icons';
import { Link } from 'react-router-dom';

const NoRowsOverlay = () => (
    <div style={{ transform: 'translateY(-12%)' }}>
        <EmptyDocument />
        <Typography
            sx={{
                fontSize: '18px',
                color: (theme) => theme.palette.glide.text.normal.main,
                fontWeight: 500,
                textAlign: 'center',
            }}
        >
            You haven't imported anything yet.
        </Typography>
        <Link to={`/importing/new`}>
            <Button
                sx={{
                    minWidth: '32px',
                    mt: '6px',
                    pointerEvents: 'all',
                }}
                size="small"
                variant="contained-blue"
                endIcon={
                    <PlusIcon
                        sx={{
                            width: 16,
                            height: 16,
                        }}
                    />
                }
            >
                New Import
            </Button>
        </Link>
    </div>
);

const Item = ({ data }) => {
    return (
        <Box sx={{ display: 'flex', gap: '4px', alignItems: 'center', width: '100%' }}>
            <DocThumbnail type={data?.extension} alt={data?.originalFileName} />
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'space-between',
                    width: '70%',
                    gap: '4px',
                }}
            >
                <WidgetItemTitle>{data?.originalFileName}</WidgetItemTitle>
                <WidgetField icon={<TimeIcon />} value={formatDateTime(data?.updatedAt)} />
            </Box>
            <Chip
                sx={{ ml: 'auto' }}
                size="small"
                label={data?.fileImportStatus}
                variant={IMPORT_STATUS_VARIANT[data?.fileImportStatus]}
            />
        </Box>
    );
};

const ImportProgress = () => {
    const gridRef = useRef<AgGridReact>();
    const widgets = useStore((state) => state.widgets);
    const setSelectedImport = useStore((state) => state.setSelectedImport);
    useEffect(() => {
        // To avoid blank page when scroll offset is reset when widget is dropped but not updating to the library
        gridRef.current?.api?.ensureIndexVisible(0);
    }, [widgets]);

    const createServerSideDataSource = useCallback(() => {
        return {
            getRows: (params: any) => {
                params.startRow === 0 && gridRef.current.api.showLoadingOverlay();
                fetch({
                    ...migrationUrls.getImportJobs,
                    qs: {
                        limit: INFINITE_BATCH_SIZE,
                        offset: params.startRow,
                    },
                })
                    .then(({ data: { data, pageInfo } }) => {
                        const lastRow = params.endRow >= pageInfo.total ? pageInfo.total : -1;

                        params.successCallback(data, lastRow);
                    })
                    .catch(() => {
                        params.failCallback();
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();
                        if (gridRef.current.api.getDisplayedRowCount() === 0) {
                            gridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, []);

    const onRowClicked = useCallback(
        ({ data }) => {
            setSelectedImport(data);
        },
        [setSelectedImport]
    );

    const gridOptions: any = useMemo(() => {
        return {
            rowHeight: 62,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: false,
                resizable: false,
                flex: 1,
                filter: false,
            },
            getRowId: (params) => params.data.id,
            columnDefs: [
                {
                    field: 'fileImportStatus',
                    headerName: 'fileImportStatus',
                    flex: 1,
                    cellRenderer: Item,
                },
            ],
            cacheBlockSize: 20,
            rowModelType: 'infinite',
            rowStyle: {
                border: 'none',
            },
            noRowsOverlayComponent: NoRowsOverlay,
            onRowClicked,
        };
    }, [onRowClicked]);

    const handleSetDataSource = useCallback(
        (event) => {
            const dataSource = createServerSideDataSource();
            gridOptions.dataSource = dataSource;
            event.api.setDatasource(dataSource);
        },
        [gridOptions]
    );

    return (
        <div className="scrollableContainer">
            <div className="content" style={{ height: '100%', width: '100%' }}>
                <Box
                    sx={{
                        height: '100%',
                        width: '100%',
                        '& .ag-header': {
                            display: 'none',
                        },
                        ...tableStyles,
                    }}
                >
                    <AgGridReact
                        className="ag-theme-alpine"
                        ref={gridRef}
                        {...gridOptions}
                        onGridReady={handleSetDataSource}
                    />
                </Box>
            </div>
        </div>
    );
};

export default ImportProgress;
