/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback } from 'react';
import { styled, IconButton, Grid } from '@mui/material';
import { DragIcon, TrashIcon } from '@tripudiotech/styleguide';

import type { UniqueIdentifier } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { motion } from 'framer-motion';
import classNames from 'classnames';
import Title from '../Base/Title';
import { useStore } from '../../store';

const initialStyles = {
    x: 0,
    y: 0,
    scale: 1,
};

const Wrapper = styled('div')(({ theme }) => ({
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    paddingBottom: '6px',
    background: '#FFFFFF',
    boxShadow: `0px 4px 20px rgba(0, 0, 0, 0.12)`,
    transition: 'box-shadow 0.4s linear',
    borderTopLeftRadius: '4px',
    borderTopRightRadius: '4px',
    '&.dragging': {
        boxShadow: `0px 0px 0px 2px ${theme.palette.info.main}`,
        '& .title': {
            cursor: 'grabbing',
        },
    },
    '& .itemHeader': {
        display: 'flex',
        justifyContent: 'space-between',
        backgroundColor: theme.palette.glide.background.normal.quarternary,
        padding: '4.5px 0',
        borderBottom: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
        borderTopLeftRadius: '4px',
        borderTopRightRadius: '4px',
        height: '42px',
        '& .deleteBtn': {
            opacity: 0,
            transition: 'all 0.3s ease-in-out',
        },
        '&:hover': {
            '& .deleteBtn': {
                opacity: 1,
            },
        },
    },
    '& .dragContainer': {
        width: '100%',
        display: 'flex',
        alignItems: 'center',
    },
    '& .title': {
        cursor: 'grab',
    },
    '& .dragIcon': {
        marginRight: '8px',
        width: '32px',
        height: '32px',
        marginLeft: '12px',
    },
    '& .expandBtn': {
        backgroundColor: theme.palette.glide.background.normal.quarternary,
        color: theme.palette.glide.text.normal.tertiary,
        marginRight: '4px',
    },
    '& .scrollableContainer': {
        margin: 0,
        overflow: 'auto',
        height: '390px',
    },
}));

const WidgetContainer = ({
    id,
    isActive,
    title,
    children,
    fullWidth = false,
}: {
    id: string;
    isActive: boolean;
    title: string;
    children: any;
    fullWidth: boolean;
}) => {
    const deleteWidget = useStore((state) => state.deleteWidget);
    const { attributes, setNodeRef, listeners, transform, isDragging } = useSortable({
        id,
        transition: null,
    });
    const gridOptions = fullWidth ? { xs: 12 } : { xs: 12, sm: 12, md: 6, lg: 6, xl: 4 };
    const onDelete = useCallback(() => {
        deleteWidget(id);
    }, [deleteWidget, id]);
    return (
        <Grid
            item
            {...gridOptions}
            className={classNames({ draggingContainer: isDragging })}
            component={motion.div}
            animate={{ zIndex: isDragging ? 1 : 0 }}
            transition={{
                zIndex: {
                    delay: isDragging ? 0 : isActive ? 0.45 : 0.5,
                },
            }}
        >
            <motion.div
                ref={setNodeRef}
                layoutId={String(id)}
                animate={
                    transform
                        ? {
                              x: transform.x,
                              y: transform.y,
                              scale: isDragging ? 1.05 : 1,
                              zIndex: isDragging ? 1 : 0,
                              boxShadow: isDragging
                                  ? '0 0 0 1px rgba(63, 63, 68, 0.05), 0px 15px 15px 0 rgba(34, 33, 81, 0.25)'
                                  : undefined,
                              opacity: isDragging ? 1 : 0.8,
                          }
                        : initialStyles
                }
                transition={{
                    duration: isDragging ? 0 : isActive ? 0.45 : 0.5,
                    easings: {
                        type: 'spring',
                    },
                    scale: {
                        duration: 0.25,
                    },
                    opacity: {
                        duration: 0.25,
                    },
                }}
            >
                <Wrapper className={classNames({ dragging: isDragging })}>
                    <div className="itemHeader">
                        <Title {...attributes} {...listeners} className="title">
                            <DragIcon className="dragIcon" />
                            {title}
                        </Title>
                        <IconButton onClick={onDelete} className="deleteBtn" sx={{ color: '#FFFFFF' }} size="small">
                            <TrashIcon />
                        </IconButton>
                    </div>
                    {children}
                </Wrapper>
            </motion.div>
        </Grid>
    );
};

export default WidgetContainer;
