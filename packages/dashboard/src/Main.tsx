/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Box, Button, Checkbox, Divider, Grid, IconButton, Menu, MenuItem, Typography } from '@mui/material';
import { AnimatedPage, SettingIcon } from '@tripudiotech/styleguide';
import { useSchemaTree } from '@tripudiotech/caching-store';
import {
    type DragStartEvent,
    type DragEndEvent,
    type MeasuringConfiguration,
    type UniqueIdentifier,
    closestCenter,
    DndContext,
    useSensor,
    useSensors,
    PointerSensor,
    KeyboardSensor,
    MeasuringStrategy,
    TouchSensor,
} from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, rectSortingStrategy } from '@dnd-kit/sortable';
import WidgetContainer from './components/Widgets/WidgetContainer';
import findIndex from 'lodash/findIndex';
import { useStore } from './store';
import MyTasks from './components/Widgets/MyTasks';
import MyEntities from './components/Widgets/MyEntities';
import RecentlyViewed from './components/Widgets/RecentlyViewed';
import { PREBUILT_WIDGET } from './constant';
import { WidgetItem } from './model';
import ImportProgress from './components/Widgets/ImportProgress';
import { Outlet } from 'react-router-dom';
import sortBy from 'lodash/sortBy';

const measuring: MeasuringConfiguration = {
    droppable: {
        strategy: MeasuringStrategy.Always,
    },
};

const renderWidget = (widgetId: string, title: string, entityType?: string) => {
    switch (widgetId) {
        case 'my-tasks':
            return <MyTasks />;
        case 'recently-viewed':
            return <RecentlyViewed />;
        case 'import-progress':
            return <ImportProgress />;
        case 'my-entities':
        default:
            return <MyEntities entityTypes={[entityType]} title={title} />;
    }
};

const convertSchemaToOption = (schema: any) => ({
    id: `my-entities-${schema.name}`,
    data: {
        title: schema.displayName,
        entityType: schema.name,
    },
});

const Main = () => {
    const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [selectedWidgetMap, setSelectedWidgetMap] = useState<Record<string, WidgetItem>>({});
    const { schemaTreeMap, isLoaded } = useSchemaTree();
    const widgetOptions: WidgetItem[] = useMemo(
        () =>
            sortBy(
                schemaTreeMap
                    ? PREBUILT_WIDGET.concat(
                          Object.values(schemaTreeMap)
                              .filter((schema: any) => schema.visible)
                              .map(convertSchemaToOption)
                      ).sort()
                    : [],
                [(option) => option.data.title]
            ),
        [schemaTreeMap]
    );

    const open = Boolean(anchorEl);

    const { widgets, setWidgets } = useStore();

    const syncWidgetsToState = useCallback(() => {
        let newState = {};
        widgets.forEach((widget) => {
            newState[widget.id] = widget;
        });
        setSelectedWidgetMap(newState);
    }, [widgets]);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleSave = useCallback(() => {
        let newWidgets = [...widgets].filter((widget) => Boolean(selectedWidgetMap[widget.id]));
        Object.entries(selectedWidgetMap).forEach(([id, value]) => {
            if (!newWidgets.some((widget) => widget.id === id) && Boolean(value)) {
                newWidgets.push({ ...value }); //
            }
        });
        setWidgets(newWidgets);
        setAnchorEl(null);
    }, [selectedWidgetMap, widgets, schemaTreeMap]);

    const handleClose = () => {
        setAnchorEl(null);
        syncWidgetsToState();
    };

    useEffect(() => {
        syncWidgetsToState();
    }, [syncWidgetsToState]);

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(TouchSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragStart = ({ active }: DragStartEvent) => {
        setActiveId(active.id);
    };
    const handleDragCancel = () => {
        setActiveId(null);
    };
    const handleDragEnd = ({ active, over }: DragEndEvent) => {
        if (!over) {
            return;
        }
        const activeIndex = findIndex(widgets, (item) => item.id === active.id);
        const overIndex = findIndex(widgets, (item) => item.id === over.id);
        setWidgets(arrayMove(widgets, activeIndex, overIndex));
    };

    const onChangeItem = useCallback(
        (id, checked, widget) => {
            setSelectedWidgetMap((state) => ({
                ...state,
                [id]: checked ? widget : null,
            }));
        },
        [setSelectedWidgetMap]
    );

    return (
        <AnimatedPage style={{ background: '#F7F8FC' }}>
            <Box sx={{ mt: '52px', pt: '24px', mb: '32px', display: 'flex', flexDirection: 'column', gap: '24px' }}>
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        justifyContent: 'space-between',
                        mx: '24px',
                    }}
                >
                    <Typography
                        variant="h1"
                        sx={{
                            fontSize: '1.25em',
                            fontWeight: 600,
                        }}
                    >
                        Dashboard
                    </Typography>
                    <Box>
                        <IconButton size="small" color="primary" onClick={handleClick} disabled={!isLoaded}>
                            <SettingIcon />
                        </IconButton>
                        <Menu
                            id="dashboard-menu"
                            anchorEl={anchorEl}
                            open={open}
                            onClose={handleClose}
                            MenuListProps={{
                                'aria-labelledby': 'basic-button',
                            }}
                        >
                            <Box sx={{ maxHeight: '70vh', overflow: 'auto' }}>
                                {widgetOptions.map((widget) => (
                                    <MenuItem
                                        key={widget.id}
                                        sx={{
                                            display: 'flex',
                                            gap: '8px',
                                            '& .MuiCheckbox-root': {
                                                p: 0,
                                                m: 0,
                                            },
                                        }}
                                        onClick={() =>
                                            onChangeItem(widget.id, !Boolean(selectedWidgetMap[widget.id]), widget)
                                        }
                                    >
                                        <Checkbox name={widget.id} checked={Boolean(selectedWidgetMap[widget.id])} />
                                        <Typography
                                            sx={{
                                                fontSize: '0.875em',
                                                color: (theme) => theme.palette.glide.text.normal.inverseSecondary,
                                            }}
                                        >
                                            {widget.data.title}
                                        </Typography>
                                    </MenuItem>
                                ))}
                            </Box>
                            <Box
                                sx={{
                                    display: 'flex',
                                    justifyContent: 'flex-end',
                                    padding: '8px',
                                    gap: '8px',
                                    borderTop: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                                }}
                            >
                                <Button size="small" variant="contained" color="secondary" onClick={handleClose}>
                                    Cancel
                                </Button>
                                <Button onClick={handleSave} size="small" variant="contained" color="primary">
                                    Save
                                </Button>
                            </Box>
                        </Menu>
                    </Box>
                </Box>
                <Divider />
                <Box sx={{ mx: '24px' }}>
                    <DndContext
                        onDragStart={handleDragStart}
                        onDragEnd={handleDragEnd}
                        onDragCancel={handleDragCancel}
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        measuring={measuring}
                    >
                        <SortableContext strategy={rectSortingStrategy} items={widgets}>
                            <Grid container spacing={'24px'}>
                                {widgets.map(({ data: { title, fullWidth, entityType }, id }) => (
                                    <WidgetContainer
                                        isActive={activeId === id}
                                        title={title}
                                        key={id}
                                        fullWidth={fullWidth}
                                        id={id}
                                    >
                                        {renderWidget(id, title, entityType)}
                                    </WidgetContainer>
                                ))}
                            </Grid>
                        </SortableContext>
                    </DndContext>
                </Box>
                <Outlet />
            </Box>
        </AnimatedPage>
    );
};

export default Main;
