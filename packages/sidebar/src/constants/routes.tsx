/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { BrowseIcon, DashboardIcon, FileIcon } from '../assets/icon/Icon';
import { ComparisonIcon, ProjectIcon, ReportIcon, UploadIcon } from '@tripudiotech/styleguide';

export const DEFAULT_ROUTES = [
    {
        id: 'dashboard',
        label: 'Dashboard',
        link: '/dashboard',
        icon: <DashboardIcon />,
    },
    {
        id: 'browse-catalog',
        icon: <BrowseIcon />,
        label: 'Browse Catalog',
        link: '/catalog',
    },
    {
        id: 'file-management',
        icon: <FileIcon />,
        label: 'File Management',
        link: '/file-management',
    },
    {
        id: 'compare',
        icon: <ComparisonIcon />,
        label: 'Compare',
        link: '/analytics',
    },
    {
        id: 'importing',
        icon: <UploadIcon sx={{ height: '24px !important', width: '24px !important' }} />,
        label: 'Importing',
        link: '/importing',
    },
    {
        id: 'project',
        icon: <ProjectIcon />,
        label: 'Project',
        link: '/project',
    },
    {
        id: 'report',
        icon: <ReportIcon sx={{ height: '24px !important', width: '24px !important' }} />,
        label: 'Report',
        link: '/report',
    },
];

export type RouteType = (typeof DEFAULT_ROUTES)[0];
