const { merge } = require('webpack-merge');
const singleSpaDefaults = require('webpack-config-single-spa-react-ts');
const { rspack } = require('@rspack/core');
const dotenv = require('dotenv');
dotenv.config();
module.exports = (webpackConfigEnv, argv) => {
    const defaultConfig = singleSpaDefaults({
        orgName: 'tripudiotech',
        projectName: 'caching-store',
        webpackConfigEnv,
        argv,
    });
    const sourceMap = process.env.SOURCE_MAP || false;
    return merge(defaultConfig, {
        plugins: [
            new rspack.DefinePlugin({
                'process.env': JSON.stringify(process.env),
            }),
        ],
        module: {
            rules: [],
        },
        devtool: sourceMap,
        devServer: {
            client: {
                overlay: {
                    errors: true,
                    warnings: false,
                    runtimeErrors: false,
                },
            },
        },
    });
};
