/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch, userUrls, DEFAULT_CLIENT_SIDE_LIMIT } from '@tripudiotech/api';
import keyBy from 'lodash/keyBy';

type OrgRole = {
    id: string;
    name: string;
};

export interface RolesStore {
    roles: OrgRole[];
    rolesMap: Record<string, any> | null;
    getRoles: (force?: Boolean) => Promise<any[]>;
}

const useRoles = create<RolesStore>((set, get) => ({
    roles: null,
    rolesMap: null,

    getRoles: async (force = false) => {
        if (get().roles && !force) return get().roles;
        try {
            const { data: roles }: { data: any[] } = await fetch({
                ...userUrls.getRoles,
                qs: {
                    limit: DEFAULT_CLIENT_SIDE_LIMIT,
                },
            });
            const rolesMap = keyBy(roles, 'name');

            set({ rolesMap: rolesMap, roles: roles });
            return roles;
        } catch (error) {
            console.error(error);
        }
    },
}));

export default useRoles;
