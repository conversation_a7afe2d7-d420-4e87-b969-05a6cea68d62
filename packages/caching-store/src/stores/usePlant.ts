/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { plantUrls, Plant, fetch, DEFAULT_CLIENT_SIDE_LIMIT } from '@tripudiotech/api';

export interface PlantStore {
    isLoaded: boolean;
    plants: Plant[];
    selectedPlant: Plant | null;
    fetchPlants: () => Promise<void>;
    changePlant: (plant: Plant) => void;
    getByName: (name: string) => Plant | undefined;
}

const usePlant = create<PlantStore>((set, get) => ({
    isLoaded: false,
    plants: [],
    selectedPlant: null,
    fetchPlants: async (force = false) => {
        try {
            if (get().isLoaded && !force) return;
            const response = await fetch({
                ...plantUrls.getPlants,
                qs: {
                    limit: DEFAULT_CLIENT_SIDE_LIMIT,
                },
            });

            const plants: Plant[] = response.data.data;
            set({ plants, isLoaded: true });
        } catch (error) {
            console.error('Failed to fetch plants:', error);
            set({ isLoaded: true });
        }
    },
    changePlant: (plant: Plant) => {
        set({ selectedPlant: plant });
        window.localStorage.setItem('plantName', plant.name);
    },
    getByName: (name: string) => {
        return get().plants.find((p) => p.name === name);
    },
}));

export default usePlant;
