/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch, DEFAULT_CLIENT_SIDE_LIMIT, entityUrls } from '@tripudiotech/api';

type AgentStore = {
    loaded: boolean;
    teams: Array<Record<string, any>>;
    companies: Array<Record<string, any>>;
    departments: Array<Record<string, any>>;
    people: Array<Record<string, any>>;
    userCompany: Record<string, any>;
    setData: (data: AgentStore | Partial<AgentStore>) => void;
    getAgents: (userId: string) => any;
    getUserCompany: (userId: string) => any;
};

const useAgent = create<AgentStore>((set, get) => ({
    loaded: false,
    teams: [],
    companies: [],
    departments: [],
    people: [],
    userCompany: null,
    setData: (data) => set(data),
    getAgents: async (userId: string) => {
        if (get().loaded) return;

        const [companies, departments, teams] = await Promise.all([
            fetch({
                ...entityUrls.getEntityRelations,
                params: {
                    fromEntityId: userId,
                    relationType: 'WORKS_FOR',
                    entityType: 'Company',
                },
                qs: { limit: DEFAULT_CLIENT_SIDE_LIMIT, offset: 0 },
            }),
            fetch({
                ...entityUrls.getEntityRelations,
                params: {
                    fromEntityId: userId,
                    relationType: 'WORKS_FOR',
                    entityType: 'Department',
                },
                qs: { limit: DEFAULT_CLIENT_SIDE_LIMIT, offset: 0 },
            }),
            fetch({
                ...entityUrls.getEntityRelations,
                params: {
                    fromEntityId: userId,
                    relationType: 'WORKS_FOR',
                    entityType: 'Team',
                },
                qs: { limit: DEFAULT_CLIENT_SIDE_LIMIT, offset: 0 },
            }),
        ]);
        set({
            companies: companies.data.data,
            teams: teams.data.data,
            departments: departments.data.data,
            loaded: true,
        });
    },
    getUserCompany: async (userId: string) => {
        if (get().userCompany) return get().userCompany;

        const { data } = await fetch({
            ...entityUrls.getEntityRelations,
            params: {
                fromEntityId: userId,
                relationType: 'WORKS_FOR',
                entityType: 'Company',
            },
            qs: { limit: 1, offset: 0 },
        });
        const userCompany = data.data[0];
        set({ userCompany });
        return userCompany;
    },
}));

export default useAgent;
