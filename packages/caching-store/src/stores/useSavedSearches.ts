/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import {
    fetch,
    entityUrls,
    type AxiosResponse,
    PageResponse,
    buildNotEqualOperatorQuery,
    SYSTEM_ENTITY_TYPE,
    SYSTEM_RELATION,
    BASE_LF_STATE,
    PERMISSION_TYPES,
    EntityDetail,
} from '@tripudiotech/api';
import orderBy from 'lodash/orderBy';
import { SchemaTreeMapEntry } from './useSchemaTree';

export interface SearchParams {
    orderBy: string;
    columnOrder: string;
    searchCriteria: string;
    entityTypes: string;
}
export interface SavedSearch extends SearchParams {
    name: string;
    title: string;
    description: string;
    id: string;
}

export interface SavedSearchDetail extends Omit<EntityDetail, 'properties'> {
    properties: SavedSearch;
    updatedAt: string;
}

export interface SavedSearchRequest extends SearchParams {
    name: string;
    description: string;
}

interface UpdateSavedSearchRequest {
    request: SavedSearchRequest;
    newViewers: string[];
    newEditors: string[];
    removedViewers: string[];
    removedEditors: string[];
    entityId: string;
    userId: string;
}

type EditType = 'share' | 'edit';

interface AgentOption {
    label: string;
    value: string;
    isNew?: boolean;
    data: { properties: Record<string, any> };
}

interface AgentOption {
    label: string;
    value: string;
    isNew?: boolean;
    data: { properties: Record<string, any> };
}

interface SavedSearchStore {
    mySavedSearches: Record<string, SavedSearchDetail>;
    isLoadingMySavedSearches: boolean;
    sharedSavedSearches: Record<string, SavedSearchDetail>;
    editor: {
        open: boolean;
        searchCriteria: string;
        onSubmit: (searchCriteria: string) => void;
        options?: SchemaTreeMapEntry[];
    };
    openQueryBuilder: (
        initialQuery: string,
        onSubmit: (searchCriteria: string) => void,
        options?: SchemaTreeMapEntry[]
    ) => void;
    closeQueryBuilder: () => void;
    isLoadingSharedSavedSearches: boolean;
    getMySavedSearches: (userId: string, force?: boolean) => Promise<SavedSearchDetail[]>;
    getSharedSavedSearches: (userId: string, force?: boolean) => Promise<SavedSearchDetail[]>;
    isLoadingSelectedSearch: boolean;
    selectedSavedSearch: SavedSearchDetail;
    getAndSetSelectedSearch: (queryId: string) => Promise<SavedSearchDetail>;
    createSavedFilter: (
        request: SavedSearchRequest,
        viewers: string[],
        editors: string[],
        userId: string
    ) => Promise<SavedSearchDetail>;
    updateExistingFilter: (request: UpdateSavedSearchRequest) => Promise<SavedSearchDetail>;
    updateSavedSearch: (request: SearchParams) => Promise<SavedSearchDetail>;
    resetSelectedSearch: () => void;
    deleteSavedSearch: (id: string, name: string) => Promise<void>;
    updateAccesses: (viewers: string[], editors: string[], entityId: string) => Promise<void>;
    revokeAccesses: (agentIds: string[], entityId: string) => Promise<void>;
    editType: EditType;
    setEditType: (editType: EditType, search: SavedSearchDetail) => void;
    selectedSearchViewers: AgentOption[];
    selectedSearchEditors: AgentOption[];
    setSelectedSearchViewers: (viewers: AgentOption[]) => void;
    setSelectedSearchEditors: (editors: AgentOption[]) => void;
    getAndSetSelectedSearchAgents: (queryId: string) => Promise<void>;
    resetEditType: () => void;
}

const useSavedSearches = create<SavedSearchStore>((set, get) => ({
    mySavedSearches: null,
    editType: null,
    selectedSearchViewers: [],
    selectedSearchEditors: [],
    selectedSavedSearch: null,
    sharedSavedSearches: null,
    isLoadingMySavedSearches: true,
    isLoadingSharedSavedSearches: true,
    isLoadingSelectedSearch: false,
    editor: {
        open: false,
        searchCriteria: '',
        onSubmit: () => {},
    },
    getMySavedSearches: async (userId: string, force: boolean = false): Promise<SavedSearchDetail[]> => {
        if (!get().isLoadingMySavedSearches && !force)
            return orderBy(Object.values(get().mySavedSearches), 'updatedAt', 'desc');
        set({ ...get(), isLoadingMySavedSearches: true });
        const response: AxiosResponse<PageResponse<SavedSearchDetail[]>> = await fetch({
            ...entityUrls.getEntityRelations,
            params: {
                relationType: SYSTEM_RELATION.OWNED_BY,
                fromEntityId: userId,
                entityType: SYSTEM_ENTITY_TYPE.SEARCH_QUERY,
            },
            qs: { reverse: true, limit: 500, fields: ['permissions'] },
        });
        const searchIdToSearch: Record<string, SavedSearchDetail> = {};
        response.data.data.forEach((search) => (searchIdToSearch[search.id] = search));
        set({ ...get(), isLoadingMySavedSearches: false, mySavedSearches: searchIdToSearch });
        return orderBy(response.data.data, 'updatedAt', 'desc');
    },
    getSharedSavedSearches: async (userId: string, force: boolean = false): Promise<SavedSearchDetail[]> => {
        if (!get().isLoadingSharedSavedSearches && !force)
            return orderBy(Object.values(get().sharedSavedSearches), 'updatedAt', 'desc');
        set({ ...get(), isLoadingSharedSavedSearches: true });
        const query = buildNotEqualOperatorQuery('relation.OWNED_BY.id', userId);
        const response: AxiosResponse<PageResponse<SavedSearchDetail[]>> = await fetch({
            ...entityUrls.getListEntity,
            params: { entityType: SYSTEM_ENTITY_TYPE.SEARCH_QUERY },
            qs: { query: JSON.stringify(query), limit: 500, fields: ['permissions'] },
        });
        const searchIdToSearch: Record<string, SavedSearchDetail> = {};
        response.data.data.forEach((search) => (searchIdToSearch[search.id] = search));
        set({ ...get(), isLoadingSharedSavedSearches: false, sharedSavedSearches: searchIdToSearch });
        return orderBy(response.data.data, 'updatedAt', 'desc');
    },
    getAndSetSelectedSearch: async (queryId: string) => {
        set({ ...get(), isLoadingSelectedSearch: true });
        const response = await fetch({
            ...entityUrls.getEntityById,
            params: { entityType: SYSTEM_ENTITY_TYPE.SEARCH_QUERY, entityId: queryId },
        });
        set({ ...get(), selectedSavedSearch: response.data, isLoadingSelectedSearch: false });
        return response.data;
    },
    updateAccesses: async (viewers: string[], editors: string[], entityId: string) => {
        let accessRequests = [];
        viewers.forEach((viewerId) =>
            accessRequests.push(
                fetch({
                    ...entityUrls.grantPermission,
                    params: {
                        entityId,
                        stateName: BASE_LF_STATE.ACTIVE,
                    },
                    data: {
                        agentId: viewerId,
                        role: PERMISSION_TYPES.VIEWER,
                    },
                })
            )
        );
        editors.forEach((editorId) =>
            accessRequests.push(
                fetch({
                    ...entityUrls.grantPermission,
                    params: {
                        entityId,
                        stateName: BASE_LF_STATE.ACTIVE,
                    },
                    data: {
                        agentId: editorId,
                        role: PERMISSION_TYPES.CONTRIBUTOR,
                    },
                    skipToast: true,
                })
            )
        );
        await Promise.all(accessRequests);
    },
    revokeAccesses: async (agentIds: string[], entityId) => {
        if (agentIds.length > 0) {
            await fetch({
                ...entityUrls.revokePermission,
                params: {
                    entityId,
                    stateName: BASE_LF_STATE.ACTIVE,
                },
                data: agentIds,
            });
        }
    },
    createSavedFilter: async (request: SavedSearchRequest, viewers: string[], editors: string[], userId: string) => {
        const response = await fetch({
            ...entityUrls.createEntity,
            params: {
                entityType: SYSTEM_ENTITY_TYPE.SEARCH_QUERY,
            },
            data: {
                attributes: {
                    ...request,
                },
            },
            successMessage: `Successfully created <b>${request?.name}</b>`,
        });

        await get().updateAccesses(viewers, editors, response.data.id);
        if (userId) {
            get().getMySavedSearches(userId, true);
            get().getSharedSavedSearches(userId, true);
        }
        return response.data;
    },
    updateExistingFilter: async ({
        request,
        entityId,
        newEditors,
        newViewers,
        removedEditors,
        removedViewers,
        userId,
    }) => {
        const response = await fetch({
            ...entityUrls.updateEntity,
            params: {
                entityType: SYSTEM_ENTITY_TYPE.SEARCH_QUERY,
                entityId,
            },
            data: {
                attributes: {
                    ...request,
                },
            },
            successMessage: `Successfully updated <b>${request?.name}</b>`,
        });

        await Promise.all([
            get().updateAccesses(newViewers, newEditors, entityId),
            get().revokeAccesses([...removedEditors, ...removedViewers], entityId),
        ]);
        if (userId) {
            get().getMySavedSearches(userId, true);
            get().getSharedSavedSearches(userId, true);
        }
        set({ ...get(), selectedSavedSearch: response.data });
        return response.data;
    },
    updateSavedSearch: async (request: SavedSearchRequest) => {
        const response = await fetch({
            ...entityUrls.updateEntity,
            params: {
                entityId: get().selectedSavedSearch?.id,
            },
            data: {
                attributes: {
                    ...request,
                },
            },
            successMessage: `Successfully updated ${get().selectedSavedSearch?.properties?.name}`,
        });
        return response.data;
    },
    deleteSavedSearch: async (id: string, name: string) => {
        if (id) {
            await fetch({
                ...entityUrls.deleteEntity,
                params: {
                    entityId: id,
                },
                successMessage: `Successfully deleted <b>${name}</b>`,
            });
        }
    },
    resetSelectedSearch: () => {
        set({ ...get(), selectedSavedSearch: null });
    },
    setEditType: (editType: EditType, search: SavedSearchDetail) => {
        set({ ...get(), editType: editType, selectedSavedSearch: search });
    },
    getAndSetSelectedSearchAgents: async (queryId: string) => {
        const res = await fetch({
            ...entityUrls.getEntityAccesses,
            params: { entityId: queryId },
            shouldShow404: true,
        });
        const viewers = res.data.data
            .filter((agent) => agent.contentRole === PERMISSION_TYPES.VIEWER)
            .map((agent) => ({
                label: agent.agent.name,
                value: agent.agent.id,
                data: { properties: agent.agent },
            }));
        const editors = res.data.data
            .filter((agent) => agent.contentRole === PERMISSION_TYPES.CONTRIBUTOR)
            .map((agent) => ({
                label: agent.agent.name,
                value: agent.agent.id,
                data: { properties: agent.agent },
            }));
        set({ ...get(), selectedSearchViewers: viewers, selectedSearchEditors: editors });
    },
    setSelectedSearchViewers: (viewers: AgentOption[]) => {
        set({ ...get(), selectedSearchViewers: viewers });
    },
    setSelectedSearchEditors: (editors: AgentOption[]) => {
        set({ ...get(), selectedSearchEditors: editors });
    },
    resetEditType: () => {
        set({ ...get(), editType: null });
    },
    openQueryBuilder: (
        initialQuery: string,
        onSubmit: (searchCriteria: string) => void,
        options?: SchemaTreeMapEntry[]
    ) => {
        set({ editor: { open: true, searchCriteria: initialQuery, onSubmit, options } });
    },
    closeQueryBuilder: () => {
        set({ editor: { open: false, searchCriteria: '', options: [], onSubmit: () => {} } });
    },
}));

export default useSavedSearches;
