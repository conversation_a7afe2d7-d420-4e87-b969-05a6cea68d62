/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { fetch, savedFilterUrls, buildInQuery, type AxiosResponse } from '@tripudiotech/api';

// TODO: check structure
// type SavedFilter = {
//     id: string;
//     name: string;
//     payload: string;
//     type: string;
// };

type SavedFilterStore = {
    filtersByType: Record<string, Array<Record<string, any>>>;
    getFiltersByType: (type: string) => void;
    createNewFilter: (payload: Record<string, any>) => Promise<void>;
    deleteFilter: (type: string, id: string) => Promise<void>;
    getFilterById: (id: string) => Promise<AxiosResponse<any, any>>;
};

const useSavedFilter = create<SavedFilterStore>()((set, get) => ({
    filtersByType: {},
    getFiltersByType: async (type: string) => {
        try {
            const { data } = await fetch({
                ...savedFilterUrls.getSavedFilters,
                qs: {
                    query: JSON.stringify(buildInQuery('type', [type])),
                    limit: 100,
                },
            });
            set({
                filtersByType: {
                    ...get().filtersByType,
                    [type]: data.data,
                },
            });
        } catch (e) {
            console.error(e);
        }
    },
    createNewFilter: async (payload) => {
        try {
            const { data } = await fetch({
                ...savedFilterUrls.createSavedFilter,
                data: payload,
            });
            get().getFiltersByType(data.type);
        } catch (e) {
            console.error(e);
        } finally {
        }
    },
    deleteFilter: async (type, id) => {
        try {
            await fetch({
                ...savedFilterUrls.deleteSavedFilter,
                params: {
                    id,
                },
            });
            get().getFiltersByType(type);
        } catch (e) {
            console.error(e);
        } finally {
        }
    },
    getFilterById: async (id) => {
        try {
            const data = await fetch({
                ...savedFilterUrls.getSavedFilter,
                params: {
                    id,
                },
            });
            return data;
        } catch (e) {
            return null;
        } finally {
        }
    },
}));

export default useSavedFilter;
