/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { schemaUrls, fetch, SchemaTree, Attribute, AxiosResponse, RelationType } from '@tripudiotech/api';
import pick from 'lodash/pick';
import isNil from 'lodash/isNil';
import orderBy from 'lodash/orderBy';
import { useMemo } from 'react';

export interface SchemaTreeMapEntry extends Partial<SchemaTree> {
    parent: string;
    visibleChildren: string[];
    path: string[];
}

type SchemaStore = {
    schemaTreeMap: Record<string, SchemaTreeMapEntry> | null;
    relationTreeMap: Record<string, Partial<RelationType>> | null;
    isLoaded: boolean;
    defaultAttributes: Attribute[];
    setSchemaTree: (schemaTree: Record<string, any>) => void;
    setRelationTree: (relationTree: Record<string, any>) => void;
    getSchemaTree: (onlyRelations?: boolean) => Promise<Record<string, SchemaTreeMapEntry>>;
};

/**
 * Schema tree is parsed and stored as HashMap for performance
 */
const useSchemaTree = create<SchemaStore>((set, get) => ({
    schemaTreeMap: null,
    relationTreeMap: null,
    isLoaded: false,
    defaultAttributes: [],
    setSchemaTree: (schemaTreeMap) => set({ ...get(), schemaTreeMap }),
    setRelationTree: (relationTreeMap) => set({ relationTreeMap }),
    getSchemaTree: async (): Promise<Record<string, SchemaTreeMapEntry>> => {
        if (get().isLoaded) get().schemaTreeMap;
        const rootSchema: AxiosResponse<SchemaTree> = await fetch({
            ...schemaUrls.getSchemaTree,
        });
        let flattenSchemaMap: Record<string, SchemaTreeMapEntry> = {};
        let relationMap: Record<string, Partial<RelationType>> = {};
        flattenTree([rootSchema.data], null, [], flattenSchemaMap, relationMap);
        set({
            schemaTreeMap: flattenSchemaMap,
            relationTreeMap: relationMap,
            isLoaded: true,
            defaultAttributes: rootSchema.data?.attributes?.filter((attr) => attr.visible) || [],
        });
        return flattenSchemaMap;
    },
}));

export const useVisibleEntityTypes = (): [boolean, SchemaTreeMapEntry[]] => {
    const { schemaTreeMap, isLoaded } = useSchemaTree();
    const entityTypes = useMemo(() => {
        if (isNil(schemaTreeMap)) {
            return [];
        }
        const types = Object.values(schemaTreeMap);

        return orderBy(
            types.filter((type) => type.visible),
            'name'
        );
    }, [schemaTreeMap]);
    return [isLoaded, entityTypes];
};

const flattenTree = (
    nodes: SchemaTree[],
    parent: SchemaTree,
    path: string[],
    map: Record<string, SchemaTreeMapEntry>,
    relationMap: Record<string, Partial<RelationType>>
) => {
    nodes.map((node) => {
        let newPath = [...path, node.name];
        map[node.name] = {
            parent: parent?.name,
            visibleChildren: [],
            path: newPath,
            ...pick(node, [
                'abstract',
                'classifications',
                'description',
                'disabled',
                'displayName',
                'extendable',
                'id',
                'masterModel',
                'name',
                'system',
                'visible',
                'relations',
                'accessibleOrgRoles',
                'attributes',
            ]),
        };
        if (parent?.name && node.visible && !node.abstract) {
            let ancestor = parent?.name;
            while (ancestor && map[ancestor]) {
                map[ancestor]?.visibleChildren?.push(node.name);
                ancestor = map[ancestor].parent;
            }
        }
        node.relations?.forEach((relation) => {
            if (!relationMap[relation.name] && relation.visible) {
                relationMap[relation.name] = {
                    name: relation.name,
                    displayName: relation.displayName,
                    fromEntityType: relation.fromEntityType,
                    toEntityType: relation.toEntityType,
                };
            }
        });

        if (node.subTypes) {
            flattenTree(node.subTypes, node, newPath, map, relationMap);
        }
    });
};

export default useSchemaTree;
