/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import { assetServiceUrl, fetch, batchWithProgress, entityUrls, batchRequestBody, Method } from '@tripudiotech/api';
import { v4 as uuidv4 } from 'uuid';
import { subscribeWithSelector } from 'zustand/middleware';
import get from 'lodash/get';

type Item = {
    id: string;
    name: string;
    file?: File;
    folderId?: string;
    error?: boolean;
    errorType?: number | null;
    progress?: number | null;
    abortController?: any;
    extension?: string;
    callback?: () => void;
};

interface UploadManagerStore {
    items: string[];
    expanded: boolean;
    uploadFiles: (folderId: string, files: File[], callback?: () => void) => Promise<void>;
    uploadFile: (folderId: string, files: File, callback?: () => void) => Promise<void>;
    itemProgress: Record<string, Item>;
    replace: (requestId: string) => void;
    toggleExpanded: () => void;
    cancelAll: (force?: boolean) => boolean;
    cancel: (requestId: string) => Promise<void>;
    createEntitiesBatch: (
        entityId: string,
        relationType: string,
        entityType: string,
        bodies: any,
        callback?: () => void
    ) => Promise<void>;
}

const initialState = {
    items: [],
    itemProgress: {},
    expanded: true,
};

const useUploadManager = create<UploadManagerStore>()(
    subscribeWithSelector((set, get) => ({
        ...initialState,
        uploadFiles: async (folderId: string, files: File[], callback?) => {
            files.forEach((file) => {
                get().uploadFile(folderId, file, callback);
            });
        },
        createEntitiesBatch: async (
            entityId: string,
            relationType: string,
            entityType: string,
            bodies: any,
            callback?
        ) => {
            const abortController = new AbortController();
            const requestId: string = uuidv4();
            set({
                items: [...get().items, requestId],
                itemProgress: {
                    ...get().itemProgress,
                    [requestId]: {
                        id: requestId,
                        name: 'Uploading your data',
                        error: false,
                        errorType: null,
                        progress: 0,
                        abortController,
                        callback,
                        extension: 'xlsx',
                    },
                },
            });
            await createEntities(entityId, relationType, entityType, bodies, abortController, callback);
            set({
                itemProgress: {
                    ...get().itemProgress,
                    [requestId]: {
                        ...get().itemProgress[requestId],
                        progress: 100,
                        name: 'Finished uploading',
                    },
                },
            });
        },
        uploadFile: async (folderId: string, file: File, callback?) => {
            const abortController = new AbortController();
            const requestId: string = uuidv4();
            set({
                items: [...get().items, requestId],
                itemProgress: {
                    ...get().itemProgress,
                    [requestId]: {
                        id: requestId,
                        name: file.name,
                        error: false,
                        errorType: null,
                        progress: 0,
                        abortController,
                        extension: file.name.split('.').pop(),
                        file: file,
                        callback,
                        folderId,
                    },
                },
            });

            const formData = new FormData();
            formData.append('file', file, file.name);

            await upload(folderId, formData, abortController, set, get, requestId, callback);
        },
        replace: async (requestId: string) => {
            const abortController = new AbortController();

            set({
                itemProgress: {
                    ...get().itemProgress,
                    [requestId]: {
                        ...get().itemProgress[requestId],
                        error: false,
                        errorType: null,
                        progress: 0,
                        abortController,
                    },
                },
            });
            const { callback, file, folderId } = get().itemProgress[requestId];

            const formData = new FormData();
            formData.append('file', file, file.name);
            formData.append('override', 'true');
            await upload(folderId, formData, abortController, set, get, requestId, callback);
        },
        cancel: async (requestId) => {
            const { abortController } = get().itemProgress[requestId];
            abortController.abort();
            set({
                items: [...get().items].filter((item) => item !== requestId),
                itemProgress: {
                    ...get().itemProgress,
                    [requestId]: undefined,
                },
            });
        },
        toggleExpanded: () => {
            set({ expanded: !get().expanded });
        },
        cancelAll: (force = false) => {
            const { items, itemProgress, toggleExpanded } = get();
            if (!force && items.some((item) => itemProgress[item].progress !== 100)) {
                return false;
            }
            items.forEach((item) => {
                const itemDetails = itemProgress[item];
                if (itemProgress[item].progress !== 100) {
                    itemDetails.abortController.abort();
                }
            });
            toggleExpanded();
            set({
                ...initialState,
            });
            return true;
        },
    }))
);

const promptHandler = (event: BeforeUnloadEvent) => {
    event.preventDefault();
    event.returnValue = '';
};

// Prevent closing the page when there are files still uploading
useUploadManager.subscribe(
    (state) => [state.items, state.itemProgress],
    ([items, itemProgress]) => {
        //@ts-ignore
        if (items.some((item) => itemProgress[item].progress !== 100)) {
            window.addEventListener('beforeunload', promptHandler);
        } else {
            window.removeEventListener('beforeunload', promptHandler);
        }
    }
);

export default useUploadManager;

async function createEntities(
    entityId,
    relationType,
    entityType: string,
    bodies: any,
    abortController: AbortController,
    callback?
) {
    const requests = bodies.map((body) => ({
        body,
        subParams: {
            entityType,
        },
    }));
    const batchReq = batchRequestBody(Method.POST, '/entity/:entityType', requests);

    const { data } = await batchWithProgress({
        ...entityUrls.batchRequest,
        data: batchReq,
        signal: abortController.signal,
    });
    const results = get(data, [0, 'data']);
    const relationRequests = results
        .filter((result) => result.success)
        .map((result) => ({
            method: 'POST',
            url: `/entity/${entityId}/${relationType}/${get(result, ['response', 'id'])}`,
            body: {},
        }));

    if (relationRequests.length > 0) {
        await batchWithProgress({
            ...entityUrls.batchRequest,
            data: relationRequests,
            signal: abortController.signal,
        });
    }

    if (callback) {
        callback();
    }
}

async function upload(
    folderId: string,
    formData: FormData,
    abortController: AbortController,
    set: (
        partial:
            | UploadManagerStore
            | Partial<UploadManagerStore>
            | ((state: UploadManagerStore) => UploadManagerStore | Partial<UploadManagerStore>),
        replace?: boolean
    ) => void,
    get: () => UploadManagerStore,
    requestId: string,
    callback: () => void
) {
    try {
        await fetch({
            ...assetServiceUrl.uploadFile,
            params: { folderId },
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            signal: abortController.signal,
            skipToast: true,
            onUploadProgress: (progressEvent) => {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                set({
                    itemProgress: {
                        ...get().itemProgress,
                        [requestId]: {
                            ...get().itemProgress[requestId],
                            progress: percentCompleted,
                        },
                    },
                });
            },
        });
        if (callback) {
            callback();
        }
    } catch (error) {
        set({
            itemProgress: {
                ...get().itemProgress,
                [requestId]: {
                    ...get().itemProgress[requestId],
                    error: true,
                    errorType: error.response.status,
                },
            },
        });
    }
}
