import { Plant } from '@tripudiotech/api';
export interface PlantStore {
    isLoaded: boolean;
    plants: Plant[];
    selectedPlant: Plant | null;
    fetchPlants: () => Promise<void>;
    changePlant: (plant: Plant) => void;
    getByName: (name: string) => Plant | undefined;
}
declare const usePlant: import('zustand').UseBoundStore<import('zustand').StoreApi<PlantStore>>;
export default usePlant;
//# sourceMappingURL=usePlant.d.ts.map
