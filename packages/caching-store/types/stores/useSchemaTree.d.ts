import { SchemaTree, Attribute, RelationType } from '@tripudiotech/api';
export interface SchemaTreeMapEntry extends Partial<SchemaTree> {
    parent: string;
    visibleChildren: string[];
    path: string[];
}
type SchemaStore = {
    schemaTreeMap: Record<string, SchemaTreeMapEntry> | null;
    relationTreeMap: Record<string, Partial<RelationType>> | null;
    isLoaded: boolean;
    defaultAttributes: Attribute[];
    setSchemaTree: (schemaTree: Record<string, any>) => void;
    setRelationTree: (relationTree: Record<string, any>) => void;
    getSchemaTree: (onlyRelations?: boolean) => Promise<Record<string, SchemaTreeMapEntry>>;
};
/**
 * Schema tree is parsed and stored as HashMap for performance
 */
declare const useSchemaTree: import('zustand').UseBoundStore<import('zustand').StoreApi<SchemaStore>>;
export declare const useVisibleEntityTypes: () => [boolean, SchemaTreeMapEntry[]];
export default useSchemaTree;
//# sourceMappingURL=useSchemaTree.d.ts.map
