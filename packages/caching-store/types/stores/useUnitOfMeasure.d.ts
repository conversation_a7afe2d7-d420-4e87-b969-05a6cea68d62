import { Schema } from '@tripudiotech/api';
interface UnitOfMeasureState {
    quantityKind?: Record<string, any>;
    quantityUnit?: Record<string, any>;
    expiredAt?: number;
    updateQuantityUnit?: (key: string, value: string) => void;
    setQuantityKind?: (quantityKind: any[]) => void;
    getQuantityKind?: () => Promise<void>;
    getQuantityUnit?: (groupId: any) => Promise<void>;
    getUnitOfMeasureFromSchema?: (detailSchema: Schema) => void;
}
declare const useUnitOfMeasure: import("zustand").UseBoundStore<Omit<import("zustand").StoreApi<UnitOfMeasureState>, "persist"> & {
    persist: {
        setOptions: (options: Partial<import("zustand/middleware").PersistOptions<UnitOfMeasureState, UnitOfMeasureState>>) => void;
        clearStorage: () => void;
        rehydrate: () => Promise<void> | void;
        hasHydrated: () => boolean;
        onHydrate: (fn: (state: UnitOfMeasureState) => void) => () => void;
        onFinishHydration: (fn: (state: UnitOfMeasureState) => void) => () => void;
        getOptions: () => Partial<import("zustand/middleware").PersistOptions<UnitOfMeasureState, UnitOfMeasureState>>;
    };
}>;
export default useUnitOfMeasure;
//# sourceMappingURL=useUnitOfMeasure.d.ts.map