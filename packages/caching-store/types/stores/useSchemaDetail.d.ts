import { Lifecycle, Schema } from '@tripudiotech/api';
export interface LifecycleWithState {
    lifeCycle: Lifecycle;
    states: Record<string, any>;
}
export interface SchemaWithLifeCycleDetail extends Schema {
    lifecycles: LifecycleWithState[];
}
type SchemaDetailStore = {
    schema: Record<string, SchemaWithLifeCycleDetail> | null;
    isLoading: Record<string, boolean>;
    isLoaded: boolean;
    getSchema: (entityTypeName: any, withLifecycle?: boolean) => Promise<SchemaWithLifeCycleDetail>;
    getMultipleSchema: (entityTypeNames: any) => Promise<SchemaWithLifeCycleDetail[]>;
    getAttributeName: (schemaDetail: SchemaWithLifeCycleDetail, attributeId: string) => string;
};
declare const useSchemaDetail: import("zustand").UseBoundStore<import("zustand").StoreApi<SchemaDetailStore>>;
export default useSchemaDetail;
//# sourceMappingURL=useSchemaDetail.d.ts.map