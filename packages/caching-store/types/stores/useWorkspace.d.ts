import { Workspace } from '@tripudiotech/api';
export interface WorkspaceStore {
    isLoaded: boolean;
    isLoading: boolean;
    workspaces: Workspace[];
    selectedWorkspace: Workspace | null;
    fetchWorkspaces: () => Promise<void>;
    changeWorkspace: (workspace: Workspace) => Promise<void>;
    removeWorkspace: () => Promise<void>;
    getByName: (name: string) => Workspace | undefined;
}
export declare function remountAllMountedApps(): Promise<void>;
declare const useWorkspace: import("zustand").UseBoundStore<import("zustand").StoreApi<WorkspaceStore>>;
export default useWorkspace;
//# sourceMappingURL=useWorkspace.d.ts.map