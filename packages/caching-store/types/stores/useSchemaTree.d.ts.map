{"version": 3, "file": "useSchemaTree.d.ts", "sourceRoot": "", "sources": ["../../src/stores/useSchemaTree.ts"], "names": [], "mappings": "AAaA,OAAO,EAAqB,UAAU,EAAE,SAAS,EAAiB,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAM1G,MAAM,WAAW,kBAAmB,SAAQ,OAAO,CAAC,UAAU,CAAC;IAC3D,MAAM,EAAE,MAAM,CAAC;IACf,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,IAAI,EAAE,MAAM,EAAE,CAAC;CAClB;AAED,KAAK,WAAW,GAAG;IACf,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,kBAAkB,CAAC,GAAG,IAAI,CAAC;IACzD,eAAe,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC;IAC9D,QAAQ,EAAE,OAAO,CAAC;IAClB,iBAAiB,EAAE,SAAS,EAAE,CAAC;IAC/B,aAAa,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC;IACzD,eAAe,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC;IAC7D,aAAa,EAAE,CAAC,aAAa,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC,CAAC;CAC3F,CAAC;AAEF;;GAEG;AACH,QAAA,MAAM,aAAa,0EAuBhB,CAAC;AAEJ,eAAO,MAAM,qBAAqB,QAAO,CAAC,OAAO,EAAE,kBAAkB,EAAE,CActE,CAAC;AAwDF,eAAe,aAAa,CAAC"}