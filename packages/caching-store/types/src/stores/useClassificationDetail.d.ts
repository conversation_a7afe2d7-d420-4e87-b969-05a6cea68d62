import { ClassificationDetail, Classification } from '@tripudiotech/api';
type ClassificationDetailStore = {
    isLoading: Record<string, boolean>;
    classification: Record<string, ClassificationDetail>;
    schemaClassification: Record<string, Classification[]>;
    getClassification: (name: string) => Promise<ClassificationDetail>;
    getClassifications: (classifications: string[]) => Promise<ClassificationDetail[]>;
    getSchemaClassification: (entityType: string) => Promise<Classification[]>;
};
declare const useClassificationDetail: import('zustand').UseBoundStore<
    import('zustand').StoreApi<ClassificationDetailStore>
>;
export default useClassificationDetail;
//# sourceMappingURL=useClassificationDetail.d.ts.map
