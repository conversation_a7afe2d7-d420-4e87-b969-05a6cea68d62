type SideBar = {
    open: boolean;
    toggle: () => void;
    setOpen: (open: any) => void;
};
declare const useSideBar: import('zustand').UseBoundStore<
    Omit<import('zustand').StoreApi<SideBar>, 'persist'> & {
        persist: {
            setOptions: (options: Partial<import('zustand/middleware').PersistOptions<SideBar, SideBar>>) => void;
            clearStorage: () => void;
            rehydrate: () => Promise<void> | void;
            hasHydrated: () => boolean;
            onHydrate: (fn: (state: SideBar) => void) => () => void;
            onFinishHydration: (fn: (state: SideBar) => void) => () => void;
            getOptions: () => Partial<import('zustand/middleware').PersistOptions<SideBar, SideBar>>;
        };
    }
>;
export default useSideBar;
//# sourceMappingURL=useSideBar.d.ts.map
