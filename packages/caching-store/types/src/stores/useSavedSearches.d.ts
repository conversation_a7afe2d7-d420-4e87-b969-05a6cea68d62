import { EntityDetail } from '@tripudiotech/api';
import { SchemaTreeMapEntry } from './useSchemaTree';
export interface SearchParams {
    orderBy: string;
    columnOrder: string;
    searchCriteria: string;
    entityTypes: string;
}
export interface SavedSearch extends SearchParams {
    name: string;
    title: string;
    description: string;
    id: string;
}
export interface SavedSearchDetail extends Omit<EntityDetail, 'properties'> {
    properties: SavedSearch;
    updatedAt: string;
}
export interface SavedSearchRequest extends SearchParams {
    name: string;
    description: string;
}
interface UpdateSavedSearchRequest {
    request: SavedSearchRequest;
    newViewers: string[];
    newEditors: string[];
    removedViewers: string[];
    removedEditors: string[];
    entityId: string;
    userId: string;
}
type EditType = 'share' | 'edit';
interface AgentOption {
    label: string;
    value: string;
    isNew?: boolean;
    data: {
        properties: Record<string, any>;
    };
}
interface AgentOption {
    label: string;
    value: string;
    isNew?: boolean;
    data: {
        properties: Record<string, any>;
    };
}
interface SavedSearchStore {
    mySavedSearches: Record<string, SavedSearchDetail>;
    isLoadingMySavedSearches: boolean;
    sharedSavedSearches: Record<string, SavedSearchDetail>;
    editor: {
        open: boolean;
        searchCriteria: string;
        onSubmit: (searchCriteria: string) => void;
        options?: SchemaTreeMapEntry[];
    };
    openQueryBuilder: (
        initialQuery: string,
        onSubmit: (searchCriteria: string) => void,
        options?: SchemaTreeMapEntry[]
    ) => void;
    closeQueryBuilder: () => void;
    isLoadingSharedSavedSearches: boolean;
    getMySavedSearches: (userId: string, force?: boolean) => Promise<SavedSearchDetail[]>;
    getSharedSavedSearches: (userId: string, force?: boolean) => Promise<SavedSearchDetail[]>;
    isLoadingSelectedSearch: boolean;
    selectedSavedSearch: SavedSearchDetail;
    getAndSetSelectedSearch: (queryId: string) => Promise<SavedSearchDetail>;
    createSavedFilter: (
        request: SavedSearchRequest,
        viewers: string[],
        editors: string[],
        userId: string
    ) => Promise<SavedSearchDetail>;
    updateExistingFilter: (request: UpdateSavedSearchRequest) => Promise<SavedSearchDetail>;
    updateSavedSearch: (request: SearchParams) => Promise<SavedSearchDetail>;
    resetSelectedSearch: () => void;
    deleteSavedSearch: (id: string, name: string) => Promise<void>;
    updateAccesses: (viewers: string[], editors: string[], entityId: string) => Promise<void>;
    revokeAccesses: (agentIds: string[], entityId: string) => Promise<void>;
    editType: EditType;
    setEditType: (editType: EditType, search: SavedSearchDetail) => void;
    selectedSearchViewers: AgentOption[];
    selectedSearchEditors: AgentOption[];
    setSelectedSearchViewers: (viewers: AgentOption[]) => void;
    setSelectedSearchEditors: (editors: AgentOption[]) => void;
    getAndSetSelectedSearchAgents: (queryId: string) => Promise<void>;
    resetEditType: () => void;
}
declare const useSavedSearches: import('zustand').UseBoundStore<import('zustand').StoreApi<SavedSearchStore>>;
export default useSavedSearches;
//# sourceMappingURL=useSavedSearches.d.ts.map
