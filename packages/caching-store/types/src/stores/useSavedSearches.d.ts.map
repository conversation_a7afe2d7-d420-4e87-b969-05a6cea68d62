{"version": 3, "file": "useSavedSearches.d.ts", "sourceRoot": "", "sources": ["../../../src/stores/useSavedSearches.ts"], "names": [], "mappings": "AAaA,OAAO,EAUH,YAAY,EACf,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AAErD,MAAM,WAAW,YAAY;IACzB,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,EAAE,MAAM,CAAC;IACvB,WAAW,EAAE,MAAM,CAAC;CACvB;AACD,MAAM,WAAW,WAAY,SAAQ,YAAY;IAC7C,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;IACpB,EAAE,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,iBAAkB,SAAQ,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC;IACvE,UAAU,EAAE,WAAW,CAAC;IACxB,SAAS,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,kBAAmB,SAAQ,YAAY;IACpD,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;CACvB;AAED,UAAU,wBAAwB;IAC9B,OAAO,EAAE,kBAAkB,CAAC;IAC5B,UAAU,EAAE,MAAM,EAAE,CAAC;IACrB,UAAU,EAAE,MAAM,EAAE,CAAC;IACrB,cAAc,EAAE,MAAM,EAAE,CAAC;IACzB,cAAc,EAAE,MAAM,EAAE,CAAC;IACzB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;CAClB;AAED,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM,CAAC;AAEjC,UAAU,WAAW;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,IAAI,EAAE;QAAE,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;KAAE,CAAC;CAC7C;AAED,UAAU,WAAW;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,IAAI,EAAE;QAAE,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;KAAE,CAAC;CAC7C;AAED,UAAU,gBAAgB;IACtB,eAAe,EAAE,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;IACnD,wBAAwB,EAAE,OAAO,CAAC;IAClC,mBAAmB,EAAE,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;IACvD,MAAM,EAAE;QACJ,IAAI,EAAE,OAAO,CAAC;QACd,cAAc,EAAE,MAAM,CAAC;QACvB,QAAQ,EAAE,CAAC,cAAc,EAAE,MAAM,KAAK,IAAI,CAAC;QAC3C,OAAO,CAAC,EAAE,kBAAkB,EAAE,CAAC;KAClC,CAAC;IACF,gBAAgB,EAAE,CACd,YAAY,EAAE,MAAM,EACpB,QAAQ,EAAE,CAAC,cAAc,EAAE,MAAM,KAAK,IAAI,EAC1C,OAAO,CAAC,EAAE,kBAAkB,EAAE,KAC7B,IAAI,CAAC;IACV,iBAAiB,EAAE,MAAM,IAAI,CAAC;IAC9B,4BAA4B,EAAE,OAAO,CAAC;IACtC,kBAAkB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC;IACtF,sBAAsB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC;IAC1F,uBAAuB,EAAE,OAAO,CAAC;IACjC,mBAAmB,EAAE,iBAAiB,CAAC;IACvC,uBAAuB,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACzE,iBAAiB,EAAE,CACf,OAAO,EAAE,kBAAkB,EAC3B,OAAO,EAAE,MAAM,EAAE,EACjB,OAAO,EAAE,MAAM,EAAE,EACjB,MAAM,EAAE,MAAM,KACb,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAChC,oBAAoB,EAAE,CAAC,OAAO,EAAE,wBAAwB,KAAK,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACxF,iBAAiB,EAAE,CAAC,OAAO,EAAE,YAAY,KAAK,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACzE,mBAAmB,EAAE,MAAM,IAAI,CAAC;IAChC,iBAAiB,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/D,cAAc,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1F,cAAc,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IACxE,QAAQ,EAAE,QAAQ,CAAC;IACnB,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,iBAAiB,KAAK,IAAI,CAAC;IACrE,qBAAqB,EAAE,WAAW,EAAE,CAAC;IACrC,qBAAqB,EAAE,WAAW,EAAE,CAAC;IACrC,wBAAwB,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,IAAI,CAAC;IAC3D,wBAAwB,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,IAAI,CAAC;IAC3D,6BAA6B,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAClE,aAAa,EAAE,MAAM,IAAI,CAAC;CAC7B;AAED,QAAA,MAAM,gBAAgB,+EAwOnB,CAAC;AAEJ,eAAe,gBAAgB,CAAC"}