type Metadata = {
    id: string;
    name: string;
    dataType: string;
    revision?: string;
    email?: string;
};
type EntityMetadataStore = {
    entities: Record<string, any> | null;
    entitiesByProperty: Record<string, any> | null;
    entityAccess: Record<string, any> | null;
    expiredAt: number;
    getEntity: (id: any, dateType: any) => Promise<Metadata>;
    getEntityAccess: (id: string, force?: boolean) => any;
    getEntityByProperty: (entityType: string, property: string, value: string) => Promise<Metadata>;
};
declare const useEntityMetadata: import('zustand').UseBoundStore<
    Omit<import('zustand').StoreApi<EntityMetadataStore>, 'persist'> & {
        persist: {
            setOptions: (
                options: Partial<import('zustand/middleware').PersistOptions<EntityMetadataStore, EntityMetadataStore>>
            ) => void;
            clearStorage: () => void;
            rehydrate: () => Promise<void> | void;
            hasHydrated: () => boolean;
            onHydrate: (fn: (state: EntityMetadataStore) => void) => () => void;
            onFinishHydration: (fn: (state: EntityMetadataStore) => void) => () => void;
            getOptions: () => Partial<
                import('zustand/middleware').PersistOptions<EntityMetadataStore, EntityMetadataStore>
            >;
        };
    }
>;
export declare const IsExpired: (date: number) => boolean;
export declare const GetExpriedDate: (NoOfDays: any) => number;
export default useEntityMetadata;
//# sourceMappingURL=useEntityMetadata.d.ts.map
