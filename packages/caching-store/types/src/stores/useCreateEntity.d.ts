import { EntityDetail } from '@tripudiotech/api';
import { SchemaTreeMapEntry } from './useSchemaTree';
type CreateEntityStore = {
    open: boolean;
    disableNavigateOnCreate: boolean;
    postSubmitHandler?: (id: string, properties: EntityDetail['properties']) => void;
    setPostSubmitHandler: (postSubmitHandler: (id: string, properties: EntityDetail['properties']) => void) => void;
    selectedEntityType: SchemaTreeMapEntry | null;
    setSelectedEntityType: (entityType: SchemaTreeMapEntry) => void;
    setDisableNavigateOnCreate: (disableNavigateOnCreate: boolean) => void;
    toggle: () => void;
    setOpen: (open: boolean) => void;
    reset: () => void;
};
declare const useCreateEntity: import('zustand').UseBoundStore<import('zustand').StoreApi<CreateEntityStore>>;
export default useCreateEntity;
//# sourceMappingURL=useCreateEntity.d.ts.map
