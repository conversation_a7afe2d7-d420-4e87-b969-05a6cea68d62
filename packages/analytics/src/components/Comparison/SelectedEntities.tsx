/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box } from '@mui/material';
import { useMemo } from 'react';
import { tableStyles, EntityNameRenderer } from '@tripudiotech/styleguide';
import StatusCellRenderer from '../Renderer/StatusCellRenderer';
import { hasRevisionAttribute } from '../../util/helper';
import { AgGridReact } from '@ag-grid-community/react';
import { useSchemaTree } from '@tripudiotech/caching-store';

const SelectedEntities = ({ sourceEntity, targetEntity }) => {
    const rowData = useMemo(() => [sourceEntity, targetEntity], [sourceEntity, targetEntity]);
    const { schemaTreeMap } = useSchemaTree();

    const columnDefs = useMemo(() => {
        const hasRevision = hasRevisionAttribute(sourceEntity) || hasRevisionAttribute(targetEntity);

        const result = [
            {
                field: 'properties.name',
                headerName: 'Name',
                cellRenderer: EntityNameRenderer,
                flex: 1,
                filter: 'agTextColumnFilter',
                minWidth: 200,
            },
            {
                field: 'properties.type',
                headerName: 'Type',
                flex: 1,
                minWidth: 150,
                filter: 'agTextColumnFilter',
                valueGetter: (props) => {
                    if (props.context.schemaTreeMap) {
                        return (
                            props.context.schemaTreeMap?.[props.data?.properties?.type]?.displayName ||
                            props.data?.properties?.type
                        );
                    }
                    return props.data?.properties?.type;
                },
            },
            {
                field: 'properties.description',
                headerName: 'Description',
                flex: 1,
                minWidth: 150,
                filter: 'agTextColumnFilter',
            },
            {
                field: 'state.name',
                headerName: 'Status',
                cellRenderer: StatusCellRenderer,
                flex: 1,
                minWidth: 100,
            },
        ];

        // If the entitty is revision we need to display revision column
        return hasRevision
            ? [
                  ...result.slice(0, 2),
                  {
                      field: 'properties.revision',
                      headerName: 'Revision',
                      flex: 1,
                      cellStyle: { color: '#52C41A' },
                      minWidth: 130,
                  },
                  ...result.slice(2),
              ]
            : result;
    }, [sourceEntity, targetEntity]);

    const gridOptions = useMemo(() => {
        return {
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
            },
            headerHeight: 32,
            rowHeight: 32,
            getRowId: (params) => params.data.id,
            rowStyle: {
                background: '#F9F9F9',
            },
            columnDefs: columnDefs,
        };
    }, [columnDefs]);

    return (
        <Box sx={{ height: '100%', width: '100%', minHeight: '104px', ...tableStyles }}>
            <AgGridReact
                className="ag-theme-alpine"
                animateRows
                rowModelType="clientSide"
                rowData={rowData}
                context={{ schemaTreeMap: schemaTreeMap }}
                {...gridOptions}
            />
        </Box>
    );
};

export default SelectedEntities;
