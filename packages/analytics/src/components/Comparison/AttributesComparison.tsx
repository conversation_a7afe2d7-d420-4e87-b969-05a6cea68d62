/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box } from '@mui/material';
import React, { useMemo } from 'react';
import { DetailEntity, DetailSchema } from '../../model';
import { tableStyles, PropertyValueRenderer } from '@tripudiotech/styleguide';
import { AgGridReact } from '@ag-grid-community/react';
import { getColumnRevisionHeader, getRowClass } from '../../util/helper';
import get from 'lodash/get';

const AttributesComparison = ({
    sourceEntity,
    targetEntity,
    sourceSchema,
    targetSchema,
    rowData,
}: {
    sourceEntity: DetailEntity;
    targetEntity: DetailEntity;
    sourceSchema: DetailSchema;
    targetSchema: DetailSchema;
    rowData: any;
}) => {
    const gridOptions = useMemo(() => {
        return {
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                autoHeight: true,
                wrapText: true,
            },
            headerHeight: 32,
            rowHeight: 32,
            getRowId: (params) =>
                get(params, 'data.id') +
                get(params, 'data.groupLabel') +
                get(params, 'data.classificationGroup') +
                get(params, 'data.classification'),
            rowStyle: {
                background: '#F9F9F9',
            },
            getRowClass: (params) => {
                return getRowClass(params, sourceEntity, targetEntity);
            },
            isGroupOpenByDefault: () => true,
            columnDefs: [
                {
                    field: 'groupLabel',
                    headerName: 'Attribute Group',
                    rowGroup: true,
                    hide: true,
                },
                {
                    field: 'classificationGroup',
                    headerName: 'Classification Group',
                    rowGroup: true,
                    hide: true,
                },
                {
                    field: 'classification',
                    headerName: 'Classification',
                    rowGroup: true,
                    hide: true,
                },
                {
                    field: 'displayName',
                    headerName: 'Attribute',
                    flex: 1,
                    minWidth: 100,
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    flex: 1,
                    minWidth: 100,
                },
                {
                    field: 'sourceValue',
                    headerName: getColumnRevisionHeader(sourceEntity),
                    flex: 1,
                    minWidth: 100,
                    cellRenderer: PropertyValueRenderer,
                    autoHeight: true,
                    wrapText: true,
                },
                {
                    field: 'targetValue',
                    headerName: getColumnRevisionHeader(targetEntity),
                    flex: 1,
                    minWidth: 100,
                    cellRenderer: PropertyValueRenderer,
                    autoHeight: true,
                    wrapText: true,
                },
            ],
        };
    }, [sourceEntity, targetEntity]);
    return (
        <Box
            sx={{
                height: '100%',
                width: '100%',
                position: 'relative',
                '.dot': { display: 'none' },
                overflow: 'auto',
                ...tableStyles,
                '& .ag-theme-alpine .ag-row': {
                    '&.common-row': {
                        borderLeft: (theme) => `4px solid ${theme.palette.glide.background.normal.tealPrimary}`,
                        '&.with-diff': {
                            borderLeft: (theme) => `4px solid ${theme.palette.glide.background.normal.orangePrimary}`,
                        },
                    },
                    '&.unique-row': {
                        borderLeft: (theme) => `4px solid ${theme.palette.glide.background.normal.purplePrimary}`,
                        '&.target': {
                            borderLeft: (theme) => `4px solid ${theme.palette.glide.background.normal.yellowPrimary}`,
                        },
                    },
                },
                '& .ag-theme-alpine .ag-row-group': {
                    borderLeft: 'none !important',
                    fontSize: '14px',
                    fontWeight: 600,
                    lineHeight: '18px',
                    '&.common-row': {
                        backgroundColor: (theme) => `${theme.palette.glide.background.normal.tealSecondary}!important`,
                        '&.with-diff': {
                            backgroundColor: (theme) =>
                                `${theme.palette.glide.background.normal.orangeSecondary}!important`,
                        },
                    },
                    '&.unique-row': {
                        '&.source': {
                            backgroundColor: (theme) =>
                                `${theme.palette.glide.background.normal.purpleSecondary}!important`,
                        },
                        '&.target': {
                            backgroundColor: (theme) =>
                                `${theme.palette.glide.background.normal.yellowSecondary}!important`,
                        },
                    },
                },
            }}
        >
            <AgGridReact
                className="ag-theme-alpine"
                animateRows
                rowModelType={'clientSide'}
                rowData={rowData}
                groupDisplayType="groupRows"
                {...gridOptions}
            />
        </Box>
    );
};

export default AttributesComparison;
