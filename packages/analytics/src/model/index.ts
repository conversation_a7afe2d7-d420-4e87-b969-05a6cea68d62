/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Attribute, Classification, ClassificationDetail } from '@tripudiotech/api';

export type RevisionPattern = {
    major?: string;
    minor?: string;
    patch?: string;
    separator?: string;
    supportLevelRevise?: string[];
};
export type Lifecycle = {
    id: string;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    name: string;
    description: string;
    disabled: boolean;
    revisionPattern: RevisionPattern;
};

export type LifecycleState = {
    id: string;
    name: string;
    description: string;
    isSuperseded: boolean;
    isObsoleted: boolean;
    isOfficial: boolean;
    nextStates?: string[];
};
export type LifecycleDetail = {
    lifecycle: Lifecycle;
    states: Record<string, LifecycleState>;
};

export type EntityState = {
    name: string;
    isSuperseded: boolean;
    isObsoleted: boolean;
    isOfficial: boolean;
};

export type DetailEntity = {
    createdAt: string;
    createdBy: string;
    disabled: boolean;
    id: string;
    lifecycle: Lifecycle;
    entitySchema: DetailSchema;
    permissions: Record<string, string | boolean | number>;
    properties: Record<string, any>;
    updatedAt: string;
    state: EntityState;
    classifications: Classification[];
    lockedBy: any;
    isFirstLoad?: boolean;
    alternates?: any[];
};

export type DetailSchema = {
    attributes: Record<string, Attribute>;
    classifications: string[];
    entityType: {
        attributeOrder: string[];
        description: string;
        displayName: string;
        id: string;
        name: string;
        system: boolean;
        visible: boolean;
    };
    lifeCycles: Lifecycle[];
    relationTypes: any[];
    decompositionModel: {
        name: string;
        type: string;
        displayName: string;
        description: string;
    };
};

export type DetailClassificationSchema = {
    classification: ClassificationDetail;
    attributes: Record<string, Attribute>;
};

export type DetailEntitiesRequest = {
    sourceType: string;
    sourceId: string;
    targetType: string;
    targetId: string;
};

export type AnalyticsStore = {
    error: string;
    isLoading: boolean;
    showComparedItems: boolean;
    sourceEntity: DetailEntity;
    targetEntity: DetailEntity;
    sourceSchema: DetailSchema;
    targetSchema: DetailSchema;
    sourceBom: Bom[];
    targetBom: Bom[];
    sourceBomSchema: DetailSchema;
    targetBomSchema: DetailSchema;
    classificationSchema: Record<string, DetailClassificationSchema>;
    getDetailEntities: (request: DetailEntitiesRequest) => void;
    clearStore: () => void;
    toggleShowComparedItems: () => void;
    getBomSchema: (bomType: string, type: 'source' | 'target') => Promise<DetailSchema>;
    getBomComparisonData: () => void;
};

export type ComparisonBarProps = {
    id: string | any;
    source: {
        name?: string;
        total: number;
        color: string;
    };
    target?: {
        name?: string;
        total: number;
        color: string;
    };
    label: string;
    onClick?: (id: string, isSource: boolean) => void;
};

export enum ComparisonResultType {
    COMMON,
    COMMON_WITH_DIFFERENCES,
    UNIQUE_ON_SOURCE,
    UNIQUE_ON_TARGET,
}

export type Bom = {
    alternates?: any[];
    assemblyId?: string;
    component: Record<string, any>;
    componentPath?: Record<string, any>;
    id: string;
    rowId?: string;
    properties?: Record<string, any>;
    path?: string[];
    level?: number;
    uiStates?: Record<string, any>;
    compared?: boolean;
    comparisonResult?: {
        result: ComparisonResultType;
        commonWith?: {
            rowId: string;
        };
        differences?: Record<string, any>;
    };
};
