/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Provider } from 'react-redux';
import { BrowserRouter, Routes, Route, Outlet, Navigate } from 'react-router-dom';
import Box from '@mui/material/Box';
import { Notfound, AnimatedPage } from '@tripudiotech/styleguide';
import { Theme } from './Theme';
import MainLayout from './components/Layout/MainLayout';
import { ModuleRegistry } from '@ag-grid-community/core';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { FiltersToolPanelModule } from '@ag-grid-enterprise/filter-tool-panel';
import { RangeSelectionModule } from '@ag-grid-enterprise/range-selection';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ClipboardModule } from '@ag-grid-enterprise/clipboard';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';
import { InfiniteRowModelModule } from '@ag-grid-community/infinite-row-model';
import { GridChartsModule } from '@ag-grid-enterprise/charts';

import SelectCompareItem from './views/SelectCompareItem/SelectCompareItem';
import Attributes from './views/ComparisonResult/Attributes';
import BomComparison from './views/ComparisonResult/BomComparison';

import { LicenseManager } from '@ag-grid-enterprise/core';

LicenseManager.setLicenseKey(process.env.AG_GRID_LICENSE);

ModuleRegistry.registerModules([
    RowGroupingModule,
    ExcelExportModule,
    SetFilterModule,
    ColumnsToolPanelModule,
    FiltersToolPanelModule,
    RangeSelectionModule,
    MenuModule,
    ClipboardModule,
    ClientSideRowModelModule,
    ServerSideRowModelModule,
    InfiniteRowModelModule,
    GridChartsModule,
]);

const Wrapper = () => {
    return (
        <Box
            sx={{
                margin: 0,
                background: (theme) => theme.palette.glide.background.light,
                flexDirection: 'column',
                display: 'flex',
                height: 'calc(100vh - 56px)',
                marginTop: '56px',
            }}
            className="analytic-wrapper"
        >
            <Outlet />
        </Box>
    );
};

const App = () => {
    return (
        <Theme>
            <BrowserRouter>
                <Routes>
                    <Route path="analytics" element={<Wrapper />}>
                        <Route index element={<SelectCompareItem />} />
                        <Route
                            path="comparison-result/:sourceType/:sourceId/:targetType/:targetId/*"
                            element={<MainLayout />}
                        >
                            <Route path="" element={<Navigate to="attributes" />} />
                            <Route path="attributes" element={<Attributes />} />
                            <Route path="bom" element={<BomComparison />} />
                        </Route>
                        <Route path="*" element={<Notfound />} />
                    </Route>
                </Routes>
            </BrowserRouter>
        </Theme>
    );
};

export default App;
