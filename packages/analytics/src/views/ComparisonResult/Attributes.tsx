/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useMemo } from 'react';
import Box from '@mui/material/Box';
import { useAppStore } from '../../store/appStore';
import SelectedEntities from '../../components/Comparison/SelectedEntities';
import { styled } from '@mui/material';
import { AnimatedPage } from '@tripudiotech/styleguide';
import AttributesComparison from '../../components/Comparison/AttributesComparison';
import ComparisionBar from '../../components/Comparison/ComparisonBar';
import { getSummaryAttributesBar, groupAttributes } from '../../util/helper';

const Wrapper = styled('div')(({ theme }) => ({
    height: '100%',
    '& .contentHeader': {
        display: 'flex',
        gap: '40px',
        margin: '8px 16px',
        flexDirection: 'row',
        transition: 'all 0.35s ease-in',
        maxHeight: '150px',
        opacity: 1,
        [theme.breakpoints.down('md')]: {
            flexDirection: 'column',
            gap: '16px',
            maxHeight: '390px',
        },
    },
    '& .animatedContainer': {
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
    },
    '& .summaryChart': {
        width: '372px',
        [theme.breakpoints.down('md')]: {
            width: '100%',
            marginBottom: '8px',
        },
    },
    '& .content': {
        height: '100%',
    },
    '& .hide': {
        maxHeight: 0,
        opacity: 0,
        transition: 'all 0.35s ease-out',
        margin: '0 16px',
    },
}));

const Attributes = () => {
    const [showComparedItems, sourceEntity, targetEntity, sourceSchema, targetSchema, classificationSchema, isLoading] =
        useAppStore(
            ({
                showComparedItems,
                sourceEntity,
                targetEntity,
                sourceSchema,
                targetSchema,
                classificationSchema,
                isLoading,
            }) => [
                showComparedItems,
                sourceEntity,
                targetEntity,
                sourceSchema,
                targetSchema,
                classificationSchema,
                isLoading,
            ]
        );

    const rowData = useMemo(() => {
        if (isLoading) return null;
        return groupAttributes(sourceEntity, targetEntity, sourceSchema, targetSchema, classificationSchema);
    }, [isLoading]);

    const summaryBars = useMemo(() => {
        if (rowData) {
            return getSummaryAttributesBar(rowData, sourceEntity, targetEntity);
        }
        return null;
    }, [rowData]);

    return (
        !isLoading && (
            <Wrapper>
                <AnimatedPage className="animatedContainer">
                    <Box className={showComparedItems ? 'contentHeader' : 'contentHeader hide'}>
                        <SelectedEntities sourceEntity={sourceEntity} targetEntity={targetEntity} />
                        <Box className="summaryChart">
                            {summaryBars &&
                                summaryBars.attributes.map(({ id, label, source, target }) => (
                                    <ComparisionBar source={source} target={target} label={label} id={id} key={id} />
                                ))}
                        </Box>
                        <Box className="summaryChart">
                            {summaryBars &&
                                summaryBars.classifications.map(({ id, label, source, target }) => (
                                    <ComparisionBar source={source} target={target} label={label} id={id} key={id} />
                                ))}
                        </Box>
                    </Box>
                    <Box className="content">
                        <AttributesComparison
                            sourceEntity={sourceEntity}
                            targetEntity={targetEntity}
                            sourceSchema={sourceSchema}
                            targetSchema={targetSchema}
                            rowData={rowData}
                        />
                    </Box>
                </AnimatedPage>
            </Wrapper>
        )
    );
};

export default Attributes;
