/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { useAppStore } from '../../store/appStore';
import { styled, Box } from '@mui/material';
import TreeRenderer from '../../components/Renderer/TreeRenderer';
import { EXCLUDED_BOM_FIELDS } from '../../constants/common';
import debounce from 'lodash/debounce';
import { AgGridReact } from '@ag-grid-community/react';
import { tableStyles, tableIcons, LoadingOverlay, AnimatedPage, PropertyValueRenderer } from '@tripudiotech/styleguide';
import get from 'lodash/get';
import { ComparisonResultType } from '../../model';
import { getBomAttributesFromSchema, getTitle } from '../../util/helper';
import SelectedEntities from '../../components/Comparison/SelectedEntities';
import ComparisionBar from '../../components/Comparison/ComparisonBar';
import { useSchemaTree } from '@tripudiotech/caching-store';
import uniq from 'lodash/uniq';
import uniqBy from 'lodash/uniqBy';
import { ColDef, GridOptions } from '@ag-grid-community/core';

const Wrapper = styled('div')(({ theme }) => ({
    height: '100%',
    '& .ag-header-row .ag-header-row-column': {
        borderLeft: '4px solid transparent',
    },
    '& .contentHeader': {
        display: 'flex',
        gap: '40px',
        margin: '8px 16px',
        flexDirection: 'row',
        transition: 'all 0.35s ease-in',
        maxHeight: '150px',
        opacity: 1,
        [theme.breakpoints.down('md')]: {
            flexDirection: 'column',
            gap: '16px',
            maxHeight: '390px',
        },
    },
    '& .content': {
        height: '100%',
    },
    '& .animatedContainer': {
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
    },
    '& .hide': {
        maxHeight: 0,
        opacity: 0,
        transition: 'all 0.35s ease-out',
        margin: '0 16px',
    },
    '& .summaryChart': {
        width: '800px',
        [theme.breakpoints.down('md')]: {
            width: '100%',
            marginBottom: '8px',
        },
    },
}));

const getIndentClass = (params) => {
    let indent = 0;
    let node = params.node;
    while (node && node.parent) {
        indent++;
        node = node.parent;
    }
    return 'ag-cell-wrapper hyperlink indent-' + indent;
};
const autoGroupColumnDef: ColDef = {
    field: 'component.name',
    headerName: 'Name',
    cellClass: getIndentClass,
    flex: 1,
    filter: 'agTextColumnFilter',
    minWidth: 150,
    cellRendererParams: {
        suppressCount: true,
        innerRenderer: TreeRenderer,
        typePath: 'component.type',
        idPath: 'component.id',
    },
    chartDataType: 'category',
};

const BomComparison = () => {
    const {
        showComparedItems,
        sourceEntity,
        targetEntity,
        sourceBom,
        targetBom,
        sourceBomSchema,
        targetBomSchema,
        getBomComparisonData,
    } = useAppStore();
    const { schemaTreeMap } = useSchemaTree();

    const sourceGridRef = useRef<AgGridReact>(null);
    const targetGridRef = useRef<AgGridReact>(null);
    useEffect(() => {
        if (sourceEntity && targetEntity && !sourceBom && !targetBom) {
            getBomComparisonData();
        }
    }, [sourceEntity, targetEntity, sourceBom, targetBom]);

    const dynamicAttributes = useMemo(() => {
        if (!sourceBomSchema || !targetBomSchema) return null;
        const attributeOrder = uniq([
            ...sourceBomSchema.entityType.attributeOrder,
            ...targetBomSchema.entityType.attributeOrder,
        ]);

        return [...Object.values(sourceBomSchema.attributes), ...Object.values(targetBomSchema.attributes)]
            .filter(
                (attribute) => attribute.visible && !attribute.richText && !EXCLUDED_BOM_FIELDS.includes(attribute.name)
            )
            .sort((a, b) => attributeOrder.indexOf(a.name) - attributeOrder.indexOf(b.name));
    }, [sourceBomSchema, targetBomSchema]);

    const getRowId = useCallback((params) => {
        return params.data.rowId;
    }, []);

    const getContextMenuItems = useCallback(() => {
        return ['autoSizeAll', 'expandAll', 'contractAll', 'separator', 'copy', 'export', 'chartRange'];
    }, []);

    const onRowGroupOpened = useCallback(
        debounce(() => {
            sourceGridRef.current.columnApi.autoSizeColumn('ag-Grid-AutoColumn');
            targetGridRef.current.columnApi.autoSizeColumn('ag-Grid-AutoColumn');
        }, 120),
        []
    );

    const onFirstDataRendered = useCallback(() => {
        onRowGroupOpened();
    }, []);

    const isGroupOpenByDefault = useCallback((params) => {
        return params.level === 0;
    }, []);

    const getDataPath = useCallback((data) => {
        return data.path;
    }, []);

    const gridOptions: GridOptions = useMemo(() => {
        if (!sourceBomSchema || !targetBomSchema) {
            return {};
        }
        const dynamicColumnDefs: ColDef[] = uniqBy(
            [...getBomAttributesFromSchema(sourceBomSchema), ...getBomAttributesFromSchema(targetBomSchema)],
            'name'
        ).map((attribute) => ({
            field: `properties.${attribute.name}`,
            headerName: `${attribute.displayName}`,
            flex: 1,
            minWidth: 150,
            cellStyle: (params) => {
                const differences = get(params, ['data', 'comparisonResult', 'differences', 'attributes']);

                if (differences && differences.includes(attribute.name)) {
                    return {
                        backgroundColor: '#FA8C16',
                    };
                }
            },
            cellRenderer: PropertyValueRenderer,
            cellRendererParams: {
                schema: attribute,
            },
        }));

        const columnDefs: ColDef[] = [
            {
                field: 'component.type',
                headerName: 'Type',
                minWidth: 160,
                flex: 1,
                cellStyle: {},
                editable: false,
                filter: 'agSetColumnFilter',
                chartDataType: 'category',
                valueGetter: (props) => {
                    if (props.context.schemaTreeMap) {
                        return (
                            props.context.schemaTreeMap?.[props.data?.component?.type]?.displayName ||
                            props.data?.component?.type
                        );
                    }
                    return props.data?.component?.type;
                },
            },
            {
                field: 'component.revision',
                headerName: 'Revision',
                minWidth: 130,
                flex: 1,
                editable: false,
                filter: 'agTextColumnFilter',
                chartDataType: 'category',
                cellStyle: { color: '#52C41A' },
            },
            ...dynamicColumnDefs,
        ];

        return {
            headerHeight: 34,
            enableGroupEdit: true,
            enableRangeSelection: true,
            enableCharts: true,
            getContextMenuItems,
            columnDefs,
            defaultColDef: {
                sortable: true,
                resizable: true,
                filter: true,
                flex: 1,
                floatingFilter: false,
                enableRowGroup: false,
                enablePivot: false,
                autoHeight: true,
                wrapText: true,
            },
            isGroupOpenByDefault,
            undoRedoCellEditing: true,
            getDataPath,
            rowModelType: 'clientSide',
            getRowId,
            onRowGroupOpened,
            groupDisplayType: 'singleColumn',
            treeData: true,
            animateRows: true,
            rowSelection: 'single',
            stopEditingWhenCellsLoseFocus: true,
            rowHeight: 34,
            floatingFiltersHeight: 34,
            getRowStyle: (params) => {
                const comparisonResultType = get(params, 'data.comparisonResult.result');
                switch (comparisonResultType) {
                    case ComparisonResultType.COMMON:
                        return {
                            borderLeft: '4px solid #13C2C2',
                        };
                    case ComparisonResultType.COMMON_WITH_DIFFERENCES:
                        return {
                            borderLeft: '4px solid #FA8C16',
                        };
                    case ComparisonResultType.UNIQUE_ON_SOURCE:
                        return {
                            borderLeft: '4px solid #722ED1',
                        };
                    case ComparisonResultType.UNIQUE_ON_TARGET:
                        return {
                            borderLeft: '4px solid #FADB14',
                        };
                    default:
                        return {
                            borderLeft: '4px solid transparent',
                        };
                }
            },
            icons: tableIcons,
            autoGroupColumnDef,
            onFirstDataRendered,
            showOpenedGroup: true,
        };
    }, [sourceBomSchema, targetBomSchema, dynamicAttributes]);

    const onSourceSelectionChanged = useCallback((sourceGridRef, targetGridRef) => {
        const selectedRows = sourceGridRef.current.api.getSelectedRows();
        if (selectedRows.length === 0) return;

        const commonRowId = get(selectedRows, ['0', 'comparisonResult', 'commonWith', 'rowId']);
        const isCommon = [ComparisonResultType.COMMON, ComparisonResultType.COMMON_WITH_DIFFERENCES].includes(
            get(selectedRows, ['0', 'comparisonResult', 'result'])
        );
        const selectedBomId = get(selectedRows, ['0', 'id']);
        if (commonRowId) {
            const targetRow = targetGridRef.current.api.getRowNode(commonRowId);

            targetGridRef.current.api.ensureNodeVisible(targetRow);
            // This check is important to avoid memory leaks due to infinite loop of two grids
            if (!targetRow.selected) {
                targetGridRef.current.api.deselectAll();
                targetRow.setSelected(true, false, true);
            }
        } else if (isCommon) {
            // sub rows
            const firstLevelParentSourceRowId = get(selectedRows, ['0', 'path', '1']);
            const parentSourceRowNode = sourceGridRef.current.api.getRowNode(firstLevelParentSourceRowId);
            if (parentSourceRowNode) {
                const targetNode = targetGridRef.current.api.getRowNode(
                    get(parentSourceRowNode.data, ['comparisonResult', 'commonWith', 'rowId'], '')
                );
                if (targetNode) {
                    targetNode.allLeafChildren.forEach((child) => {
                        child.setExpanded(true);
                        if (child.data?.id === selectedBomId) {
                            if (!child.selected) {
                                targetGridRef.current.api.deselectAll();
                                child.setSelected(true, false, true);
                            }
                            targetGridRef.current.api.ensureNodeVisible(child);
                        }
                    });
                }
            }
        } else {
            targetGridRef.current.api.deselectAll();
        }
    }, []);

    const sourceGridOptions: GridOptions = useMemo(
        () => ({
            ...gridOptions,
            onSelectionChanged: () => onSourceSelectionChanged(sourceGridRef, targetGridRef),
        }),
        [gridOptions]
    );
    const targetGridOptions: GridOptions = useMemo(
        () => ({ ...gridOptions, onSelectionChanged: () => onSourceSelectionChanged(targetGridRef, sourceGridRef) }),
        [gridOptions]
    );

    const summaryBars = useMemo(() => {
        if (sourceBom && targetBom && sourceEntity && targetEntity) {
            const commonComponents = sourceBom.filter(
                (bom) =>
                    bom.comparisonResult?.result === ComparisonResultType.COMMON ||
                    (bom.comparisonResult?.result === ComparisonResultType.COMMON_WITH_DIFFERENCES && bom.level > 1)
            );
            const commonWithDiff = sourceBom.filter(
                (bom) =>
                    bom.comparisonResult?.result === ComparisonResultType.COMMON_WITH_DIFFERENCES && bom.level === 1
            );
            const uniqueOnSource = sourceBom.filter(
                (bom) => bom.comparisonResult?.result === ComparisonResultType.UNIQUE_ON_SOURCE
            );
            const uniqueOnTarget = targetBom.filter(
                (bom) => bom.comparisonResult?.result === ComparisonResultType.UNIQUE_ON_TARGET
            );
            return [
                {
                    id: 'common-components',
                    source: {
                        name: 'Similar',
                        total: commonComponents.length,
                        color: '#13C2C2',
                    },
                    target: {
                        name: 'Different',
                        total: commonWithDiff.length,
                        color: '#FA8C16',
                    },
                    label: 'Common Components',
                },
                {
                    id: 'unique-attributes',
                    source: {
                        name: `Bom of ${getTitle(sourceEntity)}`,
                        total: uniqueOnSource.length,
                        color: '#722ED1',
                    },
                    target: {
                        name: `Bom of ${getTitle(targetEntity)}`,
                        total: uniqueOnTarget.length,
                        color: '#E4C916',
                    },
                    label: 'Unique Components',
                },
            ];
        }
    }, [sourceBom, targetBom, targetEntity, sourceEntity]);

    return sourceBomSchema && targetBomSchema && sourceBom && targetBom ? (
        <Wrapper>
            <AnimatedPage className="animatedContainer">
                <Box className={showComparedItems ? 'contentHeader' : 'contentHeader hide'}>
                    <SelectedEntities sourceEntity={sourceEntity} targetEntity={targetEntity} />
                    <Box className="summaryChart">
                        {summaryBars &&
                            summaryBars.map(({ id, label, source, target }) => (
                                <ComparisionBar source={source} target={target} label={label} id={id} key={id} />
                            ))}
                    </Box>
                </Box>
                <Box
                    sx={{
                        display: 'flex',
                        gap: '8px',
                        height: '100%',
                        width: '100%',
                        ...tableStyles,
                    }}
                >
                    <Box className="ag-theme-alpine" sx={{ height: '100%', width: '50%' }}>
                        <AgGridReact
                            gridOptions={sourceGridOptions}
                            rowData={sourceBom}
                            ref={sourceGridRef}
                            context={{ schemaTreeMap: schemaTreeMap }}
                        />
                    </Box>
                    <Box className="ag-theme-alpine" sx={{ height: '100%', width: '50%' }}>
                        <AgGridReact
                            gridOptions={targetGridOptions}
                            rowData={targetBom}
                            ref={targetGridRef}
                            context={{ schemaTreeMap: schemaTreeMap }}
                        />
                    </Box>
                </Box>
            </AnimatedPage>
        </Wrapper>
    ) : (
        <LoadingOverlay />
    );
};

export default BomComparison;
