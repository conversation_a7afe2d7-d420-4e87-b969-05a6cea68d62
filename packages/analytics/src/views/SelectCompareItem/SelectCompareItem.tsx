/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
    Box,
    Button,
    CircularProgress,
    InputAdornment,
    TextField,
    Typography,
    useMediaQuery,
    useTheme,
} from '@mui/material';
import {
    SearchIcon,
    ComparisonIcon,
    CloseIcon,
    filterParams,
    Loading,
    tableIcons,
    buildSortParams,
    buildQueryBasedOnFilter,
    tableStyles,
    NoRowsOverlay,
    AnimatedPage,
} from '@tripudiotech/styleguide';
import { fetch as apiFetch, entityUrls, buildOrOperatorQuery, buildContainsQuery } from '@tripudiotech/api';
import NameRenderer from './NameRenderer';
import StatusCellRenderer from './StatusCellRenderer';
import { AgGridReact } from '@ag-grid-community/react';
import isNil from 'lodash/isNil';
import debounce from 'lodash/debounce';
import { useNavigate } from 'react-router-dom';
import { useSchemaTree } from '@tripudiotech/caching-store';
import { ColDef, GridOptions } from '@ag-grid-community/core';

const columnDefs: ColDef[] = [
    {
        field: 'properties.name',
        headerName: 'Name',
        cellRenderer: NameRenderer,
        flex: 1,
        filter: 'agTextColumnFilter',
        filterParams,
        checkboxSelection: true,
        minWidth: 220,
        showDisabledCheckboxes: true,
        pinned: 'left',
        headerTooltip: 'Entity Name',
    },
    {
        field: 'properties.title',
        headerName: 'Title',
        flex: 1,
        filter: 'agTextColumnFilter',
        minWidth: 120,
        headerTooltip: 'Entity Title',
    },
    {
        field: 'properties.type',
        headerName: 'Type',
        flex: 1,
        filter: 'agTextColumnFilter',
        minWidth: 120,
        headerTooltip: 'Entity Type',
        valueGetter: (props) => {
            if (props.context.schemaTreeMap) {
                return (
                    props.context.schemaTreeMap?.[props.data?.properties?.type]?.displayName ||
                    props.data?.properties?.type
                );
            }
            return props.data?.properties?.type;
        },
    },
    {
        field: 'properties.revision',
        headerName: 'Revision',
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        minWidth: 120,
        cellStyle: (params) => {
            return {
                color: '#52C41A',
            };
        },
    },
    {
        field: 'lifecycle.name',
        headerName: 'Lifecycle',
        flex: 1,
        filter: false,
        minWidth: 100,
        filterParams,
        headerTooltip: 'Lifecycle',
    },
    {
        field: 'state.name',
        headerName: 'Status',
        cellRenderer: StatusCellRenderer,
        flex: 1,
        filter: false,
        minWidth: 100,
        headerTooltip: 'Current state of the entity',
    },
    {
        field: 'properties.description',
        headerName: 'Description',
        maxWidth: 250,
        flex: 1,
        filter: 'agTextColumnFilter',
        filterParams,
        valueFormatter: ({ value }) => {
            const plainText = value ? value.replace(/<\/?[^>]+>/gi, ' ') : value;
            return plainText;
        },
        headerTooltip: 'Description',
    },
];

const SelectCompareItem = () => {
    const gridRef = useRef<AgGridReact>();
    const [search, setSearch] = useState('');
    const searchQuery = useRef(null);
    const [selectedEntities, setSelectedRows] = useState([]);
    const navigate = useNavigate();
    const getRowId = useCallback((params) => {
        return params.data.id;
    }, []);
    const { schemaTreeMap } = useSchemaTree();

    const onFirstDataRendered = useCallback(() => {
        gridRef.current.columnApi.autoSizeColumn('properties.name');
    }, []);

    const handleSetDataSource = useCallback(({ api }) => {
        const dataSource = getDataSource(gridRef);
        api.setServerSideDatasource(dataSource);
    }, []);

    const buildSearchQuery = useCallback((search) => {
        const searchText = search || '';
        return buildOrOperatorQuery([
            buildContainsQuery('name', searchText),
            buildContainsQuery('description', searchText),
        ]);
    }, []);

    const getDataSource = (gridRef) => {
        const buildParams = (params) => {
            let queryParams = {
                offset: params.startRow || 0,
                limit: 50,
                ...buildSortParams(params.sortModel),
                fields: ['revision'],
            };
            const filterModel = params.filterModel;
            if (filterModel && Object.keys(filterModel).length > 0) {
                const filterConditions = [];
                if (searchQuery.current) {
                    filterConditions.push(buildSearchQuery(searchQuery.current));
                }
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            } else if (searchQuery.current) {
                queryParams['query'] = JSON.stringify(buildSearchQuery(searchQuery.current));
            }
            return queryParams;
        };
        return {
            getRows: (params) => {
                if (isNil(searchQuery.current) || searchQuery.current.length < 2) {
                    params.successCallback([], 0);
                    gridRef.current.api.showNoRowsOverlay();
                    return;
                }
                apiFetch({
                    ...entityUrls.getListEntitySysRoot,
                    qs: buildParams(params.request),
                })
                    .then((response) => {
                        const {
                            status,
                            data: { data, pageInfo },
                        } = response;
                        if (status !== 200) {
                            params.fail();
                            return;
                        }
                        params.success({
                            rowData: data,
                            rowCount: pageInfo.total || data.length,
                        });
                    })
                    .catch(() => {
                        params.fail();
                    })
                    .finally(() => {
                        gridRef.current.api.hideOverlay();
                        if (gridRef.current.api.getDisplayedRowCount() === 0) {
                            gridRef.current.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    };

    const onSelectionChanged = useCallback(() => {
        const nodes = gridRef.current.api.getSelectedNodes();
        if (nodes.length > 2) {
            nodes[0].setSelected(false);
        }

        const rows = gridRef.current.api.getSelectedRows();
        gridRef.current.api.setPinnedTopRowData(rows);
        setSelectedRows(rows);
    }, []);

    const gridOptions: GridOptions = useMemo(
        () => ({
            headerHeight: 34,
            rowHeight: 36,
            floatingFiltersHeight: 36,
            columnDefs,
            loadingOverlayComponent: Loading,
            defaultColDef: {
                minWidth: 100,
                sortable: true,
                resizable: true,
                filter: true,
            },
            cacheBlockSize: 50,
            suppressColumnVirtualisation: true,
            serverSideInfiniteScroll: true,
            suppressMenuHide: true,
            icons: tableIcons,
            loadingCellRenderer: () => <CircularProgress style={{ marginLeft: '16px' }} size={16} color="info" />,
            onSelectionChanged,
            rowSelection: 'multiple',
            getRowId,
            suppressRowClickSelection: true,
            rowStyle: {
                backgroundColor: '#FFFFFF',
                alignItems: 'center',
            },
            rowModelType: 'serverSide',
            onFirstDataRendered,
            onGridReady: handleSetDataSource,
            noRowsOverlayComponent: NoRowsOverlay,
            noRowsOverlayComponentParams: {
                search: searchQuery,
                initialMessage: 'Search two entities to compare',
                minLength: 3,
            },
        }),
        [handleSetDataSource, onFirstDataRendered, onSelectionChanged]
    );

    const onSearch = useCallback((e) => {
        setSearch(e.target.value);
        searchQuery.current = e.target.value;
    }, []);

    const handleSearchChanged = useCallback(
        debounce((search) => {
            const dataSource = getDataSource(gridRef);
            gridRef.current.api.setServerSideDatasource(dataSource);
        }, 400),
        []
    );

    const onCompare = useCallback(() => {
        if (selectedEntities.length === 2) {
            const {
                id: sourceId,
                properties: { type: sourceType },
            } = selectedEntities[0];
            const {
                id: targetId,
                properties: { type: targetType },
            } = selectedEntities[1];
            navigate(`comparison-result/${sourceType}/${sourceId}/${targetType}/${targetId}/attributes`);
        }
    }, [selectedEntities]);

    useEffect(() => {
        handleSearchChanged(search);
    }, [search]);

    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    return (
        <AnimatedPage style={{ height: '100%', width: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box
                sx={{
                    width: '100%',
                    display: 'flex',
                    gap: '40px',
                    padding: '12px 16px',
                    alignItems: 'center',
                }}
            >
                <Typography variant="title3">Compare</Typography>
                <TextField
                    sx={{
                        '& input': {
                            paddingLeft: '4px !important',
                            fontSize: '14px',
                            height: '24px',
                        },
                    }}
                    onChange={onSearch}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        ),
                    }}
                    placeholder="Type at least 2 characters to search"
                    fullWidth
                    variant="outlined"
                    size="small"
                />
            </Box>
            <Box
                sx={{
                    width: '100%',
                    display: 'flex',
                    gap: '1px',
                    '& .MuiButton-endIcon': {
                        ml: '32px',
                        mr: 0,
                    },
                }}
            >
                <Box
                    sx={{
                        backgroundColor: (theme) => theme.palette.glide.background.normal.tertiary,
                        padding: isMobile ? '11px 16px' : '11px 0 11px 24px',
                        flexGrow: 1,
                    }}
                >
                    <Typography
                        variant="label2-med"
                        sx={{
                            color: (theme) => theme.palette.glide.text.normal.tertiary,
                        }}
                    >
                        {selectedEntities.length}/2{isMobile ? '' : ' items selected'}
                    </Typography>
                </Box>
                <Button
                    onClick={onCompare}
                    variant="contained"
                    color="primary"
                    endIcon={<ComparisonIcon />}
                    sx={{
                        background: (theme) => theme.palette.glide.background.normal.tertiary,
                    }}
                >
                    Compare
                </Button>
                <Button
                    variant="contained"
                    color="primary"
                    endIcon={<CloseIcon />}
                    onClick={() => gridRef.current.api.deselectAll()}
                    sx={{
                        background: (theme) => theme.palette.glide.background.normal.tertiary,
                    }}
                >
                    Deselect all
                </Button>
            </Box>
            <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    ...tableStyles,
                }}
            >
                <div className="ag-theme-alpine" style={{ height: '100%', width: '100%' }}>
                    <AgGridReact {...gridOptions} ref={gridRef} context={{ schemaTreeMap: schemaTreeMap }} />
                </div>
            </Box>
        </AnimatedPage>
    );
};

export default SelectCompareItem;
