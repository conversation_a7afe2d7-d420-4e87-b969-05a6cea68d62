/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useState } from 'react';
import { Button, Menu, MenuItem, Box, Typography, styled } from '@mui/material';
import { ChevronDown } from '@tripudiotech/styleguide';
import { usePlant } from '@tripudiotech/caching-store';
import { authenticationService, Plant } from '@tripudiotech/api';

const PlantButton = styled(Button)(({ theme }) => ({
    [theme.breakpoints.down('md')]: {
        '& .text': {
            display: 'none',
        },
        '& .MuiButton-endIcon': {
            marginLeft: '-2px',
        },
        borderRadius: '4px',
        width: '32px',
        minWidth: '32px',
    },
}));

const PlantSelector: React.FC = () => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const { plants, selectedPlant, fetchPlants, changePlant, getByName } = usePlant();

    useEffect(() => {
        fetchPlants();
    }, []);

    useEffect(() => {
        // Get plant name from local storage
        const plantName = authenticationService.getPlant();
        if (plants.length > 0) {
            const plant = plantName ? getByName(plantName) : undefined;
            if (plant) {
                changePlant(plant);
            } else {
                changePlant(plants[0]);
            }
        }
    }, [plants]);

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handlePlantSelect = (plant: Plant) => {
        changePlant(plant);
        handleClose();
        // Reload the page to apply the new plant filter
        window.location.reload();
    };

    return (
        <Box>
            <PlantButton
                sx={{
                    color: open ? 'black' : '#649BD6',
                    borderRadius: '4px',
                }}
                size="small"
                variant={open ? 'contained-white' : 'text'}
                onClick={handleClick}
                endIcon={
                    <ChevronDown
                        sx={{
                            width: 16,
                            height: 16,
                            transform: open ? 'rotate(180deg)' : 'none',
                            transition: 'transform 0.2s ease',
                        }}
                    />
                }
            >
                <span className="text">{selectedPlant ? selectedPlant.displayName : 'Select Plant'}</span>
            </PlantButton>

            <Menu
                id="plant-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                    'aria-labelledby': 'basic-button',
                }}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
                sx={{
                    '& .MuiPaper-root': {
                        borderRadius: '4px',
                        marginTop: '4px',
                        padding: '4px 8px',
                        minWidth: anchorEl ? (anchorEl as HTMLElement).offsetWidth : undefined,
                    },
                }}
            >
                {plants.map((plant) => (
                    <MenuItem
                        key={plant.id}
                        selected={selectedPlant?.id === plant.id}
                        onClick={() => handlePlantSelect(plant)}
                        sx={{
                            borderRadius: '4px',
                            '&.Mui-selected': {
                                backgroundColor: '#334466 !important',
                                color: 'white !important',
                            },
                            '&.Mui-selected:hover': {
                                backgroundColor: '#3b4d75 !important',
                            },
                            '&:hover': {
                                backgroundColor: '#334466 !important',
                                color: 'white',
                            },
                        }}
                    >
                        <Box>
                            <Typography variant="inherit" sx={{ fontWeight: 500 }}>
                                {plant.displayName}
                            </Typography>
                        </Box>
                    </MenuItem>
                ))}
            </Menu>
        </Box>
    );
};

export default PlantSelector;
