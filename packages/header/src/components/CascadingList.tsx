/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Loading, tableIcons, tableStyles } from '@tripudiotech/styleguide';
import { ColDef, GridOptions } from '@ag-grid-community/core';
import { Box } from '@mui/material';
import { AgGridReact } from '@ag-grid-community/react';
import { useRef, useMemo } from 'react';

const CASCADING_LIST_SEPARATOR = '|';

const getTreeDataFromCascadingList = (cascadingItems: string[]) => {
    const nodeMap = new Map<string, any>();
    return cascadingItems.map((item) => {
        const path = item.split(CASCADING_LIST_SEPARATOR);

        return {
            id: item,
            name: path[path.length - 1],
            path,
        };
    });
};

const GRID_OPTIONS: GridOptions = {
    loadingOverlayComponent: Loading,
    rowDragMultiRow: true,
    animateRows: true,
    defaultColDef: {
        sortable: true,
        resizable: true,
        flex: 1,
        filter: true,
        floatingFilter: false,
        enablePivot: true,
        autoHeight: true,
        wrapText: true,
        cellStyle: {
            'word-break': 'normal',
        },
    },
    isRowSelectable: (params) => params.data,
    suppressMenuHide: true,
    rowModelType: 'clientSide',
    rowSelection: 'multiple',
    headerHeight: 34,
    rowHeight: 40,
    groupHeaderHeight: 34,
    floatingFiltersHeight: 34,
    rowStyle: {
        backgroundColor: '#FFFFFF',
        alignItems: 'center',
    },
    enableCharts: true,
    enableRangeSelection: true,
    cacheBlockSize: 100,
    paginateChildRows: true,
    suppressPaginationPanel: true,
    suppressDragLeaveHidesColumns: true,
    isGroupOpenByDefault: (params) => params.level === 0,
    getDataPath: (data) => data.path,
    treeData: true,
    getRowId: (params) => params.data.id,
    icons: tableIcons,
};

interface CascadingListProps {
    cascadingListItems: string[];
    value: string[];
    onChange: (value: string[]) => void;
    displayName: string;
}

export const CascadingList = ({ cascadingListItems, value, onChange, displayName }: CascadingListProps) => {
    const gridRef = useRef<AgGridReact>(null);
    const treeData = getTreeDataFromCascadingList(cascadingListItems);

    const autoGroupColumnDef: ColDef = useMemo(
        () => ({
            flex: 1,
            field: 'name',
            headerName: displayName ?? 'Name',
            editable: false,
            filter: 'agTextColumnFilter',
            checkboxSelection: (params) => {
                return !params.node.group;
            },
            cellRendererParams: {
                suppressCount: true,
            },
        }),
        [displayName]
    );

    return (
        <Box
            id="cascading-list-table"
            sx={{
                height: '100%',
                minHeight: '500px',
                width: '100%',
                borderRadius: 8,
                marginBottom: '8px',
                marginLeft: '0px',
                ...tableStyles,
            }}
            className="ag-theme-alpine"
        >
            <AgGridReact
                {...GRID_OPTIONS}
                ref={gridRef}
                rowData={treeData}
                onSelectionChanged={({ api }) => {
                    const selectedNodes = api?.getSelectedRows()?.map((row) => row.id);
                    onChange(selectedNodes);
                }}
                onFirstDataRendered={() => {
                    value?.map((row) => {
                        gridRef.current?.api.getRowNode(row)?.setSelected(true);
                    });
                }}
                autoGroupColumnDef={autoGroupColumnDef}
            />
        </Box>
    );
};
