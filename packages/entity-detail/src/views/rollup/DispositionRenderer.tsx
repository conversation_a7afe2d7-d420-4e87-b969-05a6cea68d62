/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { ICellRendererParams } from '@ag-grid-community/core';
import { Box, IconButton } from '@mui/material';
import { ClearIcon, EditIcon, MainTooltip } from '@tripudiotech/styleguide';
import { RollupData } from './types';

export const DispositionActionsRenderer = ({ params }: { params: ICellRendererParams<RollupData> }) => {
    const bugId = params.node.data?.entity?.id;
    const dispositionId = params.node.data?.disposition?.id;
    if (!bugId) return <></>;
    return (
        <Box sx={{ display: 'flex', gap: '2px', alignItems: 'center' }}>
            <MainTooltip title={dispositionId ? 'Edit Disposition' : 'Add Disposition'}>
                <IconButton
                    onClick={() => {
                        params?.columnApi?.setColumnGroupOpened('disposition', true);
                        setTimeout(() => {
                            params?.api.startEditingCell({
                                rowIndex: params.node.rowIndex,
                                colKey: 'disposition.dispositionType',
                            });
                        }, 100);
                    }}
                    sx={{ width: 'fit-content', height: 'fit-content' }}
                >
                    <EditIcon style={{ width: '18px', height: '18px' }} />
                </IconButton>
            </MainTooltip>
            {params.node.data.isEdited && (
                <MainTooltip title="Clear Changes">
                    <IconButton
                        onClick={() => {
                            params.context.setEditedDispositions((old) => {
                                const { [bugId]: existingDisposition, ...remainingDispositions } = old;
                                Object.keys(existingDisposition).forEach((dispositionProperty) => {
                                    if (dispositionProperty !== 'id' && dispositionProperty !== 'type') {
                                        params.node.setDataValue(
                                            `${dispositionProperty}`,
                                            existingDisposition[dispositionProperty]?.oldValue,
                                            'clearFormatting'
                                        );
                                    }
                                });
                                params.node.data.isEdited = false;
                                params.api.refreshCells({
                                    rowNodes: [params.node],
                                    columns: ['actions'],
                                    force: true,
                                });
                                return remainingDispositions;
                            });
                        }}
                        sx={{ width: 'fit-content', height: 'fit-content' }}
                    >
                        <ClearIcon style={{ width: '18px', height: '18px' }} />
                    </IconButton>
                </MainTooltip>
            )}
        </Box>
    );
};
