/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    AnimatedPage,
    EntityNameRenderer,
    ExpandMoreIcon,
    FilterIcon,
    GroupObjectIcon,
    Loading,
    LoadingOverlay,
    PropertyValueRenderer,
    tableIcons,
    TablePagination,
    tableStyles,
    DEFAULT_CELL_WIDTH,
} from '@tripudiotech/styleguide';
import type {
    CellValueChangedEvent,
    ColDef,
    GridOptions,
    ICellRendererParams,
    ColGroupDef,
} from '@ag-grid-community/core';
import { DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH, DEFAULT_TABLE_PAGINATION_SIZE } from '../../constants/common';
import { Box, Button, CircularProgress, MenuItem, Select, Typography } from '@mui/material';
import FixedContainer from '../../components/Layout/FixedContainer';
import { ToolbarItem, ToolbarWrapper } from '../../components/Relations/Toolbar';
import { AgGridReact } from '@ag-grid-community/react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
    Attribute,
    AttributeType,
    batchRequest,
    batchRequestBody,
    entityUrls,
    fetch,
    filterAndSortAttributes,
    handleBatchRequestRes,
    RELATION_DIRECTION,
    SYSTEM_ENTITY_TYPE,
    SYSTEM_RELATION,
} from '@tripudiotech/api';
import { useParams } from 'react-router-dom';
import { SchemaWithLifeCycleDetail, useSchemaDetail, useSchemaTree } from '@tripudiotech/caching-store';
import { useSelector } from 'react-redux';
import { selectAppReducer } from '../../selectors';
import NoRowsOverlay from '../../components/NoRowsOverlay/NoRowsOverlay';
import StatusCellRenderer from '../../components/AttributeDetail/StatusCellRenderer';
import SavedFilterSidebar from '../../components/SavedFilter/SavedFilter';
import get from 'lodash/get';
import { buildEditableColumnDefs } from '../../utils/columnDefsBuilder';
import flatten from 'lodash/flatten';
import { RollupData } from './types';
import { DispositionActionsRenderer } from './DispositionRenderer';

const autoGroupColumnDef: ColDef = {
    minWidth: 140,
    flex: 1,
    filter: true,
    filterValueGetter: (params) => {
        const colId = params.column.getColId();
        const key = colId.replace('ag-Grid-AutoColumn-', '');
        if (key === 'reportedAgainst' || key === 'introducedIn') {
            return params.data?.[key] ? params.data?.[key]?.name + ' ' + params.data?.[key]?.revision : '';
        }

        return get(params.data, key) || '';
    },
};

const GRID_OPTIONS: GridOptions<RollupData> = {
    loadingOverlayComponent: Loading,
    rowDragMultiRow: true,
    animateRows: true,
    defaultColDef: {
        sortable: true,
        resizable: true,
        filter: true,
        flex: 1,
        floatingFilter: false,
        enablePivot: true,
        enableRowGroup: true,
        autoHeight: true,
        minWidth: DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH,
    },
    suppressMenuHide: true,
    rowModelType: 'clientSide',
    rowSelection: 'multiple',
    headerHeight: 34,
    rowHeight: 40,
    groupHeaderHeight: 34,
    floatingFiltersHeight: 34,
    sideBar: {
        toolPanels: [
            {
                id: 'columns',
                labelDefault: 'Columns',
                labelKey: 'columns',
                iconKey: 'columns',
                toolPanel: 'agColumnsToolPanel',
            },
            {
                id: 'filters',
                labelDefault: 'Filters',
                labelKey: 'filters',
                iconKey: 'filter',
                toolPanel: 'agFiltersToolPanel',
            },
        ],
    },
    rowStyle: {
        backgroundColor: '#FFFFFF',
        alignItems: 'center',
    },
    enableCharts: true,
    rowGroupPanelShow: 'always',
    enableRangeSelection: true,
    cacheBlockSize: 100,
    pagination: true,
    paginationPageSize: DEFAULT_TABLE_PAGINATION_SIZE,
    paginateChildRows: true,
    suppressPaginationPanel: true,
    suppressDragLeaveHidesColumns: true,
    groupDisplayType: 'multipleColumns',
    isGroupOpenByDefault: (params) => params.level === 0,
    autoGroupColumnDef: autoGroupColumnDef,
    icons: tableIcons,
    enableFillHandle: true,
};

const DEFAULT_COLUMN_DEFS: ColDef<RollupData>[] = [
    {
        field: 'entity.type',
        headerName: 'Type',
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        minWidth: 100,
        valueGetter: (props) => {
            if (props.context.schemaTreeMap) {
                return props.context.schemaTreeMap?.[props.data?.entity?.type]?.displayName || props.data?.entity?.type;
            }
            return props.data?.entity?.type;
        },
    },
    {
        field: 'reportedAgainst',
        headerName: 'Reported Against / Requested On',
        minWidth: 200,
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        cellRenderer: (params: ICellRendererParams<RollupData>) => (
            <EntityNameRenderer
                data={{
                    ...params.data?.reportedAgainst,
                    properties: {
                        type: params.data?.reportedAgainst?.type,
                        name: params.data?.reportedAgainst?.name,
                        id: params.data?.reportedAgainst?.id,
                    },
                }}
                type={params.data?.reportedAgainst?.type}
                value={
                    params.data?.reportedAgainst?.name
                        ? params.data?.reportedAgainst?.name + ' ' + params.data?.reportedAgainst?.revision
                        : params.node.field === 'reportedAgainst'
                        ? params.node.key
                        : ''
                }
            />
        ),
        valueGetter: (props) =>
            props.data?.reportedAgainst?.name
                ? props.data?.reportedAgainst?.name + ' ' + props.data?.reportedAgainst?.revision
                : props.node?.field === 'reportedAgainst'
                ? props.node?.key
                : '',
    },
    {
        field: 'introducedIn',
        headerName: 'Introduced In',
        minWidth: 200,
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        cellRenderer: (params: ICellRendererParams<RollupData>) => (
            <EntityNameRenderer
                data={{
                    ...params.data?.introducedIn,
                    properties: {
                        type: params.data?.introducedIn?.type,
                        name: params.data?.introducedIn?.name,
                        id: params.data?.introducedIn?.id,
                    },
                }}
                type={params.data?.introducedIn?.type}
                value={
                    params.data?.introducedIn?.name
                        ? params.data?.introducedIn?.name + ' ' + params.data?.introducedIn?.revision
                        : params.node.field === 'introducedIn'
                        ? params.node.key
                        : ''
                }
            />
        ),
        valueGetter: (props) =>
            props.data?.introducedIn?.name
                ? props.data?.introducedIn?.name + ' ' + props.data?.introducedIn?.revision
                : props.node?.field === 'introducedIn'
                ? props.node?.key
                : '',
    },
    {
        field: 'entity.explanation',
        headerName: 'Explanation',
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        width: DEFAULT_CELL_WIDTH,
        cellRenderer: (params: ICellRendererParams) => (
            <PropertyValueRenderer
                {...params}
                data={{
                    ...params.data,
                    type: AttributeType.STRING,
                    displayName: 'Explanation',
                }}
                value={params.data?.explanation?.[0] || ''}
            />
        ),
    },
    {
        field: 'entity.lifecycle.name',
        headerName: 'Lifecycle',
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        minWidth: 100,
    },
    {
        field: 'entity.state.name',
        headerName: 'Status',
        cellRenderer: (params: ICellRendererParams<RollupData, any>) => {
            return <StatusCellRenderer {...params} data={params?.data?.entity} />;
        },
        flex: 1,
        filter: 'agTextColumnFilter',
        minWidth: 100,
        valueGetter: (params) => params?.data?.entity?.state?.name,
    },
];

const EXCLUDED_ATTRIBUTES = ['name'];

const getColDef = (attribute: Attribute) => ({
    field: `entity.${attribute.name}`,
    headerName: attribute.displayName,
    flex: 1,
    minWidth: 120,
    editable: true,
    filter: 'agTextColumnFilter',
    autoHeight: true,
    cellRenderer: PropertyValueRenderer,
    cellRendererParams: {
        schema: attribute,
    },
    columnGroupShow: 'open',
    colId: attribute.name,
});

const buildColDefs = (attributes: Record<string, Attribute>, attributeOrder: string[]) => {
    const sortedColDefs: ColDef[] = [];
    const sortedKeys = ['name'];
    attributeOrder.forEach((order) => {
        if (!order.includes(':')) {
            if (EXCLUDED_ATTRIBUTES.includes(order)) return;
            const attribute = attributes[order];
            if (attribute.visible) {
                sortedColDefs.push(getColDef(attribute));
            }
            sortedKeys.push(order);
            return;
        }
        const groupAttributes = order.split(':')[1].split(',') ?? [];

        groupAttributes.forEach((groupAttr) => {
            if (EXCLUDED_ATTRIBUTES.includes(groupAttr)) return;
            const attribute = attributes[groupAttr];
            if (attribute.visible) {
                sortedColDefs.push(getColDef(attribute));
            }
            sortedKeys.push(groupAttr);
        });
    });
    Object.values(attributes ?? {}).forEach((attribute) => {
        if (!sortedKeys.includes(attribute.name) && attribute.visible) {
            sortedColDefs.push(getColDef(attribute));
        }
    });
    return sortedColDefs;
};

const getDispositionColDefs = (schema: SchemaWithLifeCycleDetail) => {
    const colDefs = [
        {
            field: 'actions',
            headerName: 'Actions',
            minWidth: 200,
            flex: 1,
            editable: false,
            filter: 'agTextColumnFilter',
            cellRenderer: (params: ICellRendererParams<RollupData>) => <DispositionActionsRenderer params={params} />,
        },
        {
            field: 'disposition.name',
            headerName: 'Name',
            minWidth: 200,
            flex: 1,
            editable: false,
            filter: 'agTextColumnFilter',
            cellRenderer: (params: ICellRendererParams<RollupData>) => (
                <EntityNameRenderer
                    data={{
                        id: params.data?.disposition?.id,
                        properties: {
                            type: params.data?.disposition?.type,
                            name: params.data?.disposition?.name,
                        },
                    }}
                    type={params.data?.disposition?.type}
                    value={
                        params.data?.disposition?.name ??
                        (params.node.field === 'disposition.name' ? params.node.key : '')
                    }
                />
            ),
        },
        {
            field: 'disposition.state.name',
            headerName: 'Status',
            cellRenderer: (params: ICellRendererParams<RollupData, any>) => {
                return <StatusCellRenderer {...params} data={params?.data?.disposition} />;
            },
            editable: false,
            flex: 1,
            filter: 'agTextColumnFilter',
            minWidth: 100,
            valueGetter: (params) => params?.data?.disposition?.state?.name,
        },
    ];
    const visibleSortedAttributes = filterAndSortAttributes(schema.attributes, schema.entityType.attributeOrder);
    const editableColDefs =
        buildEditableColumnDefs({
            entityAttributes: visibleSortedAttributes,
            fieldPrefix: 'disposition',
            columnOptions: {
                editable: true,
                columnGroupShow: 'open',
            },
        }) ?? [];

    return [...colDefs, ...editableColDefs];
};

const prepareDispositionRequestBody = (
    editedDispositions: Record<string, Record<string, { oldValue: string; newValue: string }>>,
    bugId: string,
    entityId: string,
    entityType: string
) => {
    const propertiesBeforeParsing = editedDispositions[bugId];
    const attributes = {};
    Object.keys(propertiesBeforeParsing).forEach((property) => {
        if (propertiesBeforeParsing[property]?.newValue) {
            attributes[property.replace('disposition.', '')] = propertiesBeforeParsing[property].newValue;
        }
    });
    if (!propertiesBeforeParsing.id) {
        return {
            subParams: {
                entityType: SYSTEM_ENTITY_TYPE.SEMICONDUCTOR_DISPOSITION,
            },
            url: entityUrls.createEntity.url.replace('/entity', ''),
            body: {
                attributes,
                relations: [
                    {
                        name: SYSTEM_RELATION.SIP_DISPOSITIONED_BUG,
                        entityId: bugId,
                        entityType: propertiesBeforeParsing.type,
                        direction: RELATION_DIRECTION.OUTGOING.toUpperCase(),
                    },
                    {
                        name: SYSTEM_RELATION.SIP_DISPOSITION_DEFINED_FOR,
                        entityId: entityId,
                        entityType: entityType,
                        direction: RELATION_DIRECTION.OUTGOING.toUpperCase(),
                    },
                ],
            },
        };
    } else {
        return {
            subParams: {
                entityType: SYSTEM_ENTITY_TYPE.SEMICONDUCTOR_DISPOSITION,
                entityId: propertiesBeforeParsing.id,
            },
            method: entityUrls.updateEntity.method,
            url: entityUrls.updateEntity.url.replace('/entity', ''),
            body: {
                attributes,
            },
        };
    }
};

enum RollupView {
    DIRECT,
    INHERITED,
}

const Rollup = () => {
    const { entityType, id: entityId } = useParams();
    // Pagination
    const [isPaginationOn, setPaginationOn] = useState<boolean>(true);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [totalPages, setTotalPages] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(DEFAULT_TABLE_PAGINATION_SIZE);
    const { getMultipleSchema, schema } = useSchemaDetail();
    const [editedDispositions, setEditedDispositions] = useState<
        Record<string, Record<string, { oldValue: string; newValue: string }>>
    >({});
    const [rollupView, setRollupView] = useState<RollupView>(RollupView.INHERITED);
    const editedDispositionCount = Object.keys(editedDispositions).length;
    const editedDispositionCountRef = useRef(0);
    const { schemaTreeMap } = useSchemaTree();
    const colDefs: (ColDef | ColGroupDef)[] = useMemo(() => {
        if (
            !schema?.[SYSTEM_ENTITY_TYPE.ISSUE]?.attributes ||
            !schema?.[SYSTEM_ENTITY_TYPE.SEMICONDUCTOR_DISPOSITION]?.attributes
        )
            return [];
        return [
            {
                field: 'entity.name',
                headerName: 'Name',
                minWidth: 200,
                flex: 1,
                editable: false,
                filter: 'agTextColumnFilter',
                cellRenderer: (params: ICellRendererParams<RollupData>) => (
                    <EntityNameRenderer
                        data={{
                            id: params.data?.entity?.id,
                            properties: {
                                type: params.data?.entity?.type,
                                name: params.data?.entity?.name,
                            },
                        }}
                        type={params.data?.entity?.type}
                        value={
                            params.data?.entity?.name ?? (params.node.field === 'entity.name' ? params.node.key : '')
                        }
                    />
                ),
            },
            {
                headerName: 'Disposition',
                openByDefault: false,
                groupId: 'disposition',
                children: getDispositionColDefs(schema[SYSTEM_ENTITY_TYPE.SEMICONDUCTOR_DISPOSITION]),
            },
            {
                headerName: 'Ticket Attributes',
                openByDefault: true,
                children: [
                    ...DEFAULT_COLUMN_DEFS,
                    ...(buildColDefs(
                        schema[SYSTEM_ENTITY_TYPE.ISSUE].attributes,
                        schema[SYSTEM_ENTITY_TYPE.ISSUE].entityType.attributeOrder
                    ) ?? []),
                ],
            },
        ];
    }, [schema]);

    const enableRowGroupPanel = useRef(true);
    const gridRef = useRef<AgGridReact>(null);
    const [errorFetchingData, setErrorFetchingData] = useState(false);
    const {
        detailEntity,
        sessions: { isLoadingEntityDetail },
    } = useSelector(selectAppReducer);
    const [savingDisposition, setSavingDisposition] = useState(false);

    const handleCurrentPageChanged = useCallback(
        (value) => {
            gridRef.current?.api.paginationGoToPage(value - 1);
            setCurrentPage(value);
        },
        [setCurrentPage]
    );

    const onPageSizeChanged = useCallback((value) => {
        gridRef.current?.api.paginationSetPageSize(value);
    }, []);

    const handleOnOffPagination = useCallback(
        (value) => {
            setPaginationOn(value);
            if (value) {
                gridRef.current?.api.paginationSetPageSize(DEFAULT_TABLE_PAGINATION_SIZE);
            } else {
                gridRef.current?.api.paginationSetPageSize(gridRef.current?.api.getDisplayedRowCount() + 1);
            }
        },
        [setPaginationOn]
    );

    const onPaginationChanged = useCallback(() => {
        if (gridRef.current?.api) {
            setCurrentPage(gridRef.current.api.paginationGetCurrentPage() + 1);
            setTotalPages(gridRef.current.api.paginationGetTotalPages());
            setPageSize(gridRef.current.api.paginationGetPageSize());
        }
    }, []);

    const onFirstDataRendered = useCallback(() => {
        gridRef.current?.columnApi.autoSizeAllColumns();
    }, []);

    const toggleRowGroupPanel = useCallback(() => {
        enableRowGroupPanel.current = !enableRowGroupPanel.current;
        gridRef.current?.api.setRowGroupPanelShow(enableRowGroupPanel.current ? 'always' : 'never');
    }, []);

    const toggleColumnFilters = useCallback(() => {
        let colDefs = gridRef.current?.api.getColumnDefs() || [];
        const enabled = !Boolean(colDefs.some((colDef: any) => colDef.floatingFilter));
        colDefs.forEach((colDef: any) => {
            colDef.floatingFilter = enabled;
            if (colDef.children) {
                colDef.children.forEach((child) => (child.floatingFilter = enabled));
            }
        });
        gridRef.current?.api.setColumnDefs(colDefs);
        gridRef.current?.api.setAutoGroupColumnDef({
            ...autoGroupColumnDef,
            floatingFilter: enabled,
        });
        gridRef.current?.api.refreshHeader();
    }, []);

    const fetchRollup = useCallback(async () => {
        fetch({
            ...entityUrls.getRollup,
            params: {
                entityType,
                entityId,
                bugEntityType: SYSTEM_ENTITY_TYPE.BUG,
            },
            qs: {
                limit: 50000,
                offset: 0,
            },
        })
            .then((response) => {
                const rowsThisPage = response.data.data;
                // If disposition doesn't exist and we don't set this cell edit doesn't work
                rowsThisPage.forEach((row) => {
                    if (!row.disposition) {
                        row.disposition = {};
                    }
                });
                gridRef.current?.api?.setRowData(rowsThisPage);
            })
            .catch(() => {
                setErrorFetchingData(true);
                gridRef.current?.api?.setRowData([]);
            })
            .finally(() => {
                if (gridRef.current?.api?.getDisplayedRowCount() === 0) {
                    gridRef.current?.api?.showNoRowsOverlay();
                }
            });
    }, []);

    const onSubmitDisposition = useCallback(async () => {
        if (editedDispositionCount > 0) {
            setSavingDisposition(true);
            const batchRequestBodyData = Object.keys(editedDispositions).map((bugId) =>
                prepareDispositionRequestBody(editedDispositions, bugId, entityId, entityType)
            );
            const data = batchRequestBody(
                entityUrls.createEntity.method,
                entityUrls.createEntity.url.replace('/entity', ''),
                flatten(batchRequestBodyData)
            );
            const res = await batchRequest({
                ...entityUrls.batchRequest,
                data,
            });
            handleBatchRequestRes({
                response: res,
                statusCodeOnSuccess: [200, 201],
                onSuccess: () => {
                    setEditedDispositions({});
                    fetchRollup().then(() => {
                        setSavingDisposition(false);
                    });
                },
                successMessage: 'Successfully saved dispositions',
                failedMessage: 'Error saving some dispositions',
            });
        }
    }, [editedDispositions, entityId, entityType, fetchRollup]);

    const onDispositionCellEdit = useCallback(
        (e: CellValueChangedEvent) => {
            if (e.source === 'clearFormatting') {
                return;
            }
            const bugId = e.node.data.entity.id;
            const existingEditedDisposition = editedDispositions[bugId];
            if (existingEditedDisposition) {
                if (existingEditedDisposition?.[e.column.getColId()]?.oldValue === e.newValue) {
                    delete existingEditedDisposition?.[e.column.getColId()];
                    // id and type
                    if (
                        Object.keys(existingEditedDisposition).length === 2 &&
                        'id' in existingEditedDisposition &&
                        'type' in existingEditedDisposition
                    ) {
                        setEditedDispositions((old) => {
                            const { [bugId]: _, ...remainingDispositions } = old;
                            return remainingDispositions;
                        });
                        editedDispositionCountRef.current -= 1;
                        e.node.data.isEdited = false;
                        gridRef.current.api?.refreshCells({
                            rowNodes: [e.node],
                            columns: ['actions'],
                            force: true,
                        });
                    } else {
                        setEditedDispositions((old) => ({
                            ...old,
                            [bugId]: existingEditedDisposition,
                        }));
                    }
                } else {
                    setEditedDispositions((old) => ({
                        ...old,
                        [bugId]: {
                            ...old[bugId],
                            [e.column.getColId()]: {
                                oldValue:
                                    e.column.getColId() in existingEditedDisposition
                                        ? existingEditedDisposition[`${e.column.getColId()}`]?.oldValue
                                        : e.oldValue,
                                newValue: e.newValue,
                            },
                        },
                    }));
                }
            } else {
                editedDispositionCountRef.current += 1;
                setEditedDispositions((old) => ({
                    ...old,
                    [bugId]: {
                        [e.column.getColId()]: {
                            oldValue: e.oldValue,
                            newValue: e.newValue,
                        },
                        id: e.node.data.disposition?.id,
                        type: e.node.data.entity.type,
                    },
                }));
                e.node.data.isEdited = true;
                gridRef.current.api?.refreshCells({
                    rowNodes: [e.node],
                    columns: ['actions'],
                    force: true,
                });
            }
        },
        [editedDispositions]
    );

    useEffect(() => {
        getMultipleSchema([SYSTEM_ENTITY_TYPE.ISSUE, SYSTEM_ENTITY_TYPE.SEMICONDUCTOR_DISPOSITION]);
    }, []);

    return isLoadingEntityDetail ? (
        <LoadingOverlay />
    ) : (
        <AnimatedPage>
            <FixedContainer sx={{ backgroundColor: '#FFFFFF' }} fixedOnTablet>
                <Box
                    sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            height: '100%',
                            ...tableStyles,
                        }}
                    >
                        <ToolbarWrapper>
                            <Box
                                sx={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    width: '100%',
                                }}
                            >
                                <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                                    <ToolbarItem
                                        icon={<FilterIcon />}
                                        defaultMsg="Column Filters"
                                        onClick={toggleColumnFilters}
                                    />
                                    <ToolbarItem
                                        icon={<GroupObjectIcon />}
                                        defaultMsg="Toggle row group panel"
                                        onClick={toggleRowGroupPanel}
                                    />
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', height: '100%', gap: '4px' }}>
                                    <Select
                                        IconComponent={ExpandMoreIcon}
                                        sx={{ '&.MuiOutlinedInput-root': { height: '32px' } }}
                                        size="small"
                                        onChange={(e) => {
                                            setRollupView(e.target.value as RollupView);
                                            if (e.target.value === RollupView.DIRECT) {
                                                gridRef.current?.api?.setFilterModel({
                                                    reportedAgainst: {
                                                        filterType: 'text',
                                                        type: 'startsWith',
                                                        filter: detailEntity?.properties?.name,
                                                    },
                                                    introducedIn: {
                                                        filter: detailEntity?.properties?.name,
                                                        filterType: 'text',
                                                        type: 'startsWith',
                                                    },
                                                });
                                            } else {
                                                gridRef.current?.api.setFilterModel({
                                                    reportedAgainst: {
                                                        filterType: 'text',
                                                        type: 'startsWith',
                                                        filter: '',
                                                    },
                                                    introducedIn: {
                                                        filter: '',
                                                        filterType: 'text',
                                                        type: 'startsWith',
                                                    },
                                                });
                                            }
                                        }}
                                        value={rollupView}
                                    >
                                        <MenuItem className="menuItem" value={RollupView.DIRECT}>
                                            Show Directly Impacting Only
                                        </MenuItem>
                                        <MenuItem className="menuItem" value={RollupView.INHERITED}>
                                            Show Direct and Inherited Impact
                                        </MenuItem>
                                    </Select>
                                    {editedDispositionCount > 0 && (
                                        <Button
                                            variant="contained-blue"
                                            sx={{ height: '32px' }}
                                            onClick={onSubmitDisposition}
                                        >
                                            {savingDisposition ? (
                                                <CircularProgress sx={{ color: '#334466' }} size={14} />
                                            ) : (
                                                'Save Changes'
                                            )}
                                        </Button>
                                    )}
                                </Box>
                            </Box>
                        </ToolbarWrapper>
                        <Box
                            id="issue-rollup-table"
                            sx={{
                                height: '100%',
                                width: '100%',
                                borderRadius: 8,
                                marginBottom: '8px',
                                marginLeft: '0px',
                            }}
                            className="ag-theme-alpine"
                        >
                            <AgGridReact
                                {...GRID_OPTIONS}
                                onFirstDataRendered={onFirstDataRendered}
                                autoGroupColumnDef={autoGroupColumnDef}
                                onColumnRowGroupChanged={onFirstDataRendered}
                                ref={gridRef}
                                columnDefs={colDefs}
                                context={{
                                    setEditedDispositions: setEditedDispositions,
                                    schemaTreeMap: schemaTreeMap,
                                }}
                                onGridReady={() => fetchRollup()}
                                onPaginationChanged={onPaginationChanged}
                                noRowsOverlayComponent={NoRowsOverlay}
                                noRowsOverlayComponentParams={{
                                    error: errorFetchingData,
                                    message: errorFetchingData ? (
                                        `There was an error fetching the Rollup for ${detailEntity?.properties.name}`
                                    ) : (
                                        <Typography sx={{ mt: 2 }}>
                                            Entity <b>{detailEntity?.properties.name}</b> does not have any rolled up
                                            Bugs
                                        </Typography>
                                    ),
                                }}
                                sideBar={{
                                    toolPanels: [
                                        {
                                            id: 'columns',
                                            labelDefault: 'Columns',
                                            labelKey: 'columns',
                                            iconKey: 'columns',
                                            toolPanel: 'agColumnsToolPanel',
                                        },
                                        {
                                            id: 'filters',
                                            labelDefault: 'Filters',
                                            labelKey: 'filters',
                                            iconKey: 'filter',
                                            toolPanel: 'agFiltersToolPanel',
                                        },
                                        {
                                            id: 'saved-filters',
                                            labelDefault: 'Saved Filters',
                                            labelKey: 'saved-filters',
                                            iconKey: 'filter',
                                            toolPanel: SavedFilterSidebar,
                                            toolPanelParams: {
                                                type: 'Rollup',
                                                defaultColumnDefs: colDefs,
                                            },
                                        },
                                    ],
                                }}
                                onCellValueChanged={onDispositionCellEdit}
                            />
                        </Box>
                    </Box>
                    <TablePagination
                        totalPages={totalPages}
                        currentPage={currentPage}
                        pageSize={pageSize}
                        onCurrentPageChanged={handleCurrentPageChanged}
                        onPageSizeChanged={onPageSizeChanged}
                        onPaginationModeChange={handleOnOffPagination}
                        isPaginationOn={isPaginationOn}
                    />
                </Box>
            </FixedContainer>
        </AnimatedPage>
    );
};

export default Rollup;
