/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import get from 'lodash/get';
import isNil from 'lodash/isNil';
import InlineTextEditor from '../components/AgGrid/InlineTextEditor';
import DatePickerEditor from '../components/AgGrid/DatePickerEditor';
import * as yup from 'yup';
import { buildMinMessage, buildMaxMessage, buildRequireMessage } from './attributeFormBuilder';
import { ColDef, EditableCallback } from '@ag-grid-community/core';
import { Attribute, AttributeType } from '@tripudiotech/api';
import { formatDate, formatDateTime, PropertyValueRenderer, DEFAULT_CELL_WIDTH } from '@tripudiotech/styleguide';
import { StringArrayEditor } from '../components/AgGrid/StringArrayEditor';
import { DateArrayEditor } from '../components/AgGrid/DateArrayEditor';
import { NumericArrayEditor } from '../components/AgGrid/NumericArrayEditor';
import { CascadingListEditor } from '../components/AgGrid/CascadingListEditor';
import BooleanEditor from '../components/AgGrid/BooleanEditor';
import { DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH, DEFAULT_DATETIME_CELL_WIDTH } from '../constants/common';
import SelectEditor from '../components/AgGrid/SelectEditor';

export const decimalColumnComparator = (valueA: string, valueB: string) => {
    const parseVersionString = (version: string) => {
        const normalizedVersion = version.replaceAll(/[-_]/g, '.');
        return normalizedVersion.split('.').map((part) => parseInt(part, 10) || 0);
    };

    const aParts = parseVersionString(valueA);
    const bParts = parseVersionString(valueB);

    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
        const a = aParts[i] || 0;
        const b = bParts[i] || 0;

        if (a !== b) {
            return a - b;
        }
    }

    return 0;
};

const buildValidationSchema = (attribute) => {
    const { type, isNullable, constraint, displayName } = attribute;
    const enumRange = get(constraint, 'enumRange', []);
    const pattern = get(constraint, 'pattern', null);
    const minLength = get(constraint, 'minLength', null);
    const maxLength = get(constraint, 'maxLength', null);
    switch (type) {
        case AttributeType.LONG:
        case AttributeType.FLOAT:
        case AttributeType.INTEGER: {
            let yupValidator: any = yup.number().typeError(`${displayName} must be a number`);

            if (minLength) {
                yupValidator = yupValidator.min(Number(minLength), buildMinMessage(displayName, minLength));
            }
            if (maxLength) {
                yupValidator = yupValidator.max(Number(maxLength), buildMaxMessage(displayName, maxLength));
            }

            if (isNil(isNullable) || Boolean(isNullable)) {
                yupValidator = yupValidator.nullable();
            } else {
                yupValidator = yupValidator.required(buildRequireMessage(displayName));
            }

            return yupValidator;
        }

        case AttributeType.STRING: {
            let yupValidator: any = yup.string();

            if (isNil(isNullable) || Boolean(isNullable)) {
                yupValidator = yupValidator.nullable();
            } else {
                yupValidator = yupValidator.required(buildRequireMessage(displayName));
            }

            if (enumRange.length > 0) {
                yupValidator = yupValidator.oneOf(enumRange);

                return yupValidator;
            }
            if (minLength) {
                yupValidator = yupValidator.min(Number(minLength), buildMinMessage(displayName, minLength));
            }
            if (maxLength) {
                yupValidator = yupValidator.max(Number(maxLength), buildMaxMessage(displayName, maxLength));
            }

            if (pattern) {
                yupValidator = yupValidator.matches(new RegExp(pattern, 'g'), `Value does not match ${pattern}`);
            }

            return yupValidator;
        }

        case AttributeType.BOOLEAN: {
            let yupValidator: any = yup.boolean();

            if (isNil(isNullable) || Boolean(isNullable)) {
                yupValidator = yupValidator.nullable();
            } else {
                yupValidator = yupValidator.required(buildRequireMessage(displayName));
            }

            return yupValidator;
        }

        case AttributeType.DATE: {
            let yupValidator: any = yup.string().nullable();

            if (!isNil(isNullable) && !Boolean(isNullable)) {
                yupValidator = yupValidator.required(buildRequireMessage(displayName));
            }

            return yupValidator;
        }
        case AttributeType.DATE_TIME: {
            let yupValidator: any = yup.string().nullable();

            if (!isNil(isNullable) && !Boolean(isNullable)) {
                yupValidator = yupValidator.required(buildRequireMessage(displayName));
            }

            return yupValidator;
        }
        default:
            return yup.string();
    }
};

const defaultColumnOptions: ColDef = {
    minWidth: 100,
    editable: true,
};

const SUPPORTED_DYNAMIC_ATTRIBUTES = [
    AttributeType.INTEGER,
    AttributeType.LONG,
    AttributeType.FLOAT,
    AttributeType.STRING,
    AttributeType.DATE_TIME,
    AttributeType.DATE,
    AttributeType.STRING_ARRAY,
    AttributeType.DATE_ARRAY,
    AttributeType.INTEGER_ARRAY,
    AttributeType.DATE_TIME_ARRAY,
    AttributeType.CASCADING_LIST,
    AttributeType.BOOLEAN,
];

export const buildEditableColumnDefs = ({
    entityAttributes = [],
    fieldPrefix,
    shouldEnableEdit = undefined,
    columnOptions = defaultColumnOptions,
}: {
    entityAttributes: Attribute[];
    fieldPrefix?: string;
    shouldEnableEdit?: EditableCallback;
    columnOptions?: ColDef;
}): ColDef[] => {
    return entityAttributes
        .filter((attribute) => SUPPORTED_DYNAMIC_ATTRIBUTES.includes(attribute.type))
        .map((attribute) => {
            const { type, displayName, name, description } = attribute;
            const validationSchema: any = buildValidationSchema(attribute);
            switch (type) {
                case AttributeType.INTEGER:
                case AttributeType.LONG:
                case AttributeType.FLOAT:
                    return {
                        ...columnOptions,
                        field: fieldPrefix ? fieldPrefix + '.' + name : name,
                        headerName: displayName,
                        cellEditor: InlineTextEditor,
                        minWidth: 130,
                        enableValue: true,
                        flex: 1,
                        editable: (params) => {
                            if (shouldEnableEdit) {
                                return shouldEnableEdit(params) && attribute.mutable;
                            }
                            return columnOptions.editable as boolean;
                        },
                        cellEditorParams: {
                            validateConstraint: async (value) => {
                                return validationSchema.validate(value);
                            },
                            helperText: description,
                        },
                        filter: 'agNumberColumnFilter',
                    };
                case AttributeType.STRING:
                    const enumRange: string[] = get(attribute, ['constraint', 'enumRange'], []);
                    if (enumRange.length > 0) {
                        return {
                            ...columnOptions,
                            field: fieldPrefix ? fieldPrefix + '.' + name : name,
                            headerName: displayName,
                            cellEditor: SelectEditor,
                            flex: 1,
                            minWidth: DEFAULT_CELL_WIDTH,
                            editable: (params) => {
                                if (shouldEnableEdit) {
                                    return shouldEnableEdit(params) && attribute.mutable;
                                }
                                return (columnOptions.editable as boolean) && attribute.mutable;
                            },
                            cellEditorParams: {
                                options: enumRange.map((val) => ({ label: val, value: val })),
                            },
                            filter: 'agTextColumnFilter',
                        };
                    }
                    return {
                        ...columnOptions,
                        field: fieldPrefix ? fieldPrefix + '.' + name : name,
                        headerName: displayName,
                        cellEditor: InlineTextEditor,
                        flex: 1,
                        editable: (params) => {
                            if (shouldEnableEdit) {
                                return shouldEnableEdit(params) && attribute.mutable;
                            }
                            return (columnOptions.editable as boolean) && attribute.mutable;
                        },
                        cellRenderer: PropertyValueRenderer,
                        cellRendererParams: {
                            schema: attribute,
                        },
                        cellEditorParams: {
                            validateConstraint: async (value) => {
                                return validationSchema.validate(value);
                            },
                            helperText: description,
                        },
                        minWidth: DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH,
                        filter: 'agSetColumnFilter',
                    };
                case AttributeType.DATE:
                    return {
                        ...columnOptions,
                        field: fieldPrefix ? fieldPrefix + '.' + name : name,
                        headerName: displayName,
                        cellEditor: DatePickerEditor,
                        flex: 1,
                        minWidth: 200,
                        editable: (params) => {
                            if (shouldEnableEdit) {
                                return shouldEnableEdit(params) && attribute.mutable;
                            }
                            return (columnOptions.editable as boolean) && attribute.mutable;
                        },
                        valueFormatter: (params) => {
                            return formatDate(params.value);
                        },
                        cellEditorParams: {
                            validateConstraint: async (value) => {
                                return validationSchema.validate(value);
                            },
                            helperText: description,
                            type: AttributeType.DATE,
                        },
                        filter: 'agTextColumnFilter',
                    };
                case AttributeType.DATE_TIME:
                    return {
                        ...columnOptions,
                        field: fieldPrefix ? fieldPrefix + '.' + name : name,
                        headerName: displayName,
                        cellEditor: DatePickerEditor,
                        flex: 1,
                        minWidth: DEFAULT_DATETIME_CELL_WIDTH,
                        editable: (params) => {
                            if (shouldEnableEdit) {
                                return shouldEnableEdit(params) && attribute.mutable;
                            }
                            return (columnOptions.editable as boolean) && attribute.mutable;
                        },
                        valueFormatter: (params) => {
                            return formatDateTime(params.value);
                        },
                        cellEditorParams: {
                            validateConstraint: async (value) => {
                                return validationSchema.validate(value);
                            },
                            helperText: description,
                            type: AttributeType.DATE_TIME,
                        },
                    };
                case AttributeType.STRING_ARRAY: {
                    return {
                        ...columnOptions,
                        field: fieldPrefix ? fieldPrefix + '.' + name : name,
                        headerName: displayName,
                        cellEditor: StringArrayEditor,
                        flex: 1,
                        minWidth: DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH,
                        editable: (params) => {
                            if (shouldEnableEdit) {
                                return shouldEnableEdit(params) && attribute.mutable;
                            }
                            return (columnOptions.editable as boolean) && attribute.mutable;
                        },
                        cellRenderer: PropertyValueRenderer,
                        cellRendererParams: {
                            schema: attribute,
                        },
                        cellEditorParams: {
                            attribute: attribute,
                        },
                    };
                }
                case AttributeType.INTEGER_ARRAY:
                case AttributeType.FLOAT_ARRAY: {
                    return {
                        ...columnOptions,
                        field: fieldPrefix ? fieldPrefix + '.' + name : name,
                        headerName: displayName,
                        cellEditor: NumericArrayEditor,
                        flex: 1,
                        minWidth: DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH,
                        suppressKeyboardEvent: (params) => params.event.key === 'Enter',
                        editable: (params) => {
                            if (shouldEnableEdit) {
                                return shouldEnableEdit(params) && attribute.mutable;
                            }
                            return (columnOptions.editable as boolean) && attribute.mutable;
                        },
                        cellRenderer: PropertyValueRenderer,
                        cellRendererParams: {
                            schema: attribute,
                        },
                        cellEditorParams: {
                            attribute: attribute,
                        },
                    };
                }
                case AttributeType.DATE_ARRAY:
                case AttributeType.DATE_TIME_ARRAY: {
                    return {
                        ...columnOptions,
                        field: fieldPrefix ? fieldPrefix + '.' + name : name,
                        headerName: displayName,
                        cellEditor: DateArrayEditor,
                        flex: 1,
                        minWidth: DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH,
                        editable: (params) => {
                            if (shouldEnableEdit) {
                                return shouldEnableEdit(params) && attribute.mutable;
                            }
                            return (columnOptions.editable as boolean) && attribute.mutable;
                        },
                        cellRenderer: PropertyValueRenderer,
                        cellRendererParams: {
                            schema: attribute,
                        },
                        cellEditorParams: {
                            attribute: attribute,
                        },
                    };
                }
                case AttributeType.CASCADING_LIST: {
                    return {
                        ...columnOptions,
                        field: fieldPrefix ? fieldPrefix + '.' + name : name,
                        headerName: displayName,
                        cellEditor: CascadingListEditor,
                        flex: 1,
                        minWidth: DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH,
                        editable: (params) => {
                            if (shouldEnableEdit) {
                                return shouldEnableEdit(params) && attribute.mutable;
                            }
                            return (columnOptions.editable as boolean) && attribute.mutable;
                        },
                        cellRenderer: PropertyValueRenderer,
                        cellRendererParams: {
                            schema: attribute,
                        },
                        cellEditorParams: {
                            attribute: attribute,
                        },
                    };
                }
                case AttributeType.BOOLEAN: {
                    return {
                        ...columnOptions,
                        field: fieldPrefix ? fieldPrefix + '.' + name : name,
                        headerName: displayName,
                        cellEditor: BooleanEditor,
                        flex: 1,
                        minWidth: DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH,
                        editable: (params) => {
                            if (shouldEnableEdit) {
                                return shouldEnableEdit(params) && attribute.mutable;
                            }
                            return (columnOptions.editable as boolean) && attribute.mutable;
                        },
                        cellRenderer: PropertyValueRenderer,
                        cellRendererParams: {
                            schema: attribute,
                        },
                        cellEditorParams: {
                            attribute: attribute,
                        },
                    };
                }
            }
        });
};
