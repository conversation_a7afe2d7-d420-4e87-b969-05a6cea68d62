/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import get from 'lodash/get';
import { AlternateRenderer, NoRowsOverlay } from '../components/Relations/Bom/BomRenderer';
import {
    DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH,
    DEFAULT_DATETIME_CELL_WIDTH,
    DEFAULT_TABLE_PAGINATION_SIZE,
    EXCLUDED_BOM_FIELDS,
} from '../constants/common';
import { buildEditableColumnDefs } from './columnDefsBuilder';
import {
    LoadingOverlay,
    tableIcons,
    EntityNameRenderer,
    MainTooltip,
    PropertyValueRenderer,
    DataTypeProperty,
    getAgGridColumnFilterType,
    DEFAULT_CELL_WIDTH,
} from '@tripudiotech/styleguide';
import TreeRenderer from '../components/Relations/Bom/TreeRenderer';
import { RowNode } from 'ag-grid-community';
import SavedFilterSidebar from '../components/SavedFilter/SavedFilter';
import { ColDef, GridOptions, ColGroupDef, ICellRendererParams } from '@ag-grid-community/core';
import { Attribute, AttributeType, EntityDetail, Schema } from '@tripudiotech/api';
import { Fragment } from 'react';
import StatusCellRenderer from '../components/AttributeDetail/StatusCellRenderer';

export const buildDynamicBomAttributes = (bomSchema: Schema): Attribute[] => {
    return Object.values(get(bomSchema, 'attributes', {}))
        .filter(
            (attribute: any) =>
                attribute.visible && !attribute.richText && !EXCLUDED_BOM_FIELDS.includes(attribute.name)
        )
        .sort(
            (a: any, b: any) =>
                bomSchema.entityType.attributeOrder.indexOf(a.name) -
                bomSchema.entityType.attributeOrder.indexOf(b.name)
        );
};

export const buildDynamicAttributes = (bomSchema: Schema): Attribute[] => {
    return Object.values(get(bomSchema, 'attributes', {}))
        .filter((attribute) => attribute.visible && attribute.name !== 'name')
        .sort(
            (a: any, b: any) =>
                bomSchema.entityType.attributeOrder.indexOf(a.name) -
                bomSchema.entityType.attributeOrder.indexOf(b.name)
        );
};

export const getExcelStyles = () => {
    const MAX_LEVELS_SUPPORT = 200;
    let styles = [];
    for (let i = 0; i <= MAX_LEVELS_SUPPORT; i++) {
        styles.push({
            id: `ag-cell-wrapper hyperlink indent-${i}`,
            alignment: {
                indent: i - 1,
            },
            // note, dataType: 'string' required to ensure that numeric values aren't right-aligned
            dataType: 'String',
        });
    }
    return [
        ...styles,
        {
            id: 'hyperlink',
            font: {
                color: '#3246D2',
            },
        },
    ];
};

export const handleCollapse = (gridRef) => {
    gridRef.current.api.getSelectedNodes().forEach((selectedNode) => {
        const { allLeafChildren } = selectedNode;
        selectedNode.setExpanded(false);
        allLeafChildren.forEach((child) => {
            child.setExpanded(false);
        });
    });
    return;
};

const rowGroupCallback = (params) => {
    return params.node.data.component.name;
};

export const exportGridExcel = (gridRef) => {
    gridRef.current.api.exportDataAsExcel({
        processRowGroupCallback: rowGroupCallback,
    });
};

export const onPageSizeChanged = (gridRef, value) => {
    gridRef.current.api.paginationSetPageSize(value);
};

const RevisionValueRenderer = (props) => {
    return props.value ? (
        <EntityNameRenderer {...props} value={props.value} data={get(props.data, props.path, null)} />
    ) : null;
};

export const isGroupOpenByDefault = (params) => params.level === 0;

const getContextMenuItems = () => {
    return ['autoSizeAll', 'expandAll', 'contractAll', 'separator', 'copy', 'export', 'chartRange'];
};

const getDataPath = (data) => data.path;

const getRowId = (params) => params.data.rowId;

const getIndentClass = (params) => {
    let indent = 0;
    let node = params.node;
    while (node && node.parent) {
        indent++;
        node = node.parent;
    }
    return 'ag-cell-wrapper hyperlink indent-' + indent;
};

export const autoGroupColumnDef: ColDef = {
    field: 'component.name',
    headerName: 'Name',
    cellClass: getIndentClass,
    flex: 2,
    filter: 'agTextColumnFilter',
    minWidth: 250,
    pinned: 'left',
    cellRendererParams: {
        checkbox: true,
        innerRenderer: (params: ICellRendererParams) => (
            <TreeRenderer
                {...params}
                innerRendererData={{
                    ...params.data.component,
                    properties: {
                        type: params.data?.component?.type,
                        name: params.data?.component?.name,
                    },
                }}
                value={get(params?.data, 'component.name', '')}
                typePath={'component.type'}
                hasThumbnailPath={'component.hasThumbnail'}
                idPath={'component.id'}
                isLink={false}
            />
        ),
    },
    chartDataType: 'category',
};

const REV_TO_REV_SPECIFIC_COL_DEFS = [
    {
        field: 'component.partSource',
        headerName: 'Part / Data Source',
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        cellRenderer: (params: ICellRendererParams) => (
            <PropertyValueRenderer
                {...params}
                data={{
                    ...params.data,
                    type: AttributeType.STRING,
                    displayName: 'Part/Data Source',
                }}
                value={params.data?.component?.partSource}
            />
        ),
        columnGroupShow: 'open',
    },
    {
        field: 'component.secureStatus',
        headerName: 'Secure Status',
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        cellRenderer: (params: ICellRendererParams) => (
            <PropertyValueRenderer
                {...params}
                data={{
                    ...params.data,
                    type: AttributeType.STRING,
                    displayName: 'Secure Status',
                }}
                value={params.data?.component?.secureStatus}
            />
        ),
        columnGroupShow: 'open',
    },
    {
        field: 'component.versionNotes',
        headerName: 'Version Notes',
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        cellRenderer: (params: ICellRendererParams) => (
            <PropertyValueRenderer
                {...params}
                data={{
                    ...params.data,
                    type: AttributeType.STRING,
                    displayName: 'Version Notes',
                }}
                value={params.data?.component?.versionNotes}
            />
        ),
        columnGroupShow: 'open',
    },
];

const REV_TO_MASTER_SPECIFIC_COL_DEFS = [
    {
        field: 'component.manufacturers',
        headerName: 'Manufacturers',
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        valueGetter: (colDef) =>
            colDef.data?.component?.manufacturers
                ?.map((manufacturers) => manufacturers?.manufacturer?.name)
                ?.join(', '),
        cellRenderer: (props) => {
            return (
                <MainTooltip title={props.value}>
                    <span>
                        {props.value?.length > 10
                            ? props.value?.substring(0, 10) + '...'
                            : props.value?.substring(0, 10)}
                    </span>
                </MainTooltip>
            );
        },
        columnGroupShow: 'open',
    },
    {
        field: 'component.cadModel.name',
        headerName: 'Cad Model',
        flex: 1,
        editable: false,
        filter: 'agTextColumnFilter',
        cellRenderer: (params) => <EntityNameRenderer {...params} data={params?.data?.component?.cadModel} />,
        columnGroupShow: 'open',
    },
];

const buildEntityColDefs = (isRevToRev: boolean) => {
    return {
        headerName: 'Entity Attributes',
        marryChildren: true,
        openByDefault: true,
        children: [
            {
                field: 'component.type',
                headerName: 'Type',
                flex: 1,
                cellStyle: {},
                editable: false,
                filter: 'agSetColumnFilter',
                valueGetter: (props) => {
                    if (props.context.schemaTreeMap) {
                        return (
                            props.context.schemaTreeMap?.[props.data?.component?.type]?.displayName ||
                            props.data?.component?.type
                        );
                    }
                    return props.data?.component?.type;
                },
            },
            {
                field: 'component.revision',
                headerName: isRevToRev ? 'Revision' : 'Latest Revision',
                flex: 1,
                editable: false,
                filter: 'agTextColumnFilter',
                chartDataType: 'category',
                cellRenderer: RevisionValueRenderer,
                cellRendererParams: {
                    path: 'component',
                },
                columnGroupShow: 'open',
            },
            {
                field: 'component.state.name',
                headerName: 'Status',
                cellRenderer: (props: ICellRendererParams) => <StatusCellRenderer data={props.data?.component} />,
                flex: 1,
                minWidth: 150,
            },
            {
                field: 'component.title',
                headerName: 'Title',
                flex: 1,
                editable: false,
                filter: 'agTextColumnFilter',
                cellRenderer: (params: ICellRendererParams) => (
                    <PropertyValueRenderer
                        {...params}
                        data={{
                            ...params.data,
                            type: AttributeType.STRING,
                            displayName: 'Title',
                        }}
                        value={params.data?.component?.title}
                    />
                ),
                columnGroupShow: 'open',
            },
            {
                field: 'component.description',
                headerName: 'Description',
                flex: 1,
                editable: false,
                filter: 'agTextColumnFilter',
                cellRenderer: (params: ICellRendererParams) => (
                    <PropertyValueRenderer
                        {...params}
                        data={{
                            ...params.data,
                            type: AttributeType.STRING,
                            displayName: 'Description',
                        }}
                        value={params.data?.component?.description}
                    />
                ),
                columnGroupShow: 'open',
            },
            {
                field: 'component.latestReleased.revision',
                headerName: 'Latest Revision',
                flex: 1,
                editable: false,
                filter: 'agTextColumnFilter',
                cellRenderer: RevisionValueRenderer,
                cellRendererParams: {
                    path: 'component.latestReleased',
                },
                columnGroupShow: 'open',
            },
            ...(isRevToRev ? REV_TO_REV_SPECIFIC_COL_DEFS : REV_TO_MASTER_SPECIFIC_COL_DEFS),
        ],
    };
};

export const buildGridOptions = (
    detailEntity: EntityDetail,
    bomSchema: Schema,
    options,
    isRevToRev = false
): GridOptions => {
    if (!detailEntity || !bomSchema) {
        return {};
    }
    const { onCAClick, onGAClick, ...others } = options;
    const dynamicBomAttributes = buildDynamicBomAttributes(bomSchema);
    const dynamicColumnDefs = buildEditableColumnDefs({
        entityAttributes: dynamicBomAttributes,
        fieldPrefix: 'properties',
        shouldEnableEdit: (params) => {
            return params.node.level !== 0;
        },
    });
    const dynamicEntityColDefs = buildEntityColDefs(isRevToRev);

    const columnDefs: (ColDef | ColGroupDef)[] = [
        dynamicEntityColDefs,
        {
            field: 'component.owner',
            headerName: 'Owner(s)',
            flex: 1,
            editable: false,
            filter: 'agTextColumnFilter',
            cellRenderer: (params) => {
                const owners = params?.data?.component?.owner;
                return (
                    <span
                        style={{
                            display: 'flex',
                            textOverflow: 'clip',
                            overflow: 'hidden',
                            flexWrap: 'wrap',
                            gap: '4px',
                        }}
                    >
                        {owners?.map((item, index) => (
                            <Fragment key={item}>
                                <DataTypeProperty value={item?.id} dataType={item?.type} />
                                {index < owners.length - 1 && ', '}
                            </Fragment>
                        ))}
                    </span>
                );
            },
        },
        {
            headerName: (bomSchema?.entityType?.displayName ?? 'Decomposition') + ' Attributes',
            children: dynamicColumnDefs,
            marryChildren: true,
            openByDefault: true,
        },
        ...(isRevToRev
            ? []
            : [
                  {
                      field: 'globalAlternates',
                      headerName: 'Global Alternate',
                      enableValue: true,
                      minWidth: 190,
                      flex: 1,
                      cellRenderer: AlternateRenderer,
                      editable: false,
                      filter: 'agNumberColumnFilter',
                      valueGetter: (params) => {
                          return get(params, ['data', 'component', 'alternates', 'length'], 0);
                      },
                      cellRendererParams: {
                          onClick: onGAClick,
                          hideRoot: true,
                      },
                  },
                  {
                      field: 'contextualAlternates',
                      headerName: 'Contextual Alternate',
                      enableValue: true,
                      minWidth: DEFAULT_CELL_WIDTH,
                      cellRenderer: AlternateRenderer,
                      flex: 1,
                      filter: 'agNumberColumnFilter',
                      valueGetter: (params) => {
                          return get(params, ['data', 'alternates', 'length'], 0);
                      },
                      cellRendererParams: {
                          onClick: onCAClick,
                          hideRoot: true,
                      },
                      editable: false,
                  },
              ]),
    ];
    return {
        headerHeight: 34,
        enableGroupEdit: true,
        enableRangeSelection: true,
        enableCharts: true,
        getContextMenuItems,
        columnDefs,
        defaultColDef: {
            sortable: true,
            resizable: true,
            filter: true,
            flex: 1,
            floatingFilter: false,
            enableRowGroup: false,
            enablePivot: false,
            autoHeight: true,
            wrapText: true,
            minWidth: DEFAULT_CELL_WIDTH,
        },
        isGroupOpenByDefault,
        undoRedoCellEditing: true,
        excelStyles: getExcelStyles(),
        noRowsOverlayComponent: NoRowsOverlay,
        noRowsOverlayComponentParams: {
            componentName: detailEntity?.properties?.name,
        },
        getDataPath,
        rowModelType: 'clientSide',
        getRowId,
        groupDisplayType: 'singleColumn',
        sideBar: {
            toolPanels: [
                {
                    id: 'columns',
                    labelDefault: 'Columns',
                    labelKey: 'columns',
                    iconKey: 'columns',
                    toolPanel: 'agColumnsToolPanel',
                    toolPanelParams: {
                        suppressPivots: true,
                        suppressPivotMode: true,
                        suppressRowGroups: true,
                    },
                },
                {
                    id: 'filters',
                    labelDefault: 'Filters',
                    labelKey: 'filters',
                    iconKey: 'filter',
                    toolPanel: 'agFiltersToolPanel',
                },
                {
                    id: 'saved-filters',
                    labelDefault: 'Saved Filters',
                    labelKey: 'saved-filters',
                    iconKey: 'filter',
                    toolPanel: SavedFilterSidebar,
                    toolPanelParams: {
                        type: bomSchema?.entityType?.name,
                        defaultColumnDefs: [...columnDefs],
                        dropZoneId: 'bom-table',
                    },
                },
            ],
        },
        treeData: true,
        animateRows: true,
        rowSelection: 'multiple',
        stopEditingWhenCellsLoseFocus: true,
        purgeClosedRowNodes: true,
        loadingOverlayComponent: LoadingOverlay,
        suppressPaginationPanel: true,
        pagination: true,
        defaultExcelExportParams: {
            sheetName: 'Results',
            fileName: `${detailEntity.properties.name} - Bill of Materials`,
            autoConvertFormulas: true,
            processCellCallback: (params) => {
                const {
                    component: { type, id },
                } = params.node.data;
                const field = params.column.getColDef().field;

                if (field === 'component.name')
                    return `=HYPERLINK("${window.location.origin}/detail/${type}/${id}/properties", "${params.value}")`;
                else return params.value;
            },
        },
        paginationPageSize: DEFAULT_TABLE_PAGINATION_SIZE,
        paginateChildRows: true,
        rowHeight: 34,
        floatingFiltersHeight: 34,
        getRowStyle: (params) => {
            const { isRecursiveParent, isRecursive } = get(params, 'node.data.uiStates', {});
            if (Boolean(isRecursive || isRecursiveParent)) {
                return {
                    background: '#FFF7E6',
                };
            }
            return {
                background: '#FFFFFF',
            };
        },
        icons: tableIcons,
        autoGroupColumnDef,
        showOpenedGroup: true,

        ...others,
    };
};

export const handleExpand = (gridRef, level) => {
    gridRef.current.api.getSelectedNodes().forEach((selectedNode) => {
        const { allLeafChildren } = selectedNode;
        allLeafChildren.forEach((child) => {
            // level = - 1 : Expand all children
            if (level === -1) {
                child.setExpanded(true);
            } else if (Number(child.level) < Number(selectedNode.level) + Number(level)) {
                // Expand to specific levels
                child.setExpanded(true);
            }
        });
    });
};

export const onSelectRecurringComponents = (gridRef) => {
    gridRef.current.api.deselectAll();
    gridRef.current.api.forEachNode((node) => {
        if (node.data.uiStates.isRecursive) {
            node.setSelected(true);
            expandToParent(node);
        }
    });
};

export const expandToParent = (node: RowNode) => {
    let parent = node;
    while (parent.level >= 0) {
        parent.setExpanded(true);
        parent = parent.parent;
    }
};

export const onDeselect = (gridRef) => {
    gridRef.current.api.deselectAll();
};

export const toggleColumnFilters = (gridRef) => {
    let colDefs = gridRef.current.api.getColumnDefs();
    const enabled = !Boolean(colDefs.some((colDef: any) => colDef.floatingFilter));

    colDefs.forEach((colDef: any) => {
        colDef.floatingFilter = enabled;
    });
    gridRef.current.api.setColumnDefs(colDefs);
    gridRef.current.api.setAutoGroupColumnDef({
        ...autoGroupColumnDef,
        floatingFilter: enabled,
    } as any);
    gridRef.current.api.refreshHeader();
};
