/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { capitalize } from '../utils/helper';

import { CONTENT_ROLE } from '@tripudiotech/api';
export const BOM_ENTITY_TYPE = 'BillOfMaterial';
export const RELATION = { HAS_ASSEMBLY: 'HAS_ASSEMBLY', HAS_COMPONENT: 'HAS_COMPONENT' };

export const BOM_TOOLBAR = {
    ADD: 'ADD',
    REMOVE: 'REMOVE',
    ADD_CONTEXTUAL_ALTERNATE: 'ADD CONTEXTUAL ALTERNATE',
    SWAP: 'SWAP',
    REPLACE: 'REPLACE',
    DOWNLOAD: 'DOWNLOAD',
    FLOATING_FILTERS: 'FLOATING_FILTERS',
    COLLAPSE: 'COLLAPSE',
    EXPAND: 'EXPAND',
    REVERT: 'REVERT',
} as const;

export const BOM_ACTIONS = {
    REMOVE_BOM: 'REMOVE_BOM',
} as const;

export const ACCESS = {
    REVOKE_ACCESS: 'REVOKE_ACCESS',
    GRANT_ACCESS: 'GRANT_ACCESS',
} as const;

export const ENTITY_LOCKED_MESSAGE = 'This entity is locked';

export const CONTENT_ROLE_OPTIONS = Object.values(CONTENT_ROLE).map((role: string) => ({
    label: capitalize(role),
    value: role,
}));

export const AGENT_TYPE_OPTIONS = [
    {
        label: 'Person',
        value: 'Person',
    },
    {
        label: 'External Company',
        value: 'ExternalCompany',
    },
    {
        label: 'Internal Company',
        value: 'InternalCompany',
    },
    {
        label: 'Team',
        value: 'Team',
    },
    {
        label: 'Department',
        value: 'Department',
    },
] as const;

export const AGENT_TYPE_MAP = {
    Person: 'Person',
    ExternalCompany: 'External Company',
    InternalCompany: 'Internal Company',
    Team: 'Team',
    Department: 'Department',
} as const;

export const routerMappedToName = {
    bom: 'BOM',
    properties: 'Properties',
    relations: 'Relations',
    history: 'History',
    lifecycles: 'Lifecycles',
    accesses: 'Accesses',
    revision: 'Revision Summary',
    'global-replacement': 'Global Replacement',
    'change-mangement': 'Change Mangement',
    'process-view': 'Process View',
    'where-used': 'Where Used',
} as const;

export const EXCLUDED_BOM_FIELDS = ['description', 'type', 'name', 'title'];

export const perPages = [20, 50, 100];

export const initialSearchStr = 2;

export const ALTERNATE_TYPE = {
    CONTEXTUAL: 'contextual',
    GLOBAL: 'global',
};

export enum EXPAND_MODE {
    ALL,
    ONE,
}

export enum COLLAPSE_MODE {
    ALL,
    ONE,
}

export enum BOM_VIEW_OPTIONS {
    LATEST_OFFICIAL = 'LATEST_OFFICIAL',
    LATEST_REVISION = 'LATEST_REVISION',
}

export enum BOM_LEVEL_OPTIONS {
    SINGLE = 1,
    MULTIPLE = -1,
}

export enum WHERE_USED_LEVEL_OPTIONS {
    MULTIPLE = 'MULTIPLE',
    LAST_LEVEL_ONLY = 'LAST_LEVEL_ONLY',
}

export const DEFAULT_TABLE_PAGINATION_SIZE = 100;

export const StatusMapping = {
    start: {
        variant: 'status-initial',
    },
    inProgress: {
        variant: 'status-info',
    },
    released: {
        variant: 'status-success',
    },
    obsoleted: {
        variant: 'status-secondary',
    },
    superseded: {
        variant: 'status-warning',
    },
} as const;

export enum EVENT_TYPE {
    ENTITY_CREATED = 'ENTITY_CREATED',
    ENTITY_UPDATED = 'ENTITY_UPDATED',
    ENTITY_DELETED = 'ENTITY_DELETED',
    ENTITY_REVISED = 'ENTITY_REVISED',
    ENTITY_PROMOTED = 'ENTITY_PROMOTED',
    ENTITY_DEMOTED = 'ENTITY_DEMOTED',
    ENTITY_LOCKED = 'ENTITY_LOCKED',
    ENTITY_UNLOCKED = 'ENTITY_UNLOCKED',
    TRACKING_SERVICE_CREATED = 'TRACKING_SERVICE_CREATED',
    ENTITY_ACCESS_GRANTED = 'ENTITY_ACCESS_GRANTED',
    ENTITY_ACCESS_REVOKED = 'ENTITY_ACCESS_REVOKED',
    BOM_REPLACED = 'BOM_REPLACED',
    BOM_SWAPPED = 'BOM_SWAPPED',
    POST_CONNECT = 'POST_CONNECT',
    POST_DISCONNECT = 'POST_DISCONNECT',
}

export const LIFECYCLE_RULE_EVENT_TYPES = {
    ENTITY_PROMOTING: 'ENTITY_PROMOTING',
    ENTITY_DEMOTING: 'ENTITY_DEMOTING',
};

export const ACTIVITY_EVENT_TYPES = [
    EVENT_TYPE.ENTITY_CREATED,
    EVENT_TYPE.ENTITY_UPDATED,
    EVENT_TYPE.ENTITY_DELETED,
    EVENT_TYPE.ENTITY_REVISED,
    EVENT_TYPE.ENTITY_LOCKED,
    EVENT_TYPE.ENTITY_UNLOCKED,
];

export const CONTEXTUAL_ALTERNATES_PARAM = 'contextualAlternatesOf';
export const GLOBAL_ALTERNATES_PARAM = 'globalAlternatesOf';

export const DEFAULT_PAGING_LIMIT = 100;

export const UI_CONFIGURATION_STORAGE_KEY = 'UIConfigurationStorage';
export const CASCADING_LIST_SEPARATOR = '|';
export const DEFAULT_DATE_AND_ARRAYS_CELL_WIDTH = 200;
export const DEFAULT_DATETIME_CELL_WIDTH = 330;
