/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    fetch as apiFetch,
    entityUrls,
    ruleUrls,
    changeUrls,
    classificationUrls,
    schemaUrls,
    CONTENT_ROLE,
    batchRequestBody,
    batchRequest as globalBatchRequest,
    SYSTEM_RELATION,
    assetServiceUrl,
    DEFAULT_CLIENT_SIDE_LIMIT,
    RELATION_DIRECTION,
    SYSTEM_ENTITY_TYPE,
    trackingService,
    type BatchRequestData,
    type Method,
    BatchRequestResponse,
    type DigitalThreadSchema,
    AxiosResponse,
    EntityDetail,
    reportingUrls,
    ReportSchedule,
} from '@tripudiotech/api';
import { commonMessages, notifySuccess, notifyError } from '@tripudiotech/styleguide';
import {
    BOM_VIEW_OPTIONS,
    EVENT_TYPE,
    LIFECYCLE_RULE_EVENT_TYPES,
    WHERE_USED_LEVEL_OPTIONS,
} from '../constants/common';
import { BOM, AffectedItemRes, ENTITY_MODEL, WhereUsed, Rule } from '../models/models';
import {
    aggregateBomTree,
    aggregateWhereUsedTree,
    flattenTreeNodes,
    isChangeOrder,
    isChangeRequest,
    isEntityDocumentType,
    isImpactAnalysis,
    isIssueEntity,
    isOutgoingRelation,
    isPlannedChange,
    isFile,
    getBomType,
    isCadModel,
    doesEntityTypeHaveMasterRelation,
    isReport,
    isBugEntity,
    isIpChangeRequestEntity,
} from '../utils/helper';
import groupBy from 'lodash/groupBy';
import orderBy from 'lodash/orderBy';
import uniqBy from 'lodash/uniqBy';
import get from 'lodash/get';
import flattenDeep from 'lodash/flattenDeep';
import isEmpty from 'lodash/isEmpty';
import { store } from '../store';
import actionTypes from './actionTypes';
import { extractFromState, extractToState } from '../utils/lifecycle';

export const batchRequest = async (data: BatchRequestData[], method?: Method, url?: string) => {
    try {
        const body = batchRequestBody(method, url, data);
        const res = await apiFetch({
            ...entityUrls.batchRequest,
            data: body,
        });

        return res;
    } catch (e) {
        return e;
    }
};

export const FORM = {
    RESET_FORM: 'RESET_FORM',
    REVALIDATE: 'REVALIDATE',
};

export const LOCK = {
    CONFIRM_LOCK: 'CONFIRM_LOCK',
    CONFIRM_UNLOCK: 'CONFIRM_UNLOCK',
};

export const CONFIRM_DELETE = 'CONFIRM_DELETE';

export const clearStore = () => ({
    type: actionTypes.CLEAR_STORE,
});

export const setLoadingAccesses = (payload) => ({
    type: actionTypes.LOADING_ACCESSES,
    payload,
});

export const setAccesses = (payload) => ({
    type: actionTypes.SET_ACCESSES,
    payload,
});

export const setLoadingRemoveBOM = (payload) => ({
    type: actionTypes.LOADING_REMOVE_BOM,
    payload,
});

export const setLoadingLifecycleDetail = (payload) => ({
    type: actionTypes.LOADING_LIFECYCLE_DETAIL,
    payload,
});

export const setLoadingEntityStateHistory = (payload) => ({
    type: actionTypes.LOADING_ENTITY_STATE_HISTORY,
    payload,
});

export const setLifecycleDetail = (payload) => ({
    type: actionTypes.SET_LIFECYCLE_DETAIL,
    payload,
});

export const setEntityStateHistory = (payload) => ({
    type: actionTypes.SET_ENTITY_STATE_HISTORY,
    payload,
});

export const setLoadingDeleteGlobalAlternate = (payload) => ({
    type: actionTypes.LOADING_DELETE_GLOBAL_ALTERNATE,
    payload,
});

export const setSelectedBOMs = (payload) => ({
    type: actionTypes.SET_SELECTED_BOMS,
    payload,
});

export const PROMPT = {
    CONFIRM_NAVIGATE: 'CONFIRM_NAVIGATE',
    CANCEL: 'CANCEL',
};

export const setLoadingWhereUsed = (payload) => ({
    type: actionTypes.LOADING_WHERE_USED,
    payload,
});

export const setWhereUsedData = (payload) => ({
    type: actionTypes.SET_WHERE_USED,
    payload,
});

export const onOpenModal = (payload) => ({
    type: actionTypes.ON_OPEN_MODAL,
    payload,
});

export const onCloseModal = (payload) => ({
    type: actionTypes.ON_CLOSE_MODAL,
    payload,
});

export const onResetModal = () => ({
    type: actionTypes.ON_RESET_MODAL,
});

export const updateWidgetContainer = (payload: boolean) => ({
    type: actionTypes.UPDATE_WIDGET_CONTAINER,
    payload,
});

export const setLoadingApplyingClassifications = (payload: boolean) => ({
    type: actionTypes.LOADING_APPLYING_CLASSIFICATIONS,
    payload,
});

export const setDetailEntity = (payload) => ({
    type: actionTypes.SET_DETAIL_ENTITY,
    payload,
});

export const setMasterSchema = (payload) => ({
    type: actionTypes.SET_MASTER_SCHEMA,
    payload,
});

export const updateDetailEntity = (payload) => ({
    type: actionTypes.UPDATE_DETAIL_ENTITY,
    payload,
});

export const setLoadingDetailEntity = (payload: boolean) => ({
    type: actionTypes.LOADING_DETAIL_ENTITY,
    payload,
});

export const setLoaded = (payload) => ({
    type: actionTypes.SET_LOADED,
    payload,
});

export const setLoadingSchemaDetail = (payload) => ({
    type: actionTypes.LOADING_SCHEMA_DETAIL,
    payload,
});

export const setSchemaDetail = (payload) => ({
    type: actionTypes.SET_SCHEMA_DETAIL,
    payload,
});

export const setDigitalThreads = (payload: DigitalThreadSchema[]) => ({
    type: actionTypes.SET_DIGITAL_THREADS,
    payload,
});

export const setRevisionSchema = (payload) => ({
    type: actionTypes.SET_REVISION_SCHEMA_DETAIL,
    payload,
});

export const setLoadingUpdatingProperties = (payload) => ({
    type: actionTypes.LOADING_UPDATING_PROPERTIES,
    payload,
});

export const setFlattenClassificationTree = (payload) => ({
    type: actionTypes.SET_FLATTEN_TREE,
    payload,
});

export const setLoadingAddBOM = (payload) => ({
    type: actionTypes.LOADING_ADD_BOM,
    payload,
});

export const setLoadingEntityRelations = (payload) => ({
    type: actionTypes.LOADING_GET_RELATIONS_SUMMARY,
    payload,
});

export const setLoadingGrantPermission = (payload) => ({
    type: actionTypes.LOADING_GRANT_ACCESS,
    payload,
});

export const storeAgent = (payload) => ({
    type: actionTypes.STORE_AGENT,
    payload,
});

export const storeEntityLifecycles = (payload) => ({
    type: actionTypes.STORE_ENTITY_LIFECYCLES,
    payload,
});

export const onGettingEntityLifecycles = (payload) => ({
    type: actionTypes.ON_GETTING_ENTITY_LIFECYCLES,
    payload,
});

export const setLoadingRelationEntities = (payload) => ({
    type: actionTypes.LOADING_RELATION_ENTITIES,
    payload,
});

export const setDetailMasterEntity = (payload) => ({
    type: actionTypes.SET_MASTER_DETAILS,
    payload,
});

export const updateMasterDetailEntity = (payload) => ({
    type: actionTypes.UPDATE_MASTER_DETAIL_ENTITY,
    payload,
});

export const setRevisions = (payload) => ({
    type: actionTypes.SET_MASTER_REVISON_DATA,
    payload,
});

export const setEntityModel = (payload) => ({
    type: actionTypes.SET_ENTITY_MODEL,
    payload,
});

export const setLoadingBom = (payload) => ({
    type: actionTypes.LOADING_BOM_TREE,
    payload,
});

export const setBomTree = (payload) => ({
    type: actionTypes.SET_BOM_TREE,
    payload,
});

export const setDecompositionSchema = (payload) => ({
    type: actionTypes.SET_DECOMPOSITION_SCHEMA,
    payload,
});

export const setRecursiveComponents = (payload) => ({
    type: actionTypes.SET_RECURSIVE_COMPONENTS,
    payload,
});

export const setRecursiveAssemblies = (payload) => ({
    type: actionTypes.SET_RECURSIVE_ASSEMBLIES,
    payload,
});

export const setClassificationSchema = (payload) => ({
    type: actionTypes.SET_CLASSIFICATION_SCHEMA,
    payload,
});

export const setLoadingChangedEntity = (payload) => ({
    type: actionTypes.SET_LOADING_CHANGED_ENTITY,
    payload,
});

export const setChangedEntity = (payload) => ({
    type: actionTypes.SET_CHANGED_ENTITY,
    payload,
});

export const setReportLayout = (id, payload) => ({
    type: actionTypes.SET_REPORT_LAYOUT,
    id,
    payload,
});

export const setChangedEntitySchema = (payload) => ({
    type: actionTypes.SET_CHANGED_ENTITY_SCHEMA,
    payload,
});

export const setLoadingIssueSummary = (payload) => ({
    type: actionTypes.LOADING_ISSUE_SUMMARY,
    payload,
});

export const setIssueSummary = (payload) => ({
    type: actionTypes.SET_ISSUE_SUMMARY,
    payload,
});

export const setLoadingRevisionSummary = (payload) => ({
    type: actionTypes.LOADING_REVISION_SUMMARY,
    payload,
});

export const setRevisionSummary = (payload) => ({
    type: actionTypes.SET_REVISION_SUMMARY,
    payload,
});

export const setLoadingChangedEntityBom = (payload) => ({
    type: actionTypes.LOADING_CHANGED_ENTITY_BOM,
    payload,
});

export const setReportSchedules = (payload) => ({
    type: actionTypes.SET_REPORT_SCHEDULES,
    payload,
});

export const setChangedEntityBom = (payload) => ({
    type: actionTypes.SET_CHANGED_ENTITY_BOM,
    payload,
});

export const setLoadingChangedItems = (payload) => ({
    type: actionTypes.LOADING_CHANGED_ITEMS,
    payload,
});

export const setChangeItems = (payload) => ({
    type: actionTypes.SET_CHANGED_ATTRIBUTE_ITEM,
    payload,
});

export const setLoadingUpdatingPlannedChange = (payload) => ({
    type: actionTypes.LOADING_UPDATE_PLANNED_CHANGE,
    payload,
});

export const setLoadingAffectedItems = (payload) => ({
    type: actionTypes.LOADING_AFFECTED_ITEMS,
    payload,
});

export const setAffectedItems = (payload) => ({
    type: actionTypes.SET_AFFECTED_ITEMS,
    payload,
});

export const setShowChangeItemsOnly = (payload) => ({
    type: actionTypes.SET_SHOW_CHANGE_ITEMS_ONLY,
    payload,
});

export const setShowPlannedChange = (payload) => ({
    type: actionTypes.SET_SHOW_PLANNED_CHANGE,
    payload,
});

export const setChangedEntityMaster = (payload) => ({
    type: actionTypes.SET_CHANGED_ENTITY_MASTER,
    payload,
});

export const setChangedEntityMasterSchema = (payload) => ({
    type: actionTypes.SET_CHANGED_ENTITY_MASTER_SCHEMA,
    payload,
});

export const setLoadingBomPlannedChange = (payload) => ({
    type: actionTypes.SET_LOADING_BOM_PLANNED_CHANGE_SUMMARY,
    payload,
});

export const setBomPlannedChangeSummary = (payload) => ({
    type: actionTypes.SET_BOM_PLANNED_CHANGE_SUMMARY,
    payload,
});

export const setLoadingChangeState = (payload) => ({
    type: actionTypes.LOADING_CHANGE_STATE,
    payload,
});

export const setLoadingBusinessRules = (payload) => ({
    type: actionTypes.LOADING_BUSINESS_RULES,
    payload,
});

export const setBusinessRules = (payload) => ({
    type: actionTypes.SET_BUSINESS_RULES,
    payload,
});

export const setLoadingInvokeRules = (payload) => ({
    type: actionTypes.LOADING_INVOKE_RULES,
    payload,
});

export const setInvokedRules = (payload) => ({
    type: actionTypes.SET_INVOKED_RULES,
    payload,
});

export const setSchema = (payload) => ({
    type: actionTypes.SET_SCHEMA,
    payload,
});

export const setIssueSchema = (payload) => ({
    type: actionTypes.SET_ISSUE_SCHEMA,
    payload,
});

export const setLoadingOverlay = (payload) => ({
    type: actionTypes.LOADING_OVERLAY,
    payload,
});

export const setLoadingRelations = (payload) => ({
    type: actionTypes.LOADING_RELATIONS,
    payload,
});

export const setRelations = (payload) => ({
    type: actionTypes.SET_RELATIONS,
    payload,
});

export const setComponents = (payload) => ({
    type: actionTypes.SET_COMPONENTS,
    payload,
});

export const setLoadingComponents = (payload) => ({
    type: actionTypes.LOADING_COMPONENTS,
    payload,
});

export const setProposalRelations = (payload) => ({
    type: actionTypes.SET_PROPOSAL_OF_RELATIONS,
    payload,
});

export const setImpactAnalysis = (payload) => ({
    type: actionTypes.SET_IMPACT_ANALYSIS,
    payload,
});

export const loadingImpactAnalysis = (payload) => ({
    type: actionTypes.LOADING_PLANNED_CHANGE_IMPACT_ANALYSIS,
    payload,
});

export const setHasAppliedProcess = (payload) => ({
    type: actionTypes.SET_HAS_APPLIED_PROCESS,
    payload,
});

export const checkEntityTypeHasAppliedProcess = (entityType, signal) => async (dispatch) => {
    try {
        const { data } = await apiFetch({
            ...schemaUrls.getSchemaProcess,
            params: { entityType },
            signal,
        });
        const hasProcess = !isEmpty(data);
        dispatch(setHasAppliedProcess(hasProcess));
    } catch (err) {
        dispatch(setHasAppliedProcess(false));
    }
};

export const addImpactAnalysisToPlannedChange = (plannedChangeEntity, impactAnalysisId) => async (dispatch) => {
    try {
        dispatch(loadingImpactAnalysis(true));

        await apiFetch({
            ...entityUrls.createRelation,
            params: {
                fromEntityId: plannedChangeEntity.id,
                relationType: SYSTEM_RELATION.IMPACT_ANALYSIS,
                toEntityId: impactAnalysisId,
            },
        });
        await dispatch(getPlannedChangeImpactAnalysis(plannedChangeEntity));
        return true;
    } catch (err) {
        console.error(err);
        return false;
    } finally {
        dispatch(loadingImpactAnalysis(false));
    }
};

export const getPlannedChangeImpactAnalysis = (plannedChangeEntity, signal?) => async (dispatch, getState) => {
    try {
        const { impactAnalysis } = getState().appReducer;
        if (impactAnalysis) return;

        dispatch(loadingImpactAnalysis(true));

        const {
            data: { data: relations },
        } = await apiFetch({
            ...entityUrls.getAllRelationsUnderEntity,
            params: {
                entityId: plannedChangeEntity.id,
            },
            qs: {
                limit: 1,
                relationNames: SYSTEM_RELATION.IMPACT_ANALYSIS,
            },
            signal,
        });

        if (relations.length > 0) {
            const relationEntity = get(relations, ['0', 'relation']);
            const { data: detailEntity } = await apiFetch({
                ...entityUrls.getEntityById,
                params: { entityType: relationEntity.type, entityId: relationEntity.id },
            });
            const classificationSchema = await getClassificationSchema(detailEntity.classifications || []);
            dispatch(setClassificationSchema(classificationSchema));
            dispatch(setImpactAnalysis(detailEntity));
        }
        return true;
    } catch (err) {
        console.error(err);
        return false;
    } finally {
        dispatch(loadingImpactAnalysis(false));
    }
};

export const getEntityRelations = (entityId, signal) => async (dispatch, getState) => {
    try {
        const { relations } = getState().appReducer;
        if (Boolean(relations)) return;

        dispatch(setLoadingRelations(true));
        const { data } = await apiFetch({
            ...entityUrls.getAllRelationsUnderEntity,
            params: {
                entityId,
            },
            qs: {
                limit: DEFAULT_CLIENT_SIDE_LIMIT,
            },
            signal,
        });
        dispatch(setRelations(data.data));
    } catch {
        return false;
    } finally {
        dispatch(setLoadingRelations(false));
    }
};

export const getComponents = (entityId, signal) => async (dispatch, getState) => {
    try {
        const { components } = getState().appReducer;
        if (Boolean(components)) return;

        dispatch(setLoadingComponents(true));
        const { data } = await apiFetch({
            ...entityUrls.getBOMList,
            params: {
                entityId,
            },
            qs: {
                limit: DEFAULT_CLIENT_SIDE_LIMIT,
                level: 1,
            },
            signal,
        });
        dispatch(setComponents(data.data?.map((bom) => bom.component)));
    } catch {
        return false;
    } finally {
        dispatch(setLoadingComponents(false));
    }
};

export const invokeStateTransitionRules =
    (entityType, eventType, fromState, toState, signal?) => async (dispatch, getState) => {
        try {
            const { lifecycleInvokedRules, detailEntity, relations, components } = getState().appReducer;
            const key = `${fromState}-${toState}`;
            if (lifecycleInvokedRules[key])
                return (
                    isEmpty(lifecycleInvokedRules[key]?.results) ||
                    lifecycleInvokedRules[key]?.results.every((result) => result.status === 'Valid')
                );

            dispatch(setLoadingInvokeRules(true));
            const { data } = await apiFetch({
                ...ruleUrls.invokeRuleByEntityType,
                params: {
                    entityType,
                    eventType,
                },
                data: {
                    entity: detailEntity,
                    metadata: {
                        fromState,
                        toState,
                    },
                    relations,
                    components,
                },
                signal,
            });
            dispatch(
                setInvokedRules({
                    ...lifecycleInvokedRules,
                    [key]: data.results,
                })
            );
            if (isEmpty(data.results) || data.results.every((result) => result.status === 'Valid')) {
                return true;
            }
            return false;
        } catch {
            return false;
        } finally {
            dispatch(setLoadingInvokeRules(false));
        }
    };

export const getLifecycleBusinessRules = (entityType, signal?) => async (dispatch, getState) => {
    try {
        const { lifecycleBusinessRules } = getState().appReducer;
        if (lifecycleBusinessRules !== null) {
            return;
        }
        dispatch(setLoadingBusinessRules(true));

        const responses = await Promise.all(
            Object.values(LIFECYCLE_RULE_EVENT_TYPES).map(async (eventType) =>
                apiFetch({
                    ...ruleUrls.getRules,
                    qs: {
                        entityType,
                        eventType,
                        limit: DEFAULT_CLIENT_SIDE_LIMIT,
                    },
                    signal,
                })
            )
        );

        const data = flattenDeep(responses.map(({ data }) => data.data)).map((rule: Rule) => ({
            ...rule,
            fromState: extractFromState(rule.expression),
            toState: extractToState(rule.expression),
        }));

        dispatch(setBusinessRules(flattenDeep(data)));
    } catch {
        return false;
    } finally {
        dispatch(setLoadingBusinessRules(false));
    }
};

export const refreshEntityDetail = (entity: EntityDetail) => async (dispatch) => {
    const { data } = await apiFetch({
        ...entityUrls.getEntityById,
        params: { entityId: entity.id, entityType: entity.properties.type },
    });

    dispatch(setDetailEntity(data));
};

export const promoteEntity =
    (entity, stateName, skipValidate = false) =>
    async (dispatch) => {
        try {
            dispatch(setLoadingChangeState(true));
            if (!skipValidate) {
                const validation = await dispatch(
                    invokeStateTransitionRules(
                        entity.properties.type,
                        LIFECYCLE_RULE_EVENT_TYPES.ENTITY_PROMOTING,
                        entity.state.name,
                        stateName
                    )
                );
                if (!validation) {
                    return false;
                }
            }
            await apiFetch({
                ...entityUrls.promote,
                params: {
                    entityId: entity.id,
                    stateName,
                },
                skipDetails: true,
                successMessage: `Successfully promoted the entity to state <b>${stateName}</b>`,
            });
            await dispatch(refreshEntityDetail(entity));
            return true;
        } catch (err) {
            console.error('Failed to demote entity', err);
            return false;
        } finally {
            dispatch(setLoadingChangeState(false));
        }
    };

export const demoteEntity =
    (entity, stateName, skipValidate = false) =>
    async (dispatch) => {
        try {
            dispatch(setLoadingChangeState(true));

            if (!skipValidate) {
                const validation = await dispatch(
                    invokeStateTransitionRules(
                        entity.properties.type,
                        LIFECYCLE_RULE_EVENT_TYPES.ENTITY_DEMOTING,
                        entity.state.name,
                        stateName
                    )
                );
                if (!validation) {
                    return false;
                }
            }

            await apiFetch({
                ...entityUrls.demote,
                params: {
                    entityId: entity.id,
                    stateName,
                },
                skipDetails: true,
                successMessage: `Successfully demoted the entity to state <b>${stateName}</b>`,
            });
            await dispatch(refreshEntityDetail(entity));
            return true;
        } catch (err) {
            console.error('Failed to demote entity', err);
            return false;
        } finally {
            dispatch(setLoadingChangeState(false));
        }
    };
export const getAffectedItems = (entity: EntityDetail, errorHandler: () => void) => async (dispatch) => {
    try {
        dispatch(setLoadingAffectedItems(true));

        const isECR = isChangeRequest(get(entity, ['properties', 'type']));
        const request = isECR ? changeUrls.getChangeRequestAffectedItems : changeUrls.getChangeOrderAffectedItems;
        const { data }: { data: { data: AffectedItemRes[] } } = await apiFetch({
            ...request,
            params: {
                entityId: entity.id,
            },
            qs: {
                limit: DEFAULT_CLIENT_SIDE_LIMIT,
            },
            skipToast: true,
        });
        dispatch(setAffectedItems(data?.data || []));
    } catch (e) {
        errorHandler();
        dispatch(setAffectedItems([]));
    } finally {
        dispatch(setLoadingAffectedItems(false));
    }
};

export const updateAttributePlan = (plannedChange, changedEntity, changedAttributes) => async (dispatch) => {
    try {
        dispatch(setLoadingUpdatingPlannedChange(true));
        const entityId = plannedChange.id;
        await Promise.all(
            changedAttributes.map(async (attribute) =>
                apiFetch({
                    ...changeUrls.updateAttributePlan,
                    params: {
                        entityId,
                    },
                    data: {
                        entityId: changedEntity.id,
                        payload: {
                            [attribute.name]: attribute.value,
                        },
                    },
                })
            )
        );
        await Promise.all([
            dispatch(getchangeItems(plannedChange)),
            dispatch(getBomPlannedChangeSummary(plannedChange)),
        ]);
        notifySuccess('Successfully updated your planned change for <b>Properties</b>');
        return true;
    } catch (err) {
        return false;
    } finally {
        dispatch(setLoadingUpdatingPlannedChange(false));
    }
};

export const createDeleteComponentPlan = (entity, components) => async (dispatch) => {
    try {
        dispatch(setLoadingUpdatingPlannedChange(true));
        const entityId = entity.id;
        const responses = await Promise.all(
            components.map(async (component) =>
                apiFetch({
                    ...changeUrls.deleteComponentPlan,
                    params: {
                        entityId,
                    },
                    data: {
                        entityId: component.id,
                    },
                    skipToast: true,
                })
            )
        );

        await Promise.all([dispatch(getchangeItems(entity)), dispatch(getBomPlannedChangeSummary(entity))]);
        notifySuccess('Successfully updated your planned change for <b>BOM</b>');
        return true;
    } catch (err) {
        notifyError('An error occurred while updating your planned change');
        dispatch(getchangeItems(entity));
        dispatch(getBomPlannedChangeSummary(entity));
        return false;
    } finally {
        dispatch(setLoadingUpdatingPlannedChange(false));
    }
};

export const createAddComponentPlan = (entity, requests) => async (dispatch) => {
    try {
        dispatch(setLoadingUpdatingPlannedChange(true));
        const entityId = entity.id;
        const responses = await Promise.all(
            requests.map(async (request) =>
                apiFetch({
                    ...changeUrls.addComponentPlan,
                    params: {
                        entityId,
                    },
                    data: request,
                    skipToast: true,
                })
            )
        );

        await Promise.all([dispatch(getchangeItems(entity)), dispatch(getBomPlannedChangeSummary(entity))]);
        notifySuccess('Successfully updated your planned change for <b>BOM</b>');
        return true;
    } catch (err) {
        notifyError('An error occurred while updating your planned change');
        dispatch(getchangeItems(entity));
        dispatch(getBomPlannedChangeSummary(entity));
        return false;
    } finally {
        dispatch(setLoadingUpdatingPlannedChange(false));
    }
};

export const createReplaceComponentPlan = (entity, requests) => async (dispatch) => {
    try {
        dispatch(setLoadingUpdatingPlannedChange(true));
        const entityId = entity.id;
        const responses = await Promise.all(
            requests.map(async (request) =>
                apiFetch({
                    ...changeUrls.replaceComponentPlan,
                    params: {
                        entityId,
                    },
                    data: request,
                    skipToast: true,
                })
            )
        );

        await Promise.all([dispatch(getchangeItems(entity)), dispatch(getBomPlannedChangeSummary(entity))]);

        notifySuccess('Successfully updated your planned change for <b>BOM</b>');
        return true;
    } catch (err) {
        notifyError('An error occurred while updating your planned change');
        dispatch(getchangeItems(entity));
        dispatch(getBomPlannedChangeSummary(entity));
        return false;
    } finally {
        dispatch(setLoadingUpdatingPlannedChange(false));
    }
};

export const createSwapComponentPlan = (entity, requests) => async (dispatch) => {
    try {
        dispatch(setLoadingUpdatingPlannedChange(true));
        const entityId = entity.id;
        const responses = await Promise.all(
            requests.map(async (request) =>
                apiFetch({
                    ...changeUrls.swapComponentPlan,
                    params: {
                        entityId,
                    },
                    data: request,
                    skipToast: true,
                })
            )
        );

        await Promise.all([dispatch(getchangeItems(entity)), dispatch(getBomPlannedChangeSummary(entity))]);

        notifySuccess('Successfully updated your planned change for <b>BOM</b>');
        return true;
    } catch (err) {
        notifyError('An error occurred while updating your planned change');
        dispatch(getchangeItems(entity));
        dispatch(getBomPlannedChangeSummary(entity));
        return false;
    } finally {
        dispatch(setLoadingUpdatingPlannedChange(false));
    }
};

export const createAddCAPlan = (entity, requests) => async (dispatch) => {
    try {
        dispatch(setLoadingUpdatingPlannedChange(true));
        const entityId = entity.id;
        const responses = await Promise.all(
            requests.map(async (request) =>
                apiFetch({
                    ...changeUrls.addContextualAlternatePlan,
                    params: {
                        entityId,
                    },
                    data: request,
                    skipToast: true,
                })
            )
        );

        await Promise.all([dispatch(getchangeItems(entity)), dispatch(getBomPlannedChangeSummary(entity))]);
        notifySuccess('Successfully updated your planned change for <b>BOM</b>');
        return true;
    } catch (err) {
        notifyError('An error occurred while updating your planned change');
        console.error(err);
        dispatch(getchangeItems(entity));
        dispatch(getBomPlannedChangeSummary(entity));
        return false;
    } finally {
        dispatch(setLoadingUpdatingPlannedChange(false));
    }
};

export const createRemoveCAPlan = (entity, requests) => async (dispatch) => {
    try {
        dispatch(setLoadingUpdatingPlannedChange(true));
        const entityId = entity.id;
        const responses = await Promise.all(
            requests.map(async (request) =>
                apiFetch({
                    ...changeUrls.removeContextualAlternatePlan,
                    params: {
                        entityId,
                    },
                    data: request,
                    skipToast: true,
                })
            )
        );

        await Promise.all([dispatch(getchangeItems(entity)), dispatch(getBomPlannedChangeSummary(entity))]);
        notifySuccess('Successfully updated your planned change for <b>BOM</b>');
        return true;
    } catch (err) {
        notifyError('An error occurred while updating your planned change');
        console.error(err);
        dispatch(getchangeItems(entity));
        dispatch(getBomPlannedChangeSummary(entity));
        return false;
    } finally {
        dispatch(setLoadingUpdatingPlannedChange(false));
    }
};

export const deleteChangeItems = (plannedChange, changeItems) => async (dispatch) => {
    try {
        dispatch(setLoadingUpdatingPlannedChange(true));
        await Promise.all(
            changeItems.map(async (item) =>
                apiFetch({
                    ...entityUrls.deleteEntity,
                    params: {
                        entityId: item.id,
                    },
                })
            )
        );
        await Promise.all([
            dispatch(getchangeItems(plannedChange)),
            dispatch(getBomPlannedChangeSummary(plannedChange)),
        ]);
        notifySuccess('Successfully updated your planned change for <b>BOM</b>');
        return true;
    } catch (err) {
        return false;
    } finally {
        dispatch(setLoadingUpdatingPlannedChange(false));
    }
};

export const getchangeItems = (entity) => async (dispatch) => {
    try {
        dispatch(setLoadingChangedItems(true));
        const { data } = await apiFetch({
            ...entityUrls.getEntityRelations,
            params: {
                fromEntityId: entity.id,
                relationType: SYSTEM_RELATION.BELONGS_TO,
                entityType: SYSTEM_ENTITY_TYPE.CHANGE_ITEM,
            },
            qs: {
                limit: DEFAULT_CLIENT_SIDE_LIMIT,
                reverse: true,
            },
        });
        dispatch(setChangeItems(groupBy(data.data, 'properties.entityId')));
    } catch {
        dispatch(setChangeItems({}));
        return false;
    } finally {
        dispatch(setLoadingChangedItems(false));
    }
};

export const getChangedEntityBom = (entity, signal) => async (dispatch) => {
    try {
        dispatch(setLoadingChangedEntityBom(true));
        const entityId = entity.id;
        const {
            data: { data },
        }: { data: { data: BOM[] } } = await apiFetch({
            ...entityUrls.getBOMList,
            params: {
                entityId,
            },
            qs: {
                limit: DEFAULT_CLIENT_SIDE_LIMIT,
                level: 1,
                fields: ['permissions'],
            },
            signal,
        });
        dispatch(setChangedEntityBom(data));
    } catch (err) {
    } finally {
        dispatch(setLoadingChangedEntityBom(false));
    }
};

export const getReportSchedules = (reportId, signal?) => async (dispatch) => {
    try {
        const { data }: { data: ReportSchedule[] } = await apiFetch({
            ...reportingUrls.getReportSchedules,
            params: {
                reportId,
            },
            signal,
            skipToast: true,
        });
        dispatch(setReportSchedules(data));
    } catch (err) {}
};

export const getReportLayout = (reportLayoutId) => async (dispatch) => {
    try {
        const { data }: { data: EntityDetail } = await apiFetch({
            ...reportingUrls.getReportLayoutDetail,
            params: {
                reportLayoutId,
            },
            skipToast: true,
        });
        dispatch(setReportLayout(reportLayoutId, data));
    } catch (err) {
        dispatch(setReportLayout(reportLayoutId, null));
    }
};

export const getBomPlannedChangeSummary = (entity, signal?) => async (dispatch) => {
    try {
        dispatch(setLoadingBomPlannedChange(true));
        const entityId = entity.id;
        const {
            data: { data },
        } = await apiFetch({
            ...changeUrls.getPlannedChangeBomSummary,
            params: {
                entityId,
            },
            qs: {
                limit: DEFAULT_CLIENT_SIDE_LIMIT,
                level: 1,
            },
            signal,
        });
        dispatch(setBomPlannedChangeSummary(data));
    } catch (err) {
    } finally {
        dispatch(setLoadingBomPlannedChange(false));
    }
};

const getCadModel = async (entityId) => {
    try {
        const {
            data: { data },
        } = await apiFetch({
            ...entityUrls.getEntityRelations,
            params: {
                fromEntityId: entityId,
                relationType: SYSTEM_RELATION.REFERENCE_TO,
                entityType: SYSTEM_ENTITY_TYPE.THREED_CAD_MODEL,
            },
        });
        if (data?.length > 0) {
            const cadEntity = data[0];
            return {
                id: cadEntity.id,
                name: get(cadEntity, 'properties.name'),
                type: get(cadEntity, 'properties.type'),
            };
        }
        return null;
    } catch (error) {
        throw error;
    }
};

const getBomList = async (entityId, viewOption) => {
    try {
        const {
            data: { data },
        }: { data: { data: BOM[] } } = await apiFetch({
            ...entityUrls.getBOMList,
            params: {
                entityId,
            },
            qs: {
                viewOption,
                limit: DEFAULT_CLIENT_SIDE_LIMIT,
            },
        });
        return data;
    } catch (error) {
        throw error;
    }
};

export const getBomTreeData =
    (entity, viewOption = BOM_VIEW_OPTIONS.LATEST_REVISION, rootAlternates = []) =>
    async (dispatch) => {
        try {
            const entityId = entity.id;
            dispatch(setLoadingBom(true));

            const [cadModel, data] = await Promise.all([getCadModel(entityId), getBomList(entityId, viewOption)]);

            const grouped = groupBy(data, 'assemblyId');
            const root = {
                id: entityId,
                rowId: entityId,
                path: [entityId],
                componentPath: {
                    [entityId]: entityId,
                },
                component: {
                    id: entityId,
                    permissions: entity.permissions,
                    ...entity.properties,
                    alternates: rootAlternates || [],
                    cadModel,
                },
                uiStates: {
                    isRoot: true,
                    currentPermissions: entity.permissions,
                },
            };
            let aggregatedTree = { [entityId]: root };
            let recursiveComponents = [];
            aggregateBomTree(root, aggregatedTree, grouped, recursiveComponents);

            dispatch(setRecursiveComponents(uniqBy(recursiveComponents, 'component.name')));
            dispatch(setBomTree(Object.values(aggregatedTree)));
        } catch (error) {
        } finally {
            dispatch(setLoadingBom(false));
        }
    };

export const getWhereUsedData =
    (entity: EntityDetail, isRevToRev: boolean, level = WHERE_USED_LEVEL_OPTIONS.MULTIPLE, errorHandler: () => void) =>
    async (dispatch) => {
        try {
            dispatch(setLoadingWhereUsed(true));
            const entityId = entity.id;
            const isLastLevelOnly = level === WHERE_USED_LEVEL_OPTIONS.LAST_LEVEL_ONLY;
            let qs = {
                limit: DEFAULT_CLIENT_SIDE_LIMIT,
                fields: ['manufacturers'],
            };
            if (isLastLevelOnly) {
                qs['lastOnly'] = true;
            }
            const {
                data: { data },
            }: { data: { data: WhereUsed[] } } = await apiFetch({
                ...entityUrls.whereUsed,
                params: {
                    entityId,
                },
                qs,
                skipToast: true,
            });

            if (isLastLevelOnly) {
                dispatch(
                    setWhereUsedData(
                        orderBy(
                            data.map((row, idx) => {
                                const rowId = `${row.properties.id}-${idx}`;
                                return {
                                    ...row,
                                    rowId,
                                    path: [rowId],
                                    uiStates: {
                                        isRoot: false,
                                    },
                                };
                            }),
                            'updatedAt',
                            'desc'
                        )
                    )
                );
                return;
            }
            const grouped = groupBy(data, 'componentId');
            const root = {
                id: entityId,
                rowId: entityId,
                path: [entityId],
                assemblyPath: {
                    [entityId]: entityId,
                },
                assembly: {
                    id: entityId,
                    ...entity.properties,
                    assemblyMasterId: entityId,
                },
                properties: {
                    ...entity.properties,
                    id: 'entityId',
                },
                uiStates: {
                    isRoot: true,
                },
            };

            let aggregatedTree = { [entityId]: root };
            let recursiveAssemblies = [];
            aggregateWhereUsedTree(root, aggregatedTree, grouped, recursiveAssemblies, isRevToRev);

            const rows = Object.values(aggregatedTree);
            const isEmpty = rows.length === 1;
            dispatch(setRecursiveAssemblies(recursiveAssemblies));
            dispatch(setWhereUsedData(isEmpty ? [] : rows));
        } catch (error) {
            errorHandler();
            dispatch(setWhereUsedData([]));
        } finally {
            dispatch(setLoadingWhereUsed(false));
        }
    };

export const getDocumentWhereUsedData = (entity) => async (dispatch, getState) => {
    try {
        const {
            detailSchema: { relationTypes },
        } = getState().appReducer;
        dispatch(setLoadingWhereUsed(true));

        const visibleRelations = relationTypes
            .filter((r: any) => r.visible && r.direction === RELATION_DIRECTION.INCOMING)
            .map((r) => r.name);

        const {
            data: { data },
        } = await apiFetch({
            ...entityUrls.getAllRelationsUnderEntity,
            params: { entityId: entity.id },
            qs: {
                type: [SYSTEM_ENTITY_TYPE.SYS_ROOT],
                limit: DEFAULT_CLIENT_SIDE_LIMIT,
                relationNames: visibleRelations,
            },
        });

        dispatch(setWhereUsedData(data));
    } catch (error) {
    } finally {
        dispatch(setLoadingWhereUsed(false));
    }
};

export const getIssueSummary = (entityId, errorHandler: () => void) => async (dispatch) => {
    try {
        dispatch(setLoadingIssueSummary(true));

        const requests = [
            apiFetch({
                ...changeUrls.getIssueSummary,
                params: {
                    entityId,
                },
                qs: {
                    limit: DEFAULT_CLIENT_SIDE_LIMIT,
                },
                skipToast: true,
            }),
            dispatch(getSchema('issueSchema', SYSTEM_ENTITY_TYPE.ISSUE)),
            dispatch(getSchema('changeOrderSchema', SYSTEM_ENTITY_TYPE.CHANGE_ORDER)),
            dispatch(getSchema('changeRequestSchema', SYSTEM_ENTITY_TYPE.CHANGE_REQUEST)),
        ];

        const responses = await Promise.all(requests);
        dispatch(setIssueSummary(responses[0].data.data));
    } catch (error) {
        errorHandler();
        dispatch(setIssueSummary([]));
    } finally {
        dispatch(setLoadingIssueSummary(false));
    }
};

export const getSchema = (key, type) => async (dispatch, getState) => {
    const schema = getState().appReducer[key];

    if (schema) return;

    const { data } = await apiFetch({
        ...schemaUrls.getSchemaDetail,
        params: { entityTypeName: type },
    });
    dispatch(setSchema({ key, data }));
};

export const getRevisonSummary =
    (entityMasterId: string, signal: AbortSignal, errorHandler: () => void) => async (dispatch, getState) => {
        try {
            const { revisionSummary } = getState().appReducer;
            if (Boolean(revisionSummary)) return;

            dispatch(setLoadingRevisionSummary(true));

            const requests = [
                apiFetch({
                    ...changeUrls.getRevisionIssueSummary,
                    params: {
                        entityMasterId,
                    },
                    qs: {
                        limit: DEFAULT_CLIENT_SIDE_LIMIT,
                    },
                    signal,
                    skipToast: true,
                }),
                dispatch(getSchema('issueSchema', SYSTEM_ENTITY_TYPE.ISSUE)),
                dispatch(getSchema('changeRequestSchema', SYSTEM_ENTITY_TYPE.CHANGE_REQUEST)),
                dispatch(getSchema('changeOrderSchema', SYSTEM_ENTITY_TYPE.CHANGE_ORDER)),
            ];

            const responses = await Promise.all(requests);
            dispatch(setRevisionSummary(responses[0].data.data));
        } catch (error) {
            errorHandler();
            dispatch(setRevisionSummary([]));
            console.error(error);
        } finally {
            dispatch(setLoadingRevisionSummary(false));
        }
    };

export const getProposalOfRelations = (entityId: string, signal?) => async (dispatch) => {
    try {
        const { data } = await apiFetch({
            ...entityUrls.getAllRelationsUnderEntity,
            params: {
                entityId,
            },
            qs: {
                relationNames: SYSTEM_RELATION.PROPOSAL_OF,
                direction: 'OUTGOING',
            },
            signal,
        });
        dispatch(setProposalRelations(data.data));
    } catch (error) {
        console.error(error);
    } finally {
    }
};

export const getChangedEntity = (plannedChangeId, signal) => async (dispatch) => {
    try {
        dispatch(setLoadingChangedEntity(true));
        dispatch(getProposalOfRelations(plannedChangeId, signal));
        const { data: changedEntityRelation } = await apiFetch({
            ...entityUrls.getEntityRelations,
            params: {
                fromEntityId: plannedChangeId,
                relationType: SYSTEM_RELATION.CHANGING_ITEM,
                entityType: SYSTEM_ENTITY_TYPE.SYS_ROOT,
            },
            qs: {
                limit: 1,
            },
            signal,
        });
        const item = get(changedEntityRelation, 'data[0]');
        if (item) {
            const [changedItemSchema, entityRes] = await Promise.all([
                apiFetch({
                    ...schemaUrls.getSchemaDetail,
                    params: { entityTypeName: item.properties.type },
                }),

                apiFetch({
                    ...entityUrls.getEntityById,
                    params: { entityType: item.properties.type, entityId: item.id },
                }),
            ]);
            const detailChangedEntity = entityRes.data;

            const classificationSchema = await getClassificationSchema(detailChangedEntity.classifications || []);

            dispatch(setChangedEntity(detailChangedEntity));
            dispatch(setChangedEntitySchema(changedItemSchema.data));
            dispatch(setClassificationSchema(classificationSchema));

            const { relationTypes } = changedItemSchema.data;
            const hasMasterRelation = doesEntityTypeHaveMasterRelation(relationTypes);
            if (isRevisionEntity(hasMasterRelation)) {
                const [masterEntityRes, masterSchemaRes] = await Promise.all([
                    apiFetch({
                        ...entityUrls.getEntityRelations,
                        params: {
                            fromEntityId: item.id,
                            relationType: SYSTEM_RELATION.HAS_MASTER,
                            entityType: hasMasterRelation.toEntityType,
                        },
                        qs: {
                            limit: 1,
                        },
                    }),
                    apiFetch({
                        ...schemaUrls.getSchemaDetail,
                        params: { entityTypeName: hasMasterRelation.toEntityType },
                    }),
                ]);
                getDecompositionSchema(getBomType(changedItemSchema.data), dispatch);
                dispatch(setChangedEntityMaster(get(masterEntityRes, ['data', 'data', '0'])));
                dispatch(setChangedEntityMasterSchema(get(masterSchemaRes, 'data')));
            }
        }
    } catch (err) {
        console.error(err);
    } finally {
        dispatch(setLoadingChangedEntity(false));
    }
};

export const grantPermission =
    (entity, stateName, agentId, agentName, role = CONTENT_ROLE.VIEWER) =>
    async (dispatch) => {
        try {
            dispatch(setLoadingGrantPermission(true));
            await apiFetch({
                ...entityUrls.grantPermission,
                params: { entityId: entity.id, stateName },
                data: {
                    agentId,
                    role,
                },
                successMessage: (
                    <span>
                        Granted <b>{role}</b> access on <b>{stateName}</b> state for <b>{agentName}</b> successfully.
                    </span>
                ),
            });

            return true;
        } catch {
            return false;
        } finally {
            dispatch(setLoadingGrantPermission(false));
        }
    };

export const revokePermission = (entity, stateName, agentIds) => async (dispatch) => {
    try {
        dispatch(setLoadingGrantPermission(true));
        await apiFetch({
            ...entityUrls.revokePermission,
            params: { entityId: entity.id, stateName },
            data: agentIds,
        });

        dispatch(getEntityAccesses(entity.id, () => {}));

        return true;
    } catch {
        return false;
    } finally {
        dispatch(setLoadingGrantPermission(false));
    }
};

export const getClassificationSchema = async (classifications) => {
    let cacheClassificationSchema = { ...store.getState().appReducer.classificationSchema };
    const responses = await Promise.all(
        classifications
            .filter((classification) => !cacheClassificationSchema[classification.name])
            .map(async (classification) =>
                apiFetch({
                    ...classificationUrls.getClassificationDetail,
                    params: { name: classification.name },
                })
            )
    );

    responses.forEach((item) => {
        cacheClassificationSchema[item.data?.classification?.name] = item.data;
    });
    return cacheClassificationSchema;
};

const getDigitalThreads = async (
    entityType: string,
    signal: AbortSignal
): Promise<AxiosResponse<DigitalThreadSchema[], any>> => {
    try {
        const res = await apiFetch({
            ...schemaUrls.getDigitalThreadsByEntityType,
            params: { entityType },
            signal,
            skipToast: true,
        });
        return res;
    } catch (err) {
        return null;
    } finally {
    }
};

export const getEntityById =
    (
        entityType: string,
        entityId: string,
        silent: boolean = false,
        signal?: any,
        onError?: (error: AxiosResponse) => void
    ) =>
    async (dispatch) => {
        try {
            if (!silent) dispatch(setLoadingDetailEntity(true));

            const [entityDetailRes, entitySchemaRes, digitalThreadRes] = await Promise.all([
                apiFetch({
                    ...entityUrls.getEntityById,
                    params: { entityType, entityId },
                    shouldShow404: true,
                    signal,
                }),
                apiFetch({
                    ...schemaUrls.getSchemaDetail,
                    params: { entityTypeName: entityType },
                    signal,
                }),
                getDigitalThreads(entityType, signal),
            ]);

            const entityDetails = entityDetailRes.data;
            const entitySchemaDetails = entitySchemaRes.data;
            dispatch(setDetailEntity(entityDetails));
            dispatch(setSchemaDetail(entitySchemaDetails));
            dispatch(setDigitalThreads(digitalThreadRes?.data));
            const {
                properties: { isMaster },
            } = entityDetails;
            const { relationTypes, schemaName } = entitySchemaDetails;
            const hasMasterRelation = doesEntityTypeHaveMasterRelation(relationTypes);

            // If master entity, get the revision list to display on the UI
            if (isMaster) {
                await Promise.all([
                    getRevisions(hasMasterRelation, entityId, dispatch),
                    getRevisionSchema(hasMasterRelation, dispatch),
                ]);
            }
            let classifications = [...entityDetails.classifications];
            if (isRevisionEntity(hasMasterRelation)) {
                // Get master data list and master schema
                const [masterEntityRes, masterSchemaRes] = await Promise.all([
                    apiFetch({
                        ...entityUrls.getEntityRelations,
                        params: {
                            fromEntityId: entityDetails.id,
                            relationType: SYSTEM_RELATION.HAS_MASTER,
                            entityType: hasMasterRelation.toEntityType,
                        },
                        signal,
                    }),
                    apiFetch({
                        ...schemaUrls.getSchemaDetail,
                        params: { entityTypeName: hasMasterRelation.toEntityType },
                    }),
                    signal,
                ]);

                getDecompositionSchema(getBomType(entitySchemaDetails), dispatch);

                const {
                    data: { data: masterEntity },
                } = masterEntityRes;
                const { data: masterSchema } = masterSchemaRes;
                if (masterEntity.length > 0) {
                    const {
                        id: masterId,
                        properties: { type },
                    } = masterEntity[0];

                    // Get master details
                    const { data: masterDetails } = await apiFetch({
                        ...entityUrls.getEntityById,
                        params: { entityType: type, entityId: masterId },
                        shouldShow404: true,
                        signal,
                    });

                    classifications = uniqBy([...classifications, ...masterDetails.classifications], 'name');
                    dispatch(setMasterSchema(masterSchema));
                    dispatch(setDetailMasterEntity({ ...masterDetails, isFirstLoad: true }));
                }
            }

            const classificationSchema = await getClassificationSchema(classifications);
            dispatch(setClassificationSchema(classificationSchema));

            // 3DCadModel
            if (isCadModel(schemaName)) {
                dispatch(setEntityModel(ENTITY_MODEL.CAD));
                return;
            }

            // Document
            if (isEntityDocumentType(schemaName)) {
                dispatch(
                    setEntityModel(
                        isMaster
                            ? ENTITY_MODEL.DOCUMENT_MASTER
                            : hasMasterRelation
                            ? ENTITY_MODEL.DOCUMENT_REVISION
                            : ENTITY_MODEL.DOCUMENT
                    )
                );
                return;
            }

            if (isMaster) {
                dispatch(setEntityModel(ENTITY_MODEL.MASTER));
                return;
            }

            // Bug type
            if (isBugEntity(schemaName)) {
                dispatch(setEntityModel(ENTITY_MODEL.BUG));
                return;
            }

            // Issue type
            if (isIssueEntity(schemaName)) {
                dispatch(setEntityModel(ENTITY_MODEL.ISSUE));
                return;
            }

            if (isImpactAnalysis(schemaName)) {
                dispatch(setEntityModel(ENTITY_MODEL.IMPACT_ANALYSIS));
                return;
            }

            if (isIpChangeRequestEntity(schemaName)) {
                dispatch(setEntityModel(ENTITY_MODEL.IP_CHANGE_REQUEST));
                return;
            }

            if (isChangeRequest(schemaName)) {
                dispatch(setEntityModel(ENTITY_MODEL.CHANGE_REQUEST));
                return;
            }

            if (isChangeOrder(schemaName)) {
                dispatch(setEntityModel(ENTITY_MODEL.CHANGE_ORDER));
                return;
            }

            if (isPlannedChange(schemaName)) {
                dispatch(setEntityModel(ENTITY_MODEL.PLANNED_CHANGE));
                return;
            }

            if (isFile(schemaName)) {
                dispatch(setEntityModel(ENTITY_MODEL.FILE));
                return;
            }

            if (isReport(schemaName)) {
                dispatch(setEntityModel(ENTITY_MODEL.REPORT));
                return;
            }

            if (hasMasterRelation) {
                dispatch(setEntityModel(ENTITY_MODEL.REVISION));
                return;
            }

            dispatch(setEntityModel(ENTITY_MODEL.OTHER));
        } catch (err) {
            console.error(err);
            onError && onError(err?.response);
        } finally {
            dispatch(setLoadingDetailEntity(false));
            dispatch(setLoaded(true));
        }
    };

export const getEntityAccesses = (entityId: string, errorHandler: () => void) => async (dispatch) => {
    try {
        dispatch(setLoadingAccesses(true));
        const { data } = await apiFetch({
            ...entityUrls.getEntityAccesses,
            params: { entityId },
            shouldShow404: true,
        });
        const accesses = data.data || [];
        dispatch(setAccesses(accesses));

        return accesses;
    } catch (e) {
        errorHandler();
        dispatch(setAccesses([]));
    } finally {
        dispatch(setLoadingAccesses(false));
    }
};

export const getLifecycleById = (lifecycleId: string, signal?) => async (dispatch, getState) => {
    try {
        const { lifecycleDetail } = getState().appReducer;
        if (lifecycleDetail) {
            return;
        }
        dispatch(setLoadingLifecycleDetail(true));
        const { data } = await apiFetch({
            ...schemaUrls.getLifeCycle,
            params: { lifecycleId },
            shouldShow404: true,
            signal,
        });

        dispatch(setLifecycleDetail(data));

        return data;
    } catch {
        return false;
    } finally {
        dispatch(setLoadingLifecycleDetail(false));
    }
};

export const getEntityStateHistory =
    (entityId: string, signal?, silent = false) =>
    async (dispatch) => {
        try {
            !silent && dispatch(setLoadingEntityStateHistory(true));
            const { data } = await apiFetch({
                ...trackingService.getActivity,
                qs: {
                    entityId,
                    sortField: 'eventTimeStamp',
                    sortDirection: 'DESCENDING',
                    eventType: [EVENT_TYPE.ENTITY_PROMOTED, EVENT_TYPE.ENTITY_DEMOTED],
                    limit: 200,
                },
                shouldShow404: true,
                signal,
            });

            dispatch(setEntityStateHistory(data?.data || []));

            return data;
        } catch (err) {
            console.error('Failed to refresh entity state history', err);
            return false;
        } finally {
            !silent && dispatch(setLoadingEntityStateHistory(false));
        }
    };

export const applyClassifications =
    (
        entityType: string,
        entityId: string,
        classifications: string[],
        updateDetailEntityDispatch,
        successMessage: any = 'Succesfully applied classifications'
    ) =>
    async (dispatch) => {
        try {
            dispatch(setLoadingApplyingClassifications(true));
            await apiFetch({
                ...entityUrls.applyClassifications,
                params: { entityId },
                data: classifications,
                successMessage,
            });

            const { data } = await apiFetch({
                ...entityUrls.getEntityById,
                params: { entityId, entityType },
            });

            const classificationSchema = await getClassificationSchema(data.classifications);
            dispatch(setClassificationSchema(classificationSchema));
            dispatch(updateDetailEntityDispatch(data));

            return true;
        } catch {
            return false;
        } finally {
            dispatch(setLoadingApplyingClassifications(false));
        }
    };

export const reviseEntity = (entityId) => async (dispatch) => {
    try {
        dispatch(setLoadingOverlay(true));
        const { data } = await apiFetch({
            ...entityUrls.revise,
            params: {
                entityId,
            },
        });
        return data;
    } catch {
        return false;
    } finally {
        dispatch(setLoadingOverlay(false));
    }
};

export const updateAttributes =
    (entityId: string, payload, updateDetailEntity, successMessage, onError?: (error: AxiosResponse) => void) =>
    async (dispatch) => {
        try {
            dispatch(setLoadingUpdatingProperties(true));
            const { data } = await apiFetch({
                ...entityUrls.updateEntity,
                params: { entityId },
                data: payload,
                successMessage,
            });

            dispatch(updateDetailEntity(data));
            return true;
        } catch (error) {
            onError && onError(error?.response);
            return false;
        } finally {
            dispatch(setLoadingUpdatingProperties(false));
        }
    };

// separate handler for classification attributes because PUT endpoint does not return canRead, canEdit for the classifications in the PUT response
export const updateClassificationAttributes =
    (entityId: string, payload, updateDetailEntity, successMessage, classifications) => async (dispatch) => {
        try {
            dispatch(setLoadingUpdatingProperties(true));
            const { data } = await apiFetch({
                ...entityUrls.updateEntity,
                params: { entityId },
                data: payload,
                successMessage,
            });

            dispatch(updateDetailEntity({ ...data, classifications }));
            return true;
        } catch {
            return false;
        } finally {
            dispatch(setLoadingUpdatingProperties(false));
        }
    };

export const lock = (entity: EntityDetail) => async (dispatch) => {
    try {
        dispatch(setLoadingOverlay(true));
        await apiFetch({
            ...entityUrls.lockEntity,
            params: { entityId: entity?.id },
            successMessage: `Successfully locked the entity <b>${entity?.properties?.name}</b>`,
        });

        await dispatch(refreshEntityDetail(entity));
        return true;
    } catch {
        return false;
    } finally {
        dispatch(setLoadingOverlay(false));
    }
};

export const unLock = (entity: EntityDetail) => async (dispatch) => {
    try {
        dispatch(setLoadingOverlay(true));
        await apiFetch({
            ...entityUrls.unlockEntity,
            params: { entityId: entity?.id },
            successMessage: `Successfully unlocked the entity <b>${entity?.properties?.name}</b>`,
        });

        await dispatch(refreshEntityDetail(entity));
        return true;
    } catch {
        return false;
    } finally {
        dispatch(setLoadingOverlay(false));
    }
};

export const fetchClassificationTree =
    (showEntity = false) =>
    async (dispatch) => {
        try {
            const classificationTree = await apiFetch({
                ...classificationUrls.getClassificationTree,
            });
            let flattenMap = {};
            flattenTreeNodes(classificationTree.data, null, [], flattenMap);
            dispatch(setFlattenClassificationTree(flattenMap));
        } catch (e) {
        } finally {
        }
    };

export const disconnectBOMs =
    (selectedBOMs) =>
    async (dispatch): Promise<BatchRequestResponse> => {
        try {
            dispatch(setLoadingRemoveBOM(true));
            const filteredBomById: any = uniqBy(selectedBOMs, 'id');
            const boms = filteredBomById.map(({ id }) => {
                return {
                    subParams: { entityId: id },
                };
            });

            const data = batchRequestBody(entityUrls.deleteEntity.method, '/entity/:entityId', boms);

            const res = await globalBatchRequest({
                ...entityUrls.batchRequest,
                data,
            });
            return res;
        } catch (e) {
            return null;
        } finally {
            dispatch(setLoadingRemoveBOM(false));
        }
    };

const getRevisions = async (hasMasterRelation: any, entityId: string, dispatch: any) => {
    const fromEntityType = hasMasterRelation.fromEntityType;

    const { data: revisions } = await apiFetch({
        ...entityUrls.getEntityRelations,
        params: {
            fromEntityId: entityId,
            relationType: SYSTEM_RELATION.HAS_MASTER,
            entityType: fromEntityType,
        },
        qs: {
            reverse: true,
            limit: DEFAULT_CLIENT_SIDE_LIMIT,
        },
    });

    dispatch(setRevisions(revisions.data));
};

const getRevisionSchema = async (hasMasterRelation: any, dispatch: any) => {
    const fromEntityType = hasMasterRelation.fromEntityType;

    const { data: revisionSchema } = await apiFetch({
        ...schemaUrls.getSchemaDetail,
        params: { entityTypeName: fromEntityType },
    });
    getDecompositionSchema(getBomType(revisionSchema), dispatch);
    dispatch(setRevisionSchema(revisionSchema));
};

const getDecompositionSchema = async (bomType: any, dispatch: any) => {
    if (bomType) {
        const { data } = await apiFetch({
            ...schemaUrls.getSchemaDetail,
            params: {
                entityTypeName: bomType,
            },
        });
        dispatch(setDecompositionSchema(data));
    }
};

const isRevisionEntity = (hasMasterRelation: any) => {
    return hasMasterRelation && isOutgoingRelation(hasMasterRelation);
};

export const removeGlobalAlternateAction = (toEntityId, selectedRows) => async (dispatch) => {
    try {
        dispatch(setLoadingDeleteGlobalAlternate(true));
        const res = await Promise.all(
            selectedRows.map((row) => {
                return apiFetch({
                    ...entityUrls.deleteRelation,
                    params: {
                        fromEntityId: row.id,
                        toEntityId,
                        relationType: 'ALTERNATES_TO',
                    },
                });
            })
        );

        if (res.every((r: any) => r.status === 204)) {
            notifySuccess(<span>{commonMessages.removeGlobalAlternateSuccess}</span>);
        }

        return true;
    } catch (e) {
        return false;
    } finally {
        dispatch(setLoadingDeleteGlobalAlternate(false));
    }
};

export const fetchEntityLifecycles = (entityTypeName: string) => async (dispatch) => {
    try {
        dispatch(onGettingEntityLifecycles(true));
        const result = await apiFetch({
            ...schemaUrls.fetchEntityLifecycle,
            params: { entityTypeName },
        });
        dispatch(storeEntityLifecycles(result.data.data));
        return result;
    } catch {
        return false;
    } finally {
        dispatch(onGettingEntityLifecycles(false));
    }
};

export const deleteEntity = (entity: EntityDetail) => async (dispatch) => {
    try {
        dispatch(setLoadingOverlay(true));
        if (isReport(entity?.properties?.type)) {
            await apiFetch({
                ...reportingUrls.deleteReport,
                params: { reportId: entity.id },
                skipToast: true,
                successMessage: `Successfully deleted the report <b>${entity?.properties?.name}</b>`,
            });
        } else {
            await apiFetch({
                ...entityUrls.deleteEntity,
                params: { entityId: entity.id },
                skipToast: true,
                successMessage: `Successfully deleted the entity <b>${entity?.properties?.name}</b>`,
            });
        }
        return true;
    } catch (e) {
        return false;
    } finally {
        dispatch(setLoadingOverlay(true));
    }
};

export const generateEBomFromCadBom = (entity: EntityDetail) => async (dispatch) => {
    try {
        dispatch(setLoadingOverlay(true));
        const { data } = await apiFetch({
            ...entityUrls.generateEBom,
            params: { entityId: entity.id },
            successMessage: `Successfully generate <b>EBOM</b>`,
        });
        return data;
    } catch (e) {
        return false;
    } finally {
        dispatch(setLoadingOverlay(false));
    }
};

export const checkoutDocument = async (id: string, extensions?: string, fileId?: string) => {
    try {
        const queryParams = {
            fileId,
            extensions,
        };
        const result = await apiFetch({ ...assetServiceUrl.documentCheckout, params: { id }, qs: queryParams });
        return result;
    } catch (err) {
        return false;
    }
};

export const checkinDocument = async (entityId: string, request) => {
    try {
        const result = await apiFetch({
            ...assetServiceUrl.checkinDocument,
            params: { id: entityId },
            data: request,
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return {
            data: result,
            error: null,
        };
    } catch (e) {
        return {
            data: null,
            error: e,
        };
    } finally {
    }
};

export const createEntity = async (entityType: string, data, relations: any, lifeCycle) => {
    try {
        const result = await apiFetch({
            ...entityUrls.createEntity,
            params: { entityType },
            data: { attributes: data, ...(relations ? { relations } : {}), ...(lifeCycle ? { lifeCycle } : {}) },
        });
        return {
            result,
            error: null,
        };
    } catch (error) {
        return {
            result: null,
            error,
        };
    } finally {
    }
};

export const getDocumentFiles = async (entityId: string) => {
    try {
        const result = await apiFetch({
            ...assetServiceUrl.getAllDocument,
            params: {
                id: entityId,
            },
            skipToast: true,
        });
        return {
            result,
            error: null,
        };
    } catch (error) {
        return {
            result: null,
            error,
        };
    } finally {
    }
};

/*
 * TODO: check why the relation is not return from backend
 */
export const getEngineeringRevisionOfRelation = async (bomType, entityId, signal) => {
    try {
        if ([SYSTEM_ENTITY_TYPE.CAD_BOM, SYSTEM_ENTITY_TYPE.ENGINEERING_BOM].includes(bomType)) {
            const { data } = await apiFetch({
                ...entityUrls.getAllRelationsUnderEntity,
                params: { entityId },
                qs: {
                    limit: 1,
                    relationNames: [SYSTEM_RELATION.ENGINEERING_REVISION_OF],
                },
                signal,
            });
            return get(data, ['data', '0', 'relation']);
        }
    } catch (err) {
        console.error(err);
        return null;
    } finally {
    }
    return null;
};

export const getPreviewFile = async (id: string, signal?) => {
    try {
        const { data } = await apiFetch({
            ...assetServiceUrl.previewFile,
            params: {
                id,
            },
            signal,
        });
        return data;
    } catch (error) {
        return false;
    } finally {
    }
};
