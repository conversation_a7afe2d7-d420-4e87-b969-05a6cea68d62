/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, Typography, Grid, Link as MuiLink, styled, IconButton, ButtonGroup, Button } from '@mui/material';
import { useCallback, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { SYSTEM_RELATION, entityUrls, fetch as apiFetch, EntityDetail, fetch } from '@tripudiotech/api';
import { Loading, AnimatedPage, getStatus, EntitySelect, notifyError } from '@tripudiotech/styleguide';
import RevisionField from './RevisionField';
import Status from './Status';
import EntityProperty from './EntityProperty';
import { useSchemaTree } from '@tripudiotech/caching-store';
import { EditIcon } from '../icons/icons';
import { Cancel, Done } from '@mui/icons-material';

const Container = styled(Box)(({ theme }) => ({
    padding: '16px 12px',
    backgroundColor: theme.palette.glide.background.normal.blue2,
    display: 'flex',
    flexDirection: 'column',
    minHeight: '80px',
    marginBottom: '16px',
}));

export const RelateOnItem = ({
    relationResponse,
    detailEntity,
    label,
    onEdit = () => {},
    additionalQuery = {},
}: {
    relationResponse: Record<string, any>;
    detailEntity: EntityDetail;
    label?: string;
    onEdit?: () => void;
    additionalQuery?: Record<string, any>;
}) => {
    const { schemaTreeMap } = useSchemaTree();
    const [editingItem, setEditingItem] = useState<Record<string, any>>(null);
    const [submitting, setSubmitting] = useState(false);
    const relateOnItem = relationResponse.relation;
    const status = relateOnItem?.state ? getStatus(relateOnItem.state) : null;
    const relationType = schemaTreeMap?.[detailEntity?.properties?.type]?.relations?.find(
        (rel) => rel.name === relationResponse.name
    );
    const relationDisplayName = label ? label : relationType?.displayName || relationResponse.name;

    if (!relateOnItem) {
        <Container
            sx={{
                backgroundColor: (theme) => theme.palette.glide.background.normal.blue2,
                display: 'flex',
                flexDirection: 'column',
                minHeight: '102px',
                paddingBottom: 0,
            }}
        >
            <Button>Add New</Button>
        </Container>;
    }
    return (
        relateOnItem && (
            <Container
                sx={{
                    backgroundColor: (theme) => theme.palette.glide.background.normal.blue2,
                    display: 'flex',
                    flexDirection: 'column',
                    minHeight: '102px',
                    paddingBottom: 0,
                }}
                key={relateOnItem.id}
            >
                <AnimatedPage>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography
                            sx={{
                                fontSize: '14px',
                                fontWeight: 600,
                                color: (theme) => theme.palette.glide.text.tertiary,
                                lineHeight: '18px',
                                display: 'flex',
                                gap: '8px',
                            }}
                        >
                            {editingItem?.id !== relateOnItem.id && relationDisplayName}
                            {editingItem?.id === relateOnItem.id ? (
                                <>
                                    {relationType?.toEntityType && !submitting ? (
                                        <>
                                            <Box sx={{ width: 300 }}>
                                                <EntitySelect
                                                    additionalQuery={additionalQuery}
                                                    label={relationDisplayName}
                                                    entityType={relationType?.toEntityType}
                                                    onChange={(newValue) => {
                                                        setEditingItem({
                                                            ...editingItem,
                                                            newValue: (newValue as { value: string; label: string })
                                                                ?.value,
                                                        });
                                                    }}
                                                    defaultOptions={[
                                                        { label: relateOnItem.name, value: relateOnItem.id },
                                                    ]}
                                                    defaultValue={relateOnItem.id}
                                                    isMulti={!relationType?.singleRelation}
                                                    isRequired
                                                    name={relationType?.name}
                                                />
                                            </Box>
                                            <ButtonGroup>
                                                <IconButton
                                                    onClick={() => {
                                                        setSubmitting(true);
                                                        fetch({
                                                            ...entityUrls.replaceRelation,
                                                            params: {
                                                                fromEntityId: detailEntity.id,
                                                                toEntityId: editingItem.id,
                                                                relationName: relationType?.name,
                                                            },
                                                            successMessage: `Successfully updated ${relationDisplayName}`,
                                                            data: {
                                                                newToEntityId: editingItem.newValue,
                                                            },
                                                        }).then(() => {
                                                            onEdit();
                                                            setSubmitting(false);
                                                            setEditingItem(null);
                                                        });
                                                    }}
                                                >
                                                    <Done />
                                                </IconButton>
                                                <IconButton onClick={() => setEditingItem(null)}>
                                                    <Cancel />
                                                </IconButton>
                                            </ButtonGroup>
                                        </>
                                    ) : (
                                        <Loading sxProps={{ height: '40px' }} />
                                    )}
                                </>
                            ) : (
                                <MuiLink
                                    sx={{
                                        color: (theme) => theme.palette.info.main,
                                        textDecoration: 'none',
                                    }}
                                    to={`/detail/${relateOnItem.type}/${relateOnItem.id}/properties`}
                                    component={Link}
                                >
                                    {relateOnItem.name}
                                </MuiLink>
                            )}
                        </Typography>
                        {!editingItem && (
                            <IconButton
                                onClick={() => setEditingItem(relateOnItem)}
                                sx={{ height: '16px', width: '16px' }}
                            >
                                <EditIcon />
                            </IconButton>
                        )}
                    </Box>

                    <Grid container mt="8px">
                        <Grid item xs={4}>
                            <RevisionField label="Revision" value={relateOnItem?.revision} />
                        </Grid>
                        <Grid item xs={4}>
                            <Status label={relateOnItem?.state?.name} status={status?.status} />
                        </Grid>
                        <Grid item xs={4}>
                            <EntityProperty label="Type">
                                <Typography variant="bo1">
                                    {relateOnItem?.type
                                        ? schemaTreeMap?.[relateOnItem.type]?.displayName || relateOnItem.type
                                        : '-'}
                                </Typography>
                            </EntityProperty>
                        </Grid>
                    </Grid>
                </AnimatedPage>
            </Container>
        )
    );
};

const RelationAttribute = ({
    detailEntity = null,
    defaultItem = null,
    label,
    relationNames = [SYSTEM_RELATION.REPORTED_ON],
}: {
    detailEntity?: EntityDetail;
    defaultItem?: Record<string, any>;
    label?: string;
    relationNames?: string[];
}) => {
    const [relateOnItems, setRelateOnItems] = useState<Array<Record<string, any>>>([defaultItem]);
    const [isLoaded, setIsLoaded] = useState(Boolean(defaultItem));

    const fetchRelateOnItems = useCallback(
        async (detailEntity: EntityDetail) => {
            setIsLoaded(false);
            try {
                const { data } = await apiFetch({
                    ...entityUrls.getAllRelationsUnderEntity,
                    params: {
                        entityId: detailEntity.id,
                    },
                    qs: {
                        relationNames: relationNames,
                    },
                    skipToast: true,
                });
                setRelateOnItems(data?.data);
            } catch (err) {
                console.log('Error fetching relate on items: ' + err);
                notifyError('There was an error fetching these relations: [' + relationNames.join(', ') + ']');
            } finally {
                setIsLoaded(true);
            }
        },
        [relationNames]
    );

    useEffect(() => {
        if (!defaultItem && detailEntity) {
            fetchRelateOnItems(detailEntity);
        }
    }, [detailEntity, defaultItem]);

    return isLoaded ? (
        relateOnItems?.map((relationResponse) => {
            return (
                <RelateOnItem
                    detailEntity={detailEntity}
                    label={label}
                    onEdit={() => fetchRelateOnItems(detailEntity)}
                    relationResponse={relationResponse}
                    key={relationResponse?.id}
                />
            );
        })
    ) : (
        <Container sx={{ justifyContent: 'center', alignItems: 'center', minHeight: '102px' }}>
            <Loading sxProps={{ height: '40px' }} />
        </Container>
    );
};

export default RelationAttribute;
