/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useRef, useMemo, useCallback, useState, useEffect } from 'react';
import { Box, Typography, styled, Button, type BoxProps } from '@mui/material';
import {
    MainTooltip,
    commonMessages,
    PlusIcon,
    Loading,
    notifyError,
    notifySuc<PERSON>,
    checkingPermission,
    tableStyles,
    MinusIcon,
} from '@tripudiotech/styleguide';
import { useAuth, useSchemaTree } from '@tripudiotech/caching-store';
import {
    entityUrls,
    SYSTEM_RELATION,
    SYSTEM_ENTITY_TYPE,
    batchRequestBody,
    DEFAULT_CLIENT_SIDE_LIMIT,
    fetch,
    Method,
    EntityDetail,
} from '@tripudiotech/api';
import get from 'lodash/get';
import { AgGridReact } from '@ag-grid-community/react';
import { SUB_RELATION_COMPONENT } from '../../utils/helper';
import { batchRequest, onOpenModal } from '../../actions';
import AddRelations from '../Relations/AddRelations';
import useToggle from '../../hooks/useToggle';
import CreateEntityRelation from '../../views/relations/CreateEntityRelation';
import { useDispatch } from 'react-redux';
import OverflowMenu from '../Navigation/OverflowMenu';
import { COLUMN_DEFINITIONS, OPEN_ADD_NEW, RELATION_TO_ENTITY_TYPE, TITLE } from '../../constants/issue-relations';
import { ColDef, GridOptions } from '@ag-grid-community/core';
import NoRowsOverlay from '../NoRowsOverlay/NoRowsOverlay';

type ActionItem = {
    label?: string;
    icon?: any;
    onClick: () => void;
    title: string;
    disabled: boolean;
};

const Wrapper = styled(Box)(({ theme }) => ({
    '& .headerSection': {
        display: 'flex',
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
        '& .desktopActions': {
            [theme.breakpoints.down('lg')]: {
                display: 'none',
            },
        },
        '& .tabletActions': {
            display: 'none',
            [theme.breakpoints.down('lg')]: {
                display: 'flex',
            },
        },
        padding: '16px',
        paddingBottom: 0,
    },
    '& .sectionTitle': {
        fontSize: '14px',
        fontWeight: 600,
        lineHeight: '23px',
        letterSpacing: '-0.4px',
        textTransform: 'uppercase',
        color: theme.palette.glide.text.normal.inverseSecondary,
    },
    '& .sectionContent': {
        marginTop: '12.5px',
        height: '200px',
        width: '100%',
        backgroundColor: '#F9F9F9',
    },
}));

export const openAddNewCR = (relationType) => {
    window.dispatchEvent(new CustomEvent(OPEN_ADD_NEW, { detail: relationType }));
};

const GRID_OPTIONS: GridOptions = {
    loadingOverlayComponent: Loading,
    defaultColDef: {
        sortable: true,
        resizable: true,
        flex: 1,
        filter: true,
        autoHeight: true,
        wrapText: true,
    },
    headerHeight: 32,
    rowHeight: 32,
    getRowId: (params) => {
        return params.data.id;
    },

    rowStyle: {
        background: '#FFFFFF',
    },
    rowSelection: 'multiple' as any,
    suppressMenuHide: true,
};

interface EntityRelationProps {
    detailEntity: EntityDetail;
    relationType: SYSTEM_RELATION;
    containerProps?: BoxProps;
    addExistingLabel?: string;
    createNewLabel?: string;
    addIcon?: JSX.Element;
    canCreateNew?: boolean;
    canRemove?: boolean;
    columnDefs?: ColDef[];
    createNewIcon?: JSX.Element;
    readOnly?: boolean;
    getData?: () => Promise<Array<Record<string, any>>>;
    title?: string;
}

const EntityRelation = ({
    detailEntity,
    relationType,
    containerProps = {},
    addExistingLabel = 'Add',
    createNewLabel = 'Create',
    addIcon = null,
    canCreateNew = false,
    canRemove = true,
    columnDefs = null,
    createNewIcon = null,
    readOnly = false,
    getData = null,
    title,
}: EntityRelationProps) => {
    const gridRef = useRef<AgGridReact>();
    const [openAddNew, addNewAction] = useToggle(false);
    const [openAddRelations, addRelationsAction] = useToggle(false);
    const userInfo = useAuth((state) => state.userInfo);
    const [data, setData] = useState<Array<Record<string, any>>>();
    const dispatch = useDispatch();
    const [errorFetchingData, setErrorFetchingData] = useState(false);
    const { schemaTreeMap } = useSchemaTree();

    const getTableData = async () => {
        try {
            if (getData) {
                const result = await getData();
                setData(result);
                return;
            }

            const {
                data: { data: result },
            } = await fetch({
                ...entityUrls.getEntityRelations,
                params: {
                    fromEntityId: detailEntity.id,
                    relationType,
                    entityType: RELATION_TO_ENTITY_TYPE[relationType],
                },
                qs: { limit: DEFAULT_CLIENT_SIDE_LIMIT },
                skipToast: true,
            });
            setData(result);
        } catch (err) {
            setErrorFetchingData(true);
            setData([]);
        } finally {
        }
    };

    const openCreateNewListener = useCallback((e) => {
        if (e.detail === relationType) {
            addNewAction.open();
        }
    }, []);

    useEffect(() => {
        getTableData();

        window.addEventListener(OPEN_ADD_NEW, openCreateNewListener);

        return () => {
            window.removeEventListener(OPEN_ADD_NEW, openCreateNewListener);
        };
    }, []);

    const onAddRelations = async (
        properties: Record<string, any>,
        selectedRows: Array<Record<string, any>>,
        successCallback: () => void
    ): Promise<void> => {
        const params = selectedRows.map((row) => ({
            subParams: {
                fromEntityId: detailEntity.id,
                relationType: relationType,
                toEntityId: row.id,
            },
        }));
        const requests = batchRequestBody(Method.POST, `/entity/:fromEntityId/:relationType/:toEntityId`, params);
        const { data } = await batchRequest(requests);
        if (data.some((res) => !res.success || res.status != 200)) {
            notifyError(`An error occurred while adding ${TITLE[relationType]}`);
        } else {
            successCallback();
            await getTableData();
            notifySuccess(`Successfully added ${TITLE[relationType]}`);
        }
    };

    const isBtnDisabled = useMemo(() => {
        const isLocked = get(detailEntity, 'properties.isLocked', false);
        const canModify = get(detailEntity, 'permissions.canConnectAsFromSide', false);

        if (isLocked) return checkingPermission(detailEntity, userInfo, 'canConnectAsFromSide');

        return canModify;
    }, [detailEntity, userInfo]);

    const removeRelations = useCallback(
        async (selectedRows: any[]) => {
            try {
                gridRef.current.api.showLoadingOverlay();
                await Promise.all(
                    selectedRows.map((row) =>
                        fetch({
                            ...entityUrls.deleteRelation,
                            params: {
                                fromEntityId: detailEntity.id,
                                toEntityId: row.id,
                                relationType,
                                skipToast: true,
                            },
                        })
                    )
                );
                await getTableData();
                notifySuccess('Successfully removed selected items');
            } catch (error) {
                notifyError('An error occurred while removing selected rows');
            } finally {
                gridRef.current.api.hideOverlay();
            }
        },
        [detailEntity]
    );

    const onRemove = useCallback(() => {
        const selectedRows = gridRef.current.api.getSelectedRows();
        if (selectedRows.length > 0) {
            dispatch(
                onOpenModal({
                    title: `Remove selected ${TITLE[relationType]}`,
                    open: true,
                    content: `Are you sure you want to remove selected rows from <b>${get(detailEntity, [
                        'properties',
                        'name',
                    ])}</b> ${TITLE[relationType]}?`,
                    onModalNextAction: () => removeRelations(selectedRows),
                    action: 'confirm',
                    confirmColor: 'error',
                })
            );
        }
    }, [removeRelations, relationType]);

    const actionItems: ActionItem[] = useMemo(() => {
        const defaultTitle = isBtnDisabled ? '' : commonMessages.noPermission;
        let actions = [
            {
                label: addExistingLabel,
                icon: addIcon || <PlusIcon />,
                onClick: addRelationsAction.open,
                title: defaultTitle,
                disabled: !isBtnDisabled,
            },
        ];

        if (canCreateNew) {
            actions.push({
                label: createNewLabel,
                icon: createNewIcon || <PlusIcon />,
                onClick: addNewAction.open,
                title: defaultTitle,
                disabled: !isBtnDisabled,
            });
        }
        if (canRemove) {
            actions.push({
                label: 'Remove',
                icon: <MinusIcon />,
                onClick: onRemove,
                title: defaultTitle,
                disabled: !isBtnDisabled,
            });
        }

        return actions;
    }, [addExistingLabel, addIcon, isBtnDisabled, canCreateNew, createNewIcon, onRemove]);

    return (
        <Wrapper {...containerProps}>
            <div className="headerSection">
                <Typography variant="h2" className="sectionTitle">
                    {title ? title.toUpperCase() : TITLE[relationType]}
                </Typography>

                {!readOnly && (
                    <>
                        <div className="desktopActions">
                            {actionItems.map(({ disabled, title, label, icon, onClick }, idx) => (
                                <MainTooltip key={`action-item-${idx}`} title={title}>
                                    <span>
                                        <Button
                                            disabled={disabled}
                                            variant="ghost"
                                            size="small"
                                            onClick={onClick}
                                            endIcon={icon}
                                        >
                                            {label}
                                        </Button>
                                    </span>
                                </MainTooltip>
                            ))}
                        </div>
                        <div className="tabletActions">
                            <OverflowMenu items={actionItems} />
                        </div>
                    </>
                )}
            </div>
            <div className="sectionContent">
                <Box
                    className="ag-theme-alpine"
                    sx={{
                        height: '100%',
                        width: '100%',
                        position: 'relative',
                        '.dot': { display: 'none' },
                        overflow: 'auto',
                        ...tableStyles,
                    }}
                >
                    <AgGridReact
                        className="ag-theme-alpine"
                        ref={gridRef}
                        animateRows={true}
                        rowModelType={'clientSide'}
                        rowData={data}
                        noRowsOverlayComponent={NoRowsOverlay}
                        columnDefs={columnDefs || COLUMN_DEFINITIONS[relationType]}
                        noRowsOverlayComponentParams={{
                            message: errorFetchingData
                                ? `There was an error fetching the ${
                                      title || TITLE[relationType] || 'Entity'
                                  } associated with this entity`
                                : `There's no ${title || TITLE[relationType] || 'Entity'} associated with this entity`,
                            error: errorFetchingData,
                        }}
                        context={{
                            schemaTreeMap: schemaTreeMap,
                        }}
                        {...GRID_OPTIONS}
                    />
                </Box>
            </div>
            <AddRelations
                open={openAddRelations}
                onClose={addRelationsAction.close}
                entityType={RELATION_TO_ENTITY_TYPE[relationType]}
                dragContainerId="attributes-view"
                onSubmit={onAddRelations}
                title={`Add ${TITLE[relationType]}`}
                excludedIds={detailEntity ? [detailEntity.id] : []}
                includeRevision={relationType !== SYSTEM_RELATION.IMPLEMENTS}
            />
            <CreateEntityRelation
                detailEntity={detailEntity}
                open={openAddNew}
                onClose={addNewAction.close}
                onSuccess={() => {
                    getTableData();
                    addNewAction.close();
                }}
                relationType={SYSTEM_RELATION.RESOLVED_BY}
                rootEntityType={SYSTEM_ENTITY_TYPE.CHANGE_REQUEST}
                title="Create Change Request for this Issue"
                successMessage={SUB_RELATION_COMPONENT.ISSUE.addResolvedBySuccess}
                isAbsoluteType
            />
        </Wrapper>
    );
};

export default EntityRelation;
