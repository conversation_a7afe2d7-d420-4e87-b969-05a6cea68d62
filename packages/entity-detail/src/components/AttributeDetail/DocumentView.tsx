/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useState, useCallback, useMemo, useRef } from 'react';
import { Button, Typography, styled } from '@mui/material';
import {
    notifySuc<PERSON>,
    notify<PERSON>rror,
    UploadIcon,
    DownloadIcon,
    LoadingOverlay,
    AnimatedPage,
    EmptyDocument,
} from '@tripudiotech/styleguide';
import OverflowMenu from '../Navigation/OverflowMenu';
import { checkinDocument, checkoutDocument, onOpenModal, onResetModal } from '../../actions';
import { useDispatch } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import DocumentPreview from './DocumentPreview';
import { buildCheckInForm, assetServiceUrl, fetch as apiFetch, EntityDetail, downloadFile } from '@tripudiotech/api';
import Checkbox from '@mui/material/Checkbox';
import Box from '@mui/material/Box';
import FormControlLabel from '@mui/material/FormControlLabel';
import { CUSTOM_EVENTS } from '../../constants/events';

const Wrapper = styled('div')(({ theme }) => ({
    height: '100%',
    padding: '10px',
    paddingTop: '0px',
    [theme.breakpoints.down('md')]: {
        height: '78vh',
    },
    '& .header': {
        display: 'flex',
        alignItems: 'center',
        marginTop: '12.5px',
    },
    '& .preview': {
        display: 'block',
    },
    '& .title': {
        marginBottom: '10px',
    },
    '& .actions': {
        marginLeft: 'auto',
        display: 'flex',
        gap: '8px',
        marginBottom: 'auto',
    },
    '& .desktopOnly': {
        [theme.breakpoints.down('lg')]: {
            display: 'none',
        },
    },
    '& .overflowMenu': {
        [theme.breakpoints.up('lg')]: {
            display: 'none',
        },
    },
    '& .documentContainer': {
        height: 'calc(100% - 64px)',
        marginTop: '16px',
        backgroundColor: theme.palette.glide.background.normal.white,
    },
    '& .documentMsg': {
        fontSize: '14px',
        fontWeight: 500,
        color: theme.palette.glide.text.normal.inverseTertiary,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '16px 32px',
        textAlign: 'center',
    },
    '& .actionIcon': { marginLeft: '24px' },
    '& .fullscreen': {
        width: '100%',
        height: '100%',
    },
    '& .selectedFile': {
        border: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
        padding: '5px 9px',
        display: 'flex',
        gap: '8px',
        '& .label': {
            color: theme.palette.glide.text.normal.inversePrimary,
            fontSize: '14px',
            fontWeight: 400,
        },
        '& .fileName': {
            color: theme.palette.glide.text.normal.inverseTertiary,
            fontSize: '14px',
            fontWeight: 500,
        },
    },
}));

const overflowItemStyle = {
    display: 'flex',
    gap: '16px',
    justifyContent: 'space-between',
    '&:hover': {
        backgroundColor: '#E5E5E5',
    },
    '&:focus': {
        border: '2px solid #0F62FE',
    },
};

interface UploadActionProps {
    checkIn: (override: string, file: Blob, primary?: string) => Promise<void>;
    file: Blob;
    existing: boolean;
}

const UploadAction = ({ checkIn, file, existing }: UploadActionProps) => {
    const dispatch = useDispatch();
    const [primary, setPrimary] = useState(false);
    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
            }}
        >
            {existing && (
                <FormControlLabel
                    control={<Checkbox checked={primary} onClick={() => setPrimary((prev) => !prev)} />}
                    label="Make as primary file"
                />
            )}
            {existing ? (
                <Box sx={{ display: 'flex', gap: '8px' }}>
                    <Button
                        sx={{ width: 220, justifyContent: 'flex-start' }}
                        onClick={() => {
                            dispatch(onResetModal());
                        }}
                        size="large"
                        variant="contained"
                        color="secondary"
                    >
                        Cancel
                    </Button>
                    <Button
                        sx={{ width: 220, justifyContent: 'flex-start' }}
                        onClick={() => {
                            checkIn('true', file, String(primary));
                            dispatch(onResetModal());
                        }}
                        size="large"
                        variant="contained"
                        color="primary"
                    >
                        Confirm
                    </Button>
                </Box>
            ) : (
                <Box sx={{ display: 'flex', gap: '8px' }}>
                    <Button
                        sx={{ width: 220, justifyContent: 'flex-start' }}
                        onClick={() => {
                            checkIn('false', file, 'false');
                            dispatch(onResetModal());
                        }}
                        size="large"
                        variant="contained"
                        color="secondary"
                    >
                        Upload only
                    </Button>
                    <Button
                        sx={{ width: 220, justifyContent: 'flex-start' }}
                        onClick={() => {
                            checkIn('true', file, 'true');
                            dispatch(onResetModal());
                        }}
                        size="large"
                        variant="contained"
                        color="primary"
                    >
                        Upload as primary file
                    </Button>
                </Box>
            )}
        </Box>
    );
};

interface DocumentViewProps {
    detailEntity: EntityDetail;
    selectedFile: Record<string, any>;
    isFileLoading: boolean;
    files: Array<Record<string, any>>;
    isFileView: boolean;
    disableUpload?: boolean;
}

const DocumentView: React.FC<DocumentViewProps> = ({
    detailEntity,
    selectedFile,
    isFileLoading,
    files,
    isFileView = false,
    disableUpload = false,
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const editorRef = useRef(null);
    const inputFile = useRef(null);
    const [isEditing, setIsEditing] = useState(false);
    const dispatch = useDispatch();

    const onDownloadDocument = async () => {
        setIsLoading(true);
        const {
            id,
            properties: { name },
        } = selectedFile;
        const res = await checkoutDocument(detailEntity.id, get(selectedFile, 'properties.extension'));
        if (res) {
            const { downloadUrl } = res.data.fileResponses[id];
            downloadFile(downloadUrl, name);
            setIsLoading(false);
        }
    };

    const generateDownloadUrl = async (fileId: string) => {
        try {
            const { data }: any = await apiFetch({
                ...assetServiceUrl.downloadFile,
                params: {
                    fileId,
                },
            });

            return {
                data,
            };
        } catch (err) {
            return { error: err };
        }
    };

    const handleDownLoadFile = async () => {
        try {
            setIsLoading(true);
            const {
                id,
                properties: { name },
            } = selectedFile;
            const { data } = await generateDownloadUrl(id);
            if (data) {
                const { downloadUrl } = data;
                downloadFile(downloadUrl, name);
                return;
            }

            notifyError('Unable to download file. Please try again.');
        } catch (e) {
            notifyError('Unable to download file. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    const checkInNewFile = useCallback(async (override: string, file: File, primary?: string) => {
        setIsLoading(true);
        const request = buildCheckInForm({ file, override, primary });
        const { data } = await checkinDocument(detailEntity.id, request);
        if (data) {
            window.dispatchEvent(
                new CustomEvent(CUSTOM_EVENTS.REFRESH_ATTACHMENTS_TABLE, {
                    detail: get(data, 'data.id'),
                })
            );
            notifySuccess('Uploaded the file sucessfully');
        }
        setIsLoading(false);
    }, []);

    const onUpload = useCallback(
        (e) => {
            //@ts-ignore
            const inputFiles = document.querySelector(`#file-input`).files;
            if (!isEmpty(inputFiles)) {
                const file = inputFiles[0];
                const fileParts = file.name.split('.');
                const extension = fileParts.length >= 1 ? fileParts[fileParts.length - 1] : '';
                const existingExtension: boolean = files.some(
                    (existingFile) => existingFile.properties?.extension === (extension || '').toLowerCase()
                );
                dispatch(
                    onOpenModal({
                        open: true,
                        title: 'Upload document',
                        content: existingExtension
                            ? `Uploading a new file will override the existing file. Do you want to proceed?`
                            : `Do you want to make this file as primary file of the entity?`,
                        actionComponent: (
                            <UploadAction checkIn={checkInNewFile} existing={existingExtension} file={file} />
                        ),
                    })
                );
            }
        },
        [files]
    );

    const onSelectNewFile = useCallback(() => {
        inputFile.current.click();
    }, []);

    const overflowItems = useMemo(() => {
        return [
            ...(disableUpload
                ? [
                      {
                          label: 'Upload',
                          icon: <UploadIcon />,
                          style: overflowItemStyle,
                          onClick: onSelectNewFile,
                          validatePermissions: () => {
                              //TODO: validate permissions
                              return true;
                          },
                      },
                  ]
                : []),
            {
                label: 'Download',
                icon: <DownloadIcon />,
                style: overflowItemStyle,
                onClick: isFileView ? handleDownLoadFile : onDownloadDocument,
            },
        ];
    }, [onUpload, onDownloadDocument, handleDownLoadFile, isFileView]);

    return (
        <>
            {(isFileLoading || isLoading) && <LoadingOverlay />}
            {!isFileLoading && (
                <AnimatedPage style={{ height: '100%', width: '100%', display: 'flex', flexDirection: 'column' }}>
                    <Wrapper>
                        <div className="header">
                            <div className="preview">
                                {files?.length > 0 && !isFileView && (
                                    <div className="selectedFile">
                                        <Typography className="label">File:</Typography>
                                        <Typography className="fileName">
                                            {get(selectedFile, ['properties', 'name'])}
                                        </Typography>
                                    </div>
                                )}
                                <Typography variant="ol1" className="title">
                                    Preview {!isFileView ? 'Document' : 'File'}
                                </Typography>
                            </div>
                            <div className="actions">
                                <input
                                    type="file"
                                    id="file-input"
                                    value=""
                                    hidden
                                    ref={inputFile}
                                    onChange={onUpload}
                                />
                                {!isFileView && !disableUpload && (
                                    <Button
                                        className="desktopOnly"
                                        size="small"
                                        variant="contained"
                                        color="secondary"
                                        endIcon={<UploadIcon className="actionIcon" />}
                                        onClick={onSelectNewFile}
                                    >
                                        Upload
                                    </Button>
                                )}
                                <Button
                                    className="desktopOnly"
                                    size="small"
                                    variant="contained"
                                    color="secondary"
                                    endIcon={<DownloadIcon className="actionIcon" />}
                                    onClick={isFileView ? handleDownLoadFile : onDownloadDocument}
                                >
                                    Download
                                </Button>
                                <div className="overflowMenu">
                                    <OverflowMenu items={overflowItems} />
                                </div>
                            </div>
                        </div>
                        <div className="documentContainer">
                            {files && files.length === 0 && (
                                <Box
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        height: '100%',
                                        flexDirection: 'column',
                                    }}
                                >
                                    <EmptyDocument />
                                    <Typography className="documentMsg">
                                        There is no file associated with this entity. Please upload a new file.
                                    </Typography>
                                </Box>
                            )}
                            {selectedFile && (
                                <DocumentPreview editorRef={editorRef} file={selectedFile} isEditing={isEditing} />
                            )}
                        </div>
                    </Wrapper>
                </AnimatedPage>
            )}
        </>
    );
};

export default DocumentView;
