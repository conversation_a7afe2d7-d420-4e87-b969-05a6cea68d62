/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import Typography from '@mui/material/Typography';
import EntityProperty, { EntityPropertyProps } from './EntityProperty';

const RevisionField = (props: { sx?: object; value } & EntityPropertyProps) => {
    const { label, value, sx = {} } = props;

    return (
        <EntityProperty
            label={label}
            sx={{
                a: {
                    marginLeft: value ? '12px' : 0,
                    fontSize: '12px',
                    fontWeight: 500,
                    letterSpacing: '-0.2px',
                    color: (theme) => theme.palette.info.main,
                },
                ...sx,
            }}
        >
            <Typography
                sx={{
                    fontSize: '14px',
                    fontWeight: 500,
                    lineHeight: '22px',
                    color: (theme) => theme.palette.success.main,
                }}
                className="value"
            >
                {value}
            </Typography>
        </EntityProperty>
    );
};

export default RevisionField;
