/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';

export type EntityPropertyProps = {
    label: string;
    helpText?: string;
    children?: React.ReactNode;
    sx?: object;
};

const EntityProperty = ({ label, helpText = '', sx = {}, children = null }: EntityPropertyProps) => {
    return (
        <Box className="detail-item" sx={{ marginBottom: '16px', ...sx }}>
            <Typography
                sx={{
                    color: (theme) => theme.palette.glide.text.normal.inversePrimary,
                    display: 'block',
                    mb: '2px',
                }}
                variant="label3-med"
            >
                {label}
            </Typography>

            {children}

            <Typography
                sx={{
                    fontSize: '10px',
                    fontWeight: 400,
                    lineHeight: '15px',
                    color: (theme) => theme.palette.glide.text.normal.inversePrimary,
                }}
            >
                {helpText}
            </Typography>
        </Box>
    );
};

export default EntityProperty;
