/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { DRAWER_COMPONENT_NAME, buildValidationSchema, NoResultIcon, ExpandMoreIcon } from '@tripudiotech/styleguide';
import { Formik, Form } from 'formik';
import { useRef, useCallback, memo, useMemo, useState, useEffect } from 'react';
import { buildFormItem } from '../../utils/attributeFormBuilder';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import RightTray from '../Relations/RightTray';
import { useDispatch } from 'react-redux';
import { onOpenModal } from '../../actions';
import { AccordionDetails, AccordionSummary, Typography } from '@mui/material';
import { StyledAccordion } from './ClassificationAttributes';
import { Attribute, AttributeType } from '@tripudiotech/api';

interface AttributeWithGroup extends Attribute {
    attributes: Attribute[];
}
const isValidAttribute = (attribute: Attribute) =>
    isEmpty(attribute.identifier) && attribute.visible && attribute.mutable;

const processGroupAttributes = (attribute: AttributeWithGroup) => ({
    ...attribute,
    attributes: attribute.attributes.filter(isValidAttribute),
});

const buildAttributeDefaultValue = (attribute, values) => {
    values.push([attribute.name, attribute.value]);
    if (!isEmpty(attribute.unitOfMeasure)) {
        values.push([
            `${attribute.name}:unit`,
            get(attribute, 'quantityUnit') || get(attribute, 'unitOfMeasure.quantityUnit', ''),
        ]);
    }
};
const EditPropertiesPanel = ({
    open,
    onClose,
    onSave,
    attributes,
    title,
    entityType = '',
    isClassification = false,
}) => {
    const ref = useRef(null);
    const dispatch = useDispatch();
    const [formDirty, setFormDirty] = useState(false);

    const allAttributes: AttributeWithGroup[] = useMemo(
        () =>
            attributes
                .filter((attribute) => attribute.type === AttributeType.GROUP || isValidAttribute(attribute))
                .map((attribute) =>
                    attribute.type === AttributeType.GROUP ? processGroupAttributes(attribute) : attribute
                ),
        [attributes]
    );

    const initialValues = useMemo(() => {
        const values = [];

        allAttributes.forEach((attribute) => {
            if (attribute.type === AttributeType.GROUP) {
                // Process group children
                attribute.attributes.forEach((attr) => buildAttributeDefaultValue(attr, values));
            }
            // Process the attribute itself
            buildAttributeDefaultValue(attribute, values);
        });

        return Object.fromEntries(values);
    }, [allAttributes]);

    const validationSchema = useMemo(() => buildValidationSchema(allAttributes), [allAttributes]);
    const handleSubmit = useCallback(
        (values, { setFieldError }) => {
            setFormDirty(false);
            if (ref.current.dirty) {
                onSave && onSave(values, { setFieldError });
                return;
            }
            onClose();
        },
        [onSave]
    );

    const handleClose = () => {
        if (formDirty) {
            dispatch(
                onOpenModal({
                    open: true,
                    title: 'Cancel Editing?',
                    content: `You haven't finished your update yet. Do you want to leave without finishing?`,
                    onModalNextAction: () => {
                        setFormDirty(false);
                        onClose();
                    },
                    confirmColor: 'info',
                })
            );
            return;
        }
        onClose();
    };

    const preventCloseTab = useCallback((ev) => {
        ev.preventDefault();
        return (ev.returnValue = 'Are you sure you want to close?');
    }, []);

    useEffect(() => {
        if (formDirty) {
            window.addEventListener('beforeunload', preventCloseTab);
        } else {
            window.removeEventListener('beforeunload', preventCloseTab);
        }
    }, [formDirty]);

    const handleOnChange = (e, values) => {
        setFormDirty(true);
    };

    const emptyAttributes = isEmpty(allAttributes);
    return (
        <RightTray
            title={title}
            componentName={DRAWER_COMPONENT_NAME.EDIT_PROPERTIES_PANEL}
            open={open}
            onClose={handleClose}
            onConfirm={() => {
                ref.current.submitForm();
            }}
            confirmText="Save changes"
            disabled={emptyAttributes}
        >
            {emptyAttributes ? (
                <Box
                    sx={{
                        display: 'flex',
                        height: '100%',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        textAlign: 'center',
                    }}
                >
                    <NoResultIcon sx={{ mt: '-64px' }} />
                    <Typography sx={{ mt: 2, fontSize: '16px', mx: 4 }}>
                        {isClassification ? 'Classification ' : 'Entity Type '}
                        <b>{entityType}</b> doesn't have any attributes. Please contact your administrators to setup
                        attributes for it.
                    </Typography>
                </Box>
            ) : (
                <Box sx={{ overflow: 'auto' }}>
                    <Formik
                        enableReinitialize
                        initialValues={initialValues}
                        validationSchema={validationSchema}
                        innerRef={ref}
                        onSubmit={(values, { setFieldError }) => {
                            handleSubmit(values, { setFieldError });
                        }}
                    >
                        {({ values, setFieldValue, ...rest }) => (
                            <Form onChange={(e) => handleOnChange(e, values)}>
                                <Grid container spacing={3} sx={{ padding: '24px' }}>
                                    {allAttributes &&
                                        allAttributes.map((attribute) =>
                                            attribute.type === AttributeType.GROUP ? (
                                                <Grid item xs={12} key={`${AttributeType.GROUP}-${attribute.name}`}>
                                                    <StyledAccordion defaultExpanded>
                                                        <AccordionSummary
                                                            className="summary"
                                                            expandIcon={
                                                                <ExpandMoreIcon
                                                                    sx={{
                                                                        width: '16px',
                                                                        height: '16px',
                                                                    }}
                                                                />
                                                            }
                                                        >
                                                            <Typography className="label" variant="label2-med">
                                                                {attribute.name}
                                                            </Typography>
                                                        </AccordionSummary>
                                                        <AccordionDetails className="accordionDetails">
                                                            <Grid container spacing={2}>
                                                                {attribute.attributes.map((groupAttr) =>
                                                                    buildFormItem({
                                                                        attribute: groupAttr,
                                                                        setFieldValue,
                                                                        attributes: allAttributes,
                                                                        values,
                                                                    })
                                                                )}
                                                            </Grid>
                                                        </AccordionDetails>
                                                    </StyledAccordion>
                                                </Grid>
                                            ) : (
                                                buildFormItem({
                                                    attribute,
                                                    setFieldValue,
                                                    attributes: allAttributes,
                                                    values,
                                                })
                                            )
                                        )}
                                </Grid>
                            </Form>
                        )}
                    </Formik>
                </Box>
            )}
        </RightTray>
    );
};
export default memo(EditPropertiesPanel);
