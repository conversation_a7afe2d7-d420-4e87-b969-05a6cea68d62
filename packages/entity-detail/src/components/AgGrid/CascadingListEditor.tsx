/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { CloseIcon } from '@tripudiotech/styleguide';
import { Attribute } from '@tripudiotech/api';
import { CascadingList } from '../AttributeDetail/CascadingList';
import {
    Button,
    Chip,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    IconButton,
    styled,
    Typography,
} from '@mui/material';

const StyledDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiPaper-root': {
        borderRadius: 0,
        width: '30%',
        height: '50%',
        minHeight: '500px',
        overflow: 'hidden',
        maxWidth: '100%',
    },
    '& .dialogHeader': {
        display: 'flex',
        justifyContent: 'space-between',
        padding: '8px 8px 8px 24px',
        height: '46px',
        alignItems: 'center',
        backgroundColor: theme.palette.glide.background.normal.tertiary,
        '& .title': {
            fontSize: '20px',
            lineHeight: '150%',
            fontWeight: 600,
            color: theme.palette.glide.text.white,
            margin: 0,
        },
        '& .subTitle': {
            color: theme.palette.glide.text.normal.tertiary,
            fontWeight: 400,
            fontSize: '12px',
        },
        '& .headerButton': {
            alignSelf: 'flex-start',
            color: theme.palette.glide.text.normal.tertiary,
        },
    },
    '& .cancelBtn': {
        width: '160px',
        justifyContent: 'flex-start',
    },
    '& .searchBtn': {
        width: '260px',
        justifyContent: 'flex-start',
    },
    '& .actions': {
        borderTop: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
    },
    '& .dialogContent': {
        margin: '24px 0',
        '& form': {
            '& .MuiPaper-root': {
                minWidth: '180px',
                width: 'max-content',
            },
        },
    },
    '& .MuiOutlinedInput-root': {
        height: 'min-content',
    },
}));

export const CascadingListEditor = forwardRef((props: any, ref) => {
    const [value, setValue] = useState(props.value || []);
    const { attribute }: { attribute: Attribute } = props;
    const [dialogOpen, setDialogOpen] = useState(true);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (containerRef.current) {
            containerRef.current.focus();
        }
    }, []);

    useImperativeHandle(ref, () => {
        return {
            getValue() {
                return value;
            },
            isCancelBeforeStart() {
                return false;
            },
            isCancelAfterEnd() {
                return false;
            },
        };
    });

    const handleClose = () => {
        setDialogOpen(false);
        // Delay to ensure dialog closes before stopping editing
        setTimeout(() => {
            props.stopEditing?.();
        }, 50);
    };

    return (
        <div
            ref={containerRef}
            style={{
                width: '100%',
                height: '100%',
                outline: 'none',
                display: 'flex',
                alignItems: 'center',
                padding: '0 8px',
            }}
        >
            {value?.slice(0, 2).map((item, index) => (
                <Chip key={`item-${index}`} label={item} size="small" sx={{ mr: 0.5 }} />
            ))}
            {value?.length > 2 && <span>+{value.length - 2} more</span>}

            <StyledDialog
                open={dialogOpen}
                onClose={(event, reason) => {
                    if (reason === 'escapeKeyDown') {
                        handleClose();
                    }
                }}
                maxWidth="md"
                fullWidth
                disableAutoFocus
                disableEnforceFocus
                disableRestoreFocus
                onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                }}
                onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                }}
            >
                <DialogTitle className="dialogHeader">
                    <Typography className="title">Edit {attribute?.displayName}</Typography>
                    <IconButton
                        className="headerButton"
                        onClick={handleClose}
                        sx={{ position: 'absolute', right: 8, top: 8 }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent sx={{ height: '100px' }} className="dialogContent">
                    <CascadingList
                        value={value}
                        onChange={setValue}
                        cascadingListItems={attribute?.constraint?.cascadingListItems}
                        displayName={attribute?.displayName}
                    />
                </DialogContent>
                <DialogActions sx={{ alignItems: 'center', px: '24px', my: '8px' }}>
                    <Button
                        sx={{ minWidth: 160, justifyContent: 'flex-start' }}
                        size="large"
                        variant="contained"
                        color="secondary"
                        onClick={() => setValue([])}
                    >
                        Reset
                    </Button>
                    <Button
                        sx={{ minWidth: 226, justifyContent: 'flex-start' }}
                        onClick={handleClose}
                        variant="contained"
                        size="large"
                        color={'info'}
                    >
                        Save
                    </Button>
                </DialogActions>
            </StyledDialog>
        </div>
    );
});

CascadingListEditor.displayName = 'CascadingListEditor';
