/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { TextField, Typography } from '@mui/material';
import { MainTooltip, useDebounce } from '@tripudiotech/styleguide';

/**
 * Generic select cell editor. To customize it, wrap it with a HOC component
 */
const InlineTextEditor = forwardRef((props: any, ref) => {
    const [value, setValue] = useState(props.value || '');
    const debouncedValue = useDebounce(value, 200);
    const [errors, setErrors] = useState(null);
    const hasErrors = errors && errors.length > 0;
    const refInput = useRef<HTMLInputElement>(null);
    const { validateConstraint } = props;

    useEffect(() => {
        setTimeout(() => {
            refInput.current?.focus();
        });
    }, []);

    useImperativeHandle(ref, () => {
        return {
            getValue() {
                return value;
            },
            isCancelBeforeStart() {
                return false;
            },
            isCancelAfterEnd() {
                return hasErrors;
            },
        };
    });

    const handleChange = useCallback(async (e) => {
        const value = e.target.value;
        setValue(value);
    }, []);

    useEffect(() => {
        if (validateConstraint) {
            validateConstraint(debouncedValue)
                .then(() => setErrors(null))
                .catch((err) => {
                    const errs = err.errors;
                    setErrors(errs);
                });
        }
    }, [debouncedValue]);

    const validationErrors = useMemo(() => {
        return hasErrors ? (
            <div style={{ padding: '6px' }}>
                <Typography sx={{ fontWeight: 700, fontSize: '12px' }}>Invalid value provided:</Typography>
                {errors.map((err, i) => (
                    <Typography key={`err-${i}`} sx={{ fontSize: '12px', opacity: 0.9 }}>
                        {err}
                    </Typography>
                ))}
            </div>
        ) : (
            ''
        );
    }, [errors, hasErrors]);

    return (
        <MainTooltip
            TransitionProps={{ timeout: 100 }}
            variant="error"
            open
            placement="bottom"
            title={validationErrors}
        >
            <TextField
                sx={{
                    fontSize: '14px',
                    '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#D3EEFF !important',
                        borderRadius: 0,
                        height: '45px',
                    },
                    textDecoration: 'none !important',
                }}
                inputRef={refInput}
                value={value}
                size="small"
                fullWidth
                onChange={handleChange}
            />
        </MainTooltip>
    );
});

InlineTextEditor.displayName = 'InlineTextEditor';

export default InlineTextEditor;
