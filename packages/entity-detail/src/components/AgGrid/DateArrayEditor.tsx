/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';

import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import { useSelector } from 'react-redux';
import { selectAppReducer } from '../../selectors';
import { Typography } from '@mui/material';
import { EntitySelect, MainTooltip } from '@tripudiotech/styleguide';
import { Attribute, AttributeType } from '@tripudiotech/api';
import { DateArrayInput, MultiValueInput } from '../AttributeDetail/ArrayAttribute';

export const DateArrayEditor = forwardRef((props: any, ref) => {
    const [value, setValue] = useState(props.value || []);
    const { attribute }: { attribute: Attribute } = props;
    const { name, type, displayName, description, nullable } = attribute as Attribute;

    const refInput = useRef(null);
    useEffect(() => {
        if (refInput.current) {
            refInput.current.focus();
        }
    }, [refInput]);

    useImperativeHandle(ref, () => {
        return {
            getValue() {
                return value;
            },
            isCancelBeforeStart() {
                return false;
            },
            isCancelAfterEnd() {
                return false;
            },
        };
    });

    return (
        <DateArrayInput
            isDateTime={attribute.type === AttributeType.DATE_TIME_ARRAY}
            displayName={''}
            value={value}
            onChange={(value) => setValue(value)}
            required={!Boolean(nullable)}
            helperText={description}
            wrapperSx={{ width: '100%' }}
            anchorEl={ref as any}
        />
    );
});

DateArrayEditor.displayName = 'DategArrayEditor';
