/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { TextField, Typography } from '@mui/material';
import { formatSystemDate, formatSystemDateTime, MainTooltip, useDebounce } from '@tripudiotech/styleguide';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AttributeType } from '@tripudiotech/api';
import { useGlobalConfig } from '@tripudiotech/caching-store';

/**
 * Generic select cell editor. To customize it, wrap it with a HOC component
 */
const DatePickerEditor = forwardRef((props: any, ref) => {
    const [value, setValue] = useState(props.value || '');
    const debouncedValue = useDebounce(value, 200);
    const [errors, setErrors] = useState<string[] | null>(null);
    const [open, setOpen] = useState(false);
    const hasErrors = errors && errors.length > 0;
    const refInput = useRef<HTMLInputElement>(null);
    const { validateConstraint, type } = props;

    useEffect(() => {
        if (refInput.current) {
            refInput.current.focus();
        }
    }, [refInput]);

    useImperativeHandle(ref, () => {
        return {
            getValue() {
                return value;
            },
            isCancelBeforeStart() {
                return false;
            },
            isCancelAfterEnd() {
                return hasErrors;
            },
        };
    });

    const handleChange = useCallback(async (value) => {
        setValue(type === AttributeType.DATE_TIME ? formatSystemDateTime(value) : formatSystemDate(value));
    }, []);

    useEffect(() => {
        if (validateConstraint) {
            validateConstraint(debouncedValue)
                .then(() => setErrors(null))
                .catch((err) => {
                    const errs = err.errors;
                    setErrors(errs);
                });
        }
    }, [debouncedValue]);

    const validationErrors = useMemo(() => {
        return hasErrors && !open ? (
            <div style={{ padding: '6px' }}>
                <Typography sx={{ fontWeight: 700, fontSize: '12px' }}>Invalid value provided:</Typography>
                {errors.map((err, i) => (
                    <Typography key={`err-${i}`} sx={{ fontSize: '12px', opacity: 0.9 }}>
                        {err}
                    </Typography>
                ))}
            </div>
        ) : (
            ''
        );
    }, [errors, hasErrors, open]);

    return (
        <MainTooltip
            TransitionProps={{ timeout: 100 }}
            variant="error"
            open
            placement="bottom"
            title={validationErrors}
        >
            <span>
                <LocalizationProvider dateAdapter={AdapterMoment}>
                    {type === AttributeType.DATE_TIME ? (
                        <DateTimePicker
                            open={open}
                            onOpen={() => setOpen(true)}
                            onClose={() => setOpen(false)}
                            renderInput={(params) => (
                                <TextField
                                    sx={{
                                        fontSize: '14px',
                                        '& .MuiOutlinedInput-notchedOutline': {
                                            borderColor: '#D3EEFF !important',
                                            borderRadius: 0,
                                            height: '45px',
                                        },
                                    }}
                                    {...params}
                                    fullWidth
                                    size="small"
                                />
                            )}
                            inputFormat={useGlobalConfig.getState().getDateTimeFormat()}
                            value={value}
                            onChange={handleChange}
                            mask=""
                        />
                    ) : (
                        <DatePicker
                            open={open}
                            onOpen={() => setOpen(true)}
                            onClose={() => setOpen(false)}
                            renderInput={(params) => (
                                <TextField
                                    sx={{
                                        fontSize: '14px',
                                        '& .MuiOutlinedInput-notchedOutline': {
                                            borderColor: '#D3EEFF !important',
                                            borderRadius: 0,
                                            height: '45px',
                                        },
                                    }}
                                    {...params}
                                    fullWidth
                                    size="small"
                                />
                            )}
                            value={value}
                            onChange={handleChange}
                            mask=""
                            inputFormat={useGlobalConfig.getState().getDateFormat()}
                        />
                    )}
                </LocalizationProvider>
            </span>
        </MainTooltip>
    );
});

DatePickerEditor.displayName = 'DatePickerEditor';

export default DatePickerEditor;
