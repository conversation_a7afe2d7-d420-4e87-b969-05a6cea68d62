/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';

import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import { useSelector } from 'react-redux';
import { selectAppReducer } from '../../selectors';
import { CONTENT_ROLE_OPTIONS } from '../../constants/common';

/**
 * Wrapper of SelectEditor to filter out selected items
 */
export const RoleSelectEditor = forwardRef((props: any, ref) => {
    const {
        accesses,
        sessions: { isLoadingAccesses },
    } = useSelector(selectAppReducer);
    const {
        agent: { id: agentId },
        state: { name: stateName },
    } = props.data;

    const selectedRoles = accesses
        .filter((access) => access.agent.id === agentId && access.state.name === stateName)
        .map((access) => access.contentRole);

    const availableOptions = CONTENT_ROLE_OPTIONS.map(({ value, label }) => ({
        value: label,
        label: label,
        itemProps: {
            disabled: selectedRoles.some((role) => role.toLowerCase().includes(label.toLowerCase())),
        },
    }));
    return <SelectEditor disabled={isLoadingAccesses} {...props} options={availableOptions} ref={ref} />;
});

RoleSelectEditor.displayName = 'RoleSelectEditor';

/**
 * Generic select cell editor. To customize it, wrap it with a HOC component
 */
const SelectEditor = forwardRef((props: any, ref) => {
    const [value, setValue] = useState(props.value || '');
    const refInput = useRef(null);
    useEffect(() => {
        if (refInput.current) {
            refInput.current.focus();
        }
    }, [refInput]);

    useImperativeHandle(ref, () => {
        return {
            getValue() {
                return value;
            },
            isCancelBeforeStart() {
                return false;
            },
            isCancelAfterEnd() {
                return false;
            },
        };
    });

    const renderOptions = useCallback(() => {
        const { options } = props;

        return options.map((option, index) => (
            <MenuItem style={{ fontSize: '14px' }} key={index} {...option.itemProps} value={option.value}>
                {option.label}
            </MenuItem>
        ));
    }, [props.options]);

    return (
        <Select
            defaultOpen
            ref={refInput}
            size="small"
            sx={{
                fontSize: '14px',
                '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#D3EEFF !important',
                    borderRadius: 0,
                },
            }}
            fullWidth
            value={value}
            onBlur={() => props.api.stopEditing()}
            onChange={(e) => setValue(e.target.value)}
        >
            {renderOptions()}
        </Select>
    );
});

SelectEditor.displayName = 'SelectEditor';

export default SelectEditor;
