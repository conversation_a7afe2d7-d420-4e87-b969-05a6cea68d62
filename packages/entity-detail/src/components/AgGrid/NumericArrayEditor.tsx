/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { Attribute, AttributeType } from '@tripudiotech/api';
import { MultiValueInput } from '../AttributeDetail/ArrayAttribute';

export const NumericArrayEditor = forwardRef((props: any, ref) => {
    const [value, setValue] = useState(props.value || []);
    const { attribute }: { attribute: Attribute } = props;
    const { name, type, displayName, description, nullable } = attribute as Attribute;

    const refInput = useRef(null);
    useEffect(() => {
        if (refInput.current) {
            refInput.current.focus();
        }
    }, [refInput]);

    useImperativeHandle(ref, () => {
        return {
            getValue() {
                return value;
            },
            isCancelBeforeStart() {
                return false;
            },
            isCancelAfterEnd() {
                return false;
            },
        };
    });

    return (
        <MultiValueInput
            type={attribute.type as AttributeType.INTEGER_ARRAY | AttributeType.FLOAT_ARRAY}
            displayName={''}
            value={value}
            onChange={(newValue) => setValue(newValue)}
            helperText={description}
            required={!Boolean(nullable)}
            inputSx={{ backgroundColor: '#FFFFFF' }}
        />
    );
});

NumericArrayEditor.displayName = 'NumericArrayEditor';
